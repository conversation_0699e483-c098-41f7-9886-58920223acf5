/**
 * @file dreame_uds.h
 * <AUTHOR> (guo<PERSON><PERSON>@dreame.tech)
 * @brief 
 * @version 0.1
 * @date 2025-03-10
 * @copyright Copyright (c) {2025} 追觅科技有限公司版权所有
 */
#ifndef __DREAME_UDS_H__
#define __DREAME_UDS_H__

#include "UnixDomainSocket.h"
#include <unistd.h>
#include "jsonparse.h"
#include "circular_queue.h"
#include <thread>
#include <atomic>
#include <map>
#include <functional>

// 定义 Result 结构体
struct Result {
    int fd;
    std::string recv_str;
};

class DreameUds {
private:
    std::string uds_path;
    std::shared_ptr<UnixDomainSocket> dreame_uds_socket_;
    std::atomic<bool> uds_server_running;
    CircularQueue<Result>* uds_server_queue;
    std::map<std::string, std::function<void(int, Json::Value)>> method_map;

    /**
     * @brief uds 监听接收命令
     * @param buffer 
     * @param length 
     */
    void RecvUdsClientBuffer(const int fd, const uint8_t *buffer, const uint32_t length);


public:
    DreameUds();
    ~DreameUds();
    std::mutex g_mtx;             // 全局互斥锁.

    /**
     * @brief 初始化启动mqttproc_uds server服务
     * @param sock 
     * @param queue 
     * @return true 
     * @return false 
     */
    bool Init(std::string sock, CircularQueue<Result> &queue);

    /**
     * @brief 停止mqttproc_uds server运行
     */
    void Stop();

    /**
     * @brief 对发送的数据都统一加上\x03的数据
     * @param fd 
     * @param data 
     */
    void SendData(const int fd, std::string data);

    /**
     * @brief 获取队列指针
     * @return CircularQueue<Result>* 
     */
    CircularQueue<Result>* getQueue();

    /**
     * @brief 获取当前连接客户端的fd
     * @return int 
     */
    int getClientSockFd();

};

#endif