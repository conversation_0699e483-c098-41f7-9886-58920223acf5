/**
 * @file ErrorCodeMap.h
 * <AUTHOR> (g<PERSON><PERSON><PERSON>@dreame.tech)
 * @brief 错误码信息对照映射map
 * @version 0.1
 * @date 2025-04-27
 * @copyright Copyright (c) {2025} 追觅科技有限公司版权所有
 */

#ifndef ERRORCODEMAP_H
#define ERRORCODEMAP_H

#include <string>
#include <unordered_map>

namespace ErrorCodeMap {

    enum class ErrorCode {
        Success = 0,
        ClientError = 500,
        FileNotExist = 501,
        FileDirNotExist = 502,
        FileTypeNotSupport = 503,
        KlipperCommandError = 504,
        CommandTimeout = 505,
        StartStreamError = 506,
        StopStreamError = 507,
        // 扩展其他错误码...

        // 600 ~ 650 打印相关错误码
        PrintError = 600,
        PrintIdNotMatch = 601,
        PrintCurStatusNotSupport = 602,
        PrintQueueEmpty = 603,
        PrintTaskIdNotMatch = 604,
        
    };
    
    struct ErrorInfo {
        int code;
        std::string message;
    };
    
    // 错误码与消息的映射表
    inline const std::unordered_map<ErrorCode, ErrorInfo> ErrorInfoMap = {
        {ErrorCode::Success,        {200,   "Success"}},
        {ErrorCode::ClientError,    {500, "Unknown exception encountered during command execution"}},
        {ErrorCode::FileNotExist,   {501, "file not exist"}},
        {ErrorCode::FileDirNotExist,   {502, "file directory not exist"}},
        {ErrorCode::FileTypeNotSupport,   {503, "file type not support"}},
        {ErrorCode::KlipperCommandError,   {504, "klipper command error"}},
        {ErrorCode::CommandTimeout,   {505, "command timeout"}},
        {ErrorCode::StartStreamError,   {506, "start stream error"}},
        {ErrorCode::StopStreamError,   {507, "stop stream error"}},
        // 添加其他错误码...

        // 600 ~ 650 打印相关错误码
        {ErrorCode::PrintError,   {600, "print error"}},
        {ErrorCode::PrintIdNotMatch,   {601, "print id not match"}},
        {ErrorCode::PrintCurStatusNotSupport,   {602, "The current status does not support this operation"}},
        {ErrorCode::PrintQueueEmpty,   {603, "print queue is empty"}},
        {ErrorCode::PrintTaskIdNotMatch,   {604, "print task id not match"}},

    };
    
    // 获取错误信息（返回常量引用避免拷贝）
    inline const ErrorInfo& GetErrorInfo(ErrorCode code) {
        static const ErrorInfo unknown{-1, "Unknown error"};
        auto it = ErrorInfoMap.find(code);
        return (it != ErrorInfoMap.end()) ? it->second : unknown;
    }
    
} // namespace ErrorCodeMap

#endif