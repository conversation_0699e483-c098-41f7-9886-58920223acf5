/**
 * @file video_stream.h
 * <AUTHOR> (h<PERSON><PERSON><PERSON><PERSON>@dreame.tech)
 * @brief 视频流控制
 * @version 0.1
 * @date 2024-12-11
 * @copyright Copyright (c) {2024} 追觅科技有限公司版权所有
 */
#ifndef __VIDEO_STREAM_H__
#define __VIDEO_STREAM_H__

#include <iostream>
#include "jsonparse.h"
#include "dreame_3d_mqtt.h"
#include "UnixDomainSocket.h"
#include "dreame_uds.h"
#include "dreame_sub.h"
#include "ErrorCodeMap.h"

// 前置声明 避免相互引用引起的编译失败
class DreameSub;

class VideoStream
{
private:
    const std::string src = "mqtt_proc";
    const std::string des = "cam_proc";
public:
    VideoStream();
    ~VideoStream();

    /**
     * @brief 命令分发透传
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    void CommandDispatch(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 开启视频流
     * @param url rtmp地址
     * @return true 
     * @return false 
     */
    bool StartStream(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 停止推流
     * @return true 
     * @return false 
     */
    bool StopStream(DreameSub* dreame_sub_ptr, Json::Value req_json);

};

#endif
