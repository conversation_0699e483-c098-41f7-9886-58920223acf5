/**
 * @file dreame_3d_mqtt.h
 * <AUTHOR> (h<PERSON><PERSON><PERSON><PERSON>@dreame.tech)
 * @brief
 * @version 0.1
 * @date 2024-12-09
 * @copyright Copyright (c) {2024} 追觅科技有限公司版权所有
 */
#ifndef __DREAME_MQTT_CLINET_H__
#define __DREAME_MQTT_CLINET_H__

#include <mqtt/async_client.h>
#include <condition_variable>
#include <ctime>
#include <functional>
#include <memory>
#include <vector>
#include <mutex>
#include <stdint.h>
#include <string>
#include <sys/time.h>
#include "config.h"
#include <atomic>

const int QOS0 = 0;
const int QOS1 = 1;
const int QOS2 = 2;
const auto PUBLISH_TIMEOUT = std::chrono::seconds(10);
const int TASK_SHORT_TIMEOUT = 10;
const int TASK_TIMEOUT = 60;
const int TASK_LONG_TIMEOUT = 180;
const int TASK_VERY_LONG_TIMEOUT = 300;

class Dreame3dMqtt
{
private:
    const std::string CLIENTID = "FD2G007E";
    const uint64_t timeout_    = 10000L;
    std::string topic_;
    std::string thing_topic_up;
    std::string property_up_topic;           // 属性上报topic
    std::string property_set_response_topic; // 设置属性回复ack topic
    std::string event_up_topic;             // 事件上报topic
    std::string service_response_topic;     // 设备回复服务端ack(可携带响应内容)topic
    std::string progress_up_topic;          // 打印进度上报topic
    std::map<std::string, std::string> method_topic_map;
    std::string project_name_; // 项目名称
    std::string model_id_; // 目标型号
    std::string device_id_; // 目标ID
    std::string ip_addr_;
    int port_;
    std::shared_ptr<mqtt::async_client> mqtt_client_;
    std::condition_variable g_cv; // 全局条件变量
    std::mutex g_mtx;             // 全局互斥锁.
    std::mutex state_g_mtx;             // 全局互斥锁.
    std::vector<std::string> msg_list_;
    std::vector<std::string> state_msg_list_;

    /**
     * @brief 获取client id
     * @return std::string
     */
    std::string GetClientId();

    /**
     * @brief 话题消息分发回调
     * @param msg 
     */
    static void messageArrived(mqtt::const_message_ptr &msg);

public:
    Dreame3dMqtt(std::string addr = "************", int port = 31051);
    ~Dreame3dMqtt();

    std::vector<std::string> mainproc_msg_list_;
    std::mutex msg_g_mtx;
    std::vector<std::string> mainproc_heartbeat_msg_list;
    std::mutex heartbeat_msg_g_mtx;
    void SetTopicValue();

    /**
     * @brief 是否需要全量上报一次数据状态
     */
    std::atomic<bool> report_state;

    /**
     * @brief 初始化接口
     * @return true 初始化成功
     * @return false 初始化失败
     */
    bool Init();

    /**
     * @brief 进行MQTT SSL/TLS认证连接服务器
     * @return true 
     * @return false 
     */
    bool SSLInit();

    /**
     * @brief 阻塞循环, 用于消息发布到mqtt服务端和mqtt断连时重新订阅topic
     */
    void Loop();

    /**
     * @brief 消息发布接口
     * @param topicName 话题名
     * @param payload 内容
     * @param len 长度
     * @param qos QOS等级
     */
    int PublishMsg(const char* topicName, const uint8_t* payload, const int len, const int qos = QOS0);

    /**
     * @brief 回调注册
     * @param handler
     */
    void AddCallback(std::string &topic, const int qos, std::function<void(mqtt::const_message_ptr &)> handler);

    /**
     * @brief 移除订阅话题
     * @param topic 
     */
    void DelTopic(std::string topic);

    /**
     * @brief 将要上报的ack消息放入msg_list_动态数组容器, 由Loop函数发布上传
     * @param msg 
     * @return int 
     */
    int UploadMsg(std::string msg);

    /**
     * @brief 将要上报的状态消息放入state_msg_list_动态数组容器, 由Loop函数发布上传
     * @param msg 
     * @return int 
     */
    int UploadStateMsg(std::string msg);

    /**
     * @brief 将要上报的心跳包消息放入mainproc_heartbeat_msg_list_动态数组容器, 心跳数据做单独处理
     * @param msg 
     * @return int 
     */
    int UploadHeartbeatMsg(std::string msg);

    /**
     * @brief 将要上报的ack消息放入mainproc_msg_list_动态数组容器, 由Loop函数发布上传
     * @param msg 
     * @return int 
     */
    int UploadTopicMsg(std::string msg);

    /**
     * @brief 根据method的开头和结尾来匹配要上报到的topic
     * @param method 
     * @return std::string 
     */
    std::string GetCurUploadTopic(std::string method);

    std::string GetModelId();
    std::string GetDeviceId();
    std::string GetProjectName();

    /**
     * @brief 收到mqtt消息时的第一步回调处理入口
     */
    class Callback : public mqtt::callback {
        public:
            Callback(Dreame3dMqtt *parent) : parent_(parent) {}
            void message_arrived(mqtt::const_message_ptr msg) override {
                if (parent_) {
                    parent_->messageArrived(msg);
                }
            }
        private:
            Dreame3dMqtt *parent_;
        };
        std::unique_ptr<Callback> callback_;
    
    void CheckOtaUpgradeResult();

};

#endif
