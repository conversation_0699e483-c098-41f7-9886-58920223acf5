/**
 * @file color_box_handler.h
 * <AUTHOR> (g<PERSON><PERSON><PERSON>@dreame.tech)
 * @brief 
 * @version 0.1
 * @date 2025-04-10
 * @copyright Copyright (c) {2025} 追觅科技有限公司版权所有
 */

#ifndef COLOR_BOX_HANDLER_H
#define COLOR_BOX_HANDLER_H

#include <iostream>
#include "jsonparse.h"
#include "dreame_3d_mqtt.h"
#include "UnixDomainSocket.h"
#include "dreame_uds.h"
#include "dreame_sub.h"
#include "logger.h"

// 前置声明 避免相互引用引起的编译失败
class DreameSub;

class ColorBoxHandler{
private:

public:

    ColorBoxHandler();
    ~ColorBoxHandler();

    /**
     * @brief 获取当前多色盒子料槽耗材
     * @param req_json 
     */
    void ColorBoxLoadedSlot(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 料盒信息查询
     * @param req_json 
     */
    void ColorBoxInfoGet(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 烘干指令下发
     * @param req_json 
     */
    void ColorBoxDryerSet(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 进退料指令下发
     * @param req_json 
     */
    void ColorBoxFilFeedSet(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 自动续料指令下发
     * @param req_json 
     */
    void ColorBoxFilAutoFeedSet(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 料槽刷新
     * @param req_json 
     */
    void ColorBoxSlotRefreshSet(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 设置单个料槽信息
     * @param req_json 
     */
    void ColorBoxSlotInfoSet(DreameSub* dreame_sub_ptr, Json::Value req_json);


};


#endif