/**
 * @file extFil_box_handler.h
 * <AUTHOR> (g<PERSON><PERSON><PERSON>@dreame.tech)
 * @brief 
 * @version 0.1
 * @date 2025-04-10
 * @copyright Copyright (c) {2025} 追觅科技有限公司版权所有
 */

#ifndef EXTFIL_BOX_HANDLER_H
#define EXTFIL_BOX_HANDLER_H

#include <iostream>
#include "jsonparse.h"
#include "dreame_3d_mqtt.h"
#include "UnixDomainSocket.h"
#include "dreame_uds.h"
#include "dreame_sub.h"
#include "logger.h"

// 前置声明 避免相互引用引起的编译失败
class DreameSub;

class ExtFilBoxHandler{
private:

public:

    ExtFilBoxHandler();
    ~ExtFilBoxHandler();

    /**
     * @brief 外置料架设置耗材信息
     * @param req_json 
     */
    void ExtFilBoxFilSet(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 查询外置料架信息
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    void ExtFilBoxFilGet(DreameSub* dreame_sub_ptr, Json::Value req_json);

    void UploadBoxFilInfo(DreameSub* dreame_sub_ptr);

};
#endif