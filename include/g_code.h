/**
 * @file g_code.h
 * <AUTHOR> (h<PERSON><PERSON><PERSON><PERSON>@dreame.tech)
 * @brief G-code处理
 * @version 0.1
 * @date 2024-12-11
 * @copyright Copyright (c) {2024} 追觅科技有限公司版权所有
 */
#ifndef __G_CODE_H__
#define __G_CODE_H__

#include <iostream>
#include "jsonparse.h"
#include "dreame_3d_mqtt.h"
#include "UnixDomainSocket.h"
#include "dreame_uds.h"
#include "dreame_sub.h"
#include "utils.h"
#include "config.h"
#include <atomic>
#include "ErrorCodeMap.h"

// 前置声明 避免相互引用引起的编译失败
class DreameSub;

class Gcode
{
private:
    // std::string url_;
public:
    Gcode();
    ~Gcode();

    std::string url_;
    std::atomic<bool> download_cancel_{false};
    std::atomic<float> download_progress_{0.0f};
    FILE* wget_pipe_{nullptr};  // 用于与wget进程通信

    /**
     * @brief 下载云端G-code文件
     * @param url 远程地址
     * @return true 下载成功
     * @return false 下载失败
     */
    bool GcodeDownload(const std::string& url, const std::string& local_path);

    /**
     * @brief 取消当前下载
     */
    void CancelDownload();

    /**
     * @brief 获取当前下载进度
     * @return int 0-100的进度值
     */
    int GetDownloadProgress() const;

     /**
      * @brief 获取终端gcode文件目录
      * @param req_json 
      */
     void FileSdList(DreameSub* dreame_sub_ptr, Json::Value req_json);

     /**
      * @brief 获取终端gcode文件的详细信息
      * @param req_json 
      */
     void FileInfo(DreameSub* dreame_sub_ptr, Json::Value req_json);

     /**
      * @brief 
      */
    void FilePlateInfo(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 删除指定的文件 通过MQTT/UDS
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    void FileDelete(DreameSub* dreame_sub_ptr, Json::Value req_json);
};

#endif
