/**
 * @file dreme_sub.h
 * <AUTHOR> (h<PERSON><PERSON>qua<PERSON>@dreame.tech)
 * @brief 订阅管理
 * @version 0.1
 * @date 2024-12-11
 * @copyright Copyright (c) {2024} 追觅科技有限公司版权所有
 */
#ifndef __DREAME_SUB_H__
#define __DREAME_SUB_H__

#include "dreame_3d_mqtt.h"
#include "UnixDomainSocket.h"
#include <condition_variable>
#include <mutex>
#include <thread>
#include <atomic>
#include "circular_queue.h"
#include <map>
#include <functional>
#include "jsonparse.h"
#include "klipper_handler.h"
#include "dreame_uds.h"
#include "ota_handler.h"
#include "g_code.h"
#include "video_stream.h"
#include "print_task_handler.h"
#include "color_box_handler.h"
#include "extFil_box_handler.h"

// 前置声明 避免相互引用引起的编译失败
class KlipperHandler;
class KlipperInfo;
class OtaHandler;
struct OtaInfo;
class Gcode;
class VideoStream;
class PrintTaskHandler;
class ColorBoxHandler;
class ExtFilBoxHandler;

class DreameSub {
private:
    std::string topic_;
    std::shared_ptr<Dreame3dMqtt> dreame3d_mqtt_ptr_;
    std::shared_ptr<DreameUds> dreame_uds_ptr_;
    std::condition_variable g_cv_; // 全局条件变量
    std::mutex g_mtx_;             // 全局互斥锁.
    std::thread recvMqttMsgQueueThread;
    CircularQueue<std::string>* mqtt_recv_queue;
    std::map<std::string, std::function<void(DreameSub*, Json::Value)>> method_map_func_;
    CircularQueue<Result>* uds_server_queue;
    // 处理来自mainproc server发送过来的数据
    std::thread handleRecvMainprocMsgQueueThread;
    std::thread handleHeartbeatMsgThread;

    /**
     * @brief 处理MQTT订阅topic的队列中的数据

     */
    void processRecvMqttMsgQueue();

    /**
     * @brief 处理来自mainproc发送过来放在队列中的数据

     */
    void processRecvMainprocMsgQueue();

    void processHandleHeartbeatMsg();

    /**
     * @brief 话题回调
     * @param msg 
     */
    void SubMsgArrived(mqtt::const_message_ptr &msg);

public:
    DreameSub(std::shared_ptr<Dreame3dMqtt> dreame3d_mqtt, std::shared_ptr<DreameUds> dreame_uds);
    ~DreameSub();

    /**
     * @brief 是否正在处理打印中
     */
    std::atomic<bool> processing_printing;

    /**
     * @brief 云端绑定的用户信息
     */
    std::string user_id_;
    std::string username_;

    /**
     * @brief 机箱灯亮度
     */
    int light_chassis;

    /**
     * @brief task_id 任务池
     */
    std::vector<Json::Value> task_id_list;

    /**
     * @brief 任务池的互斥锁
     */
    std::mutex task_id_g_mtx;

    /**
     * @brief 将任务ID加入任务池中
     * @param task_id 任务ID
     * @param method  任务方法
     * @param timeout 指定多少秒后超时
     * @return int 任务池大小
     */
    int AddTaskID(std::string task_id, std::string method, int timeout);

    /**
     * @brief 将任务ID从任务池中删除
     * @param task_id 任务ID
     * @return int 任务池大小
     */
    int DeleteTaskID(std::string task_id);

    /**
     * @brief 检测任务池中是否有超时的任务, 有则删除
     * @return Json::Value 返回删除的任务信息
     */
    Json::Value DeleteTimeoutTaskID();

    /**
     * @brief 获取任务池中指定任务ID的信息
     * @param task_id 任务ID
     * @return Json::Value 返回任务信息
     */
    Json::Value GetTaskIDInfo(std::string task_id);

    /**
     * @brief 打印任务队列
     */
    std::vector<Json::Value> print_task_queue_list;

    /**
     * @brief 打印任务队列的互斥锁
     */
    std::mutex print_task_queue_g_mtx;

    /**
     * @brief 将打印任务加入打印任务队列中
     * @param print_task  打印任务
     * @return int 打印任务队列大小
     */
    int AddPrintTask(Json::Value print_task);

    /**
     * @brief 将打印任务从打印任务队列中删除
     * @param print_task  打印任务
     * @return int 打印任务队列大小
     */
    bool DeletePrintTask(Json::Value print_task);

    /**
     * @brief 取出打印任务队列中的第一个任务
     * @return Json::Value 返回打印任务
     */
    Json::Value GetFirstPrintTask();

    /**
     * @brief 调整任务队列顺序
     * @param req_json 
     * @return true 
     * @return false 
     */
    bool AdjustPrintTaskQueue(Json::Value req_json);

    void SubTopic(std::string topic);

    bool Init(CircularQueue<std::string> &queue);

    /**
     * @brief 获取mqtt对象指针,给程序其他地方获取使用其上报接口
     * @return std::shared_ptr<Dreame3dMqtt>* 
     */
    std::shared_ptr<Dreame3dMqtt>* getMqttPtr();

    /**
     * @brief 获取mqttproc server对象指针, 给程序其他地方直接发消息到mainproc使用
     * @return std::shared_ptr<DreameUds>* 
     */
    std::shared_ptr<DreameUds>* getDreameUdsPtr();

    std::shared_ptr<KlipperHandler> klipper_handler_;
    std::shared_ptr<KlipperInfo> klipper_info_;

    std::shared_ptr<OtaHandler> ota_handler_;
    std::shared_ptr<OtaInfo> ota_info;

    std::shared_ptr<Gcode> gcode_handler_;

    std::shared_ptr<VideoStream> video_stream_handler_;

    std::shared_ptr<PrintTaskHandler> print_task_handler_;

    std::shared_ptr<ColorBoxHandler> color_box_handler_;

    std::shared_ptr<ExtFilBoxHandler> extFil_box_handler_;

    /**
     * @brief Ota信息初始化
     * @param  
     */
    void OtaInfoInit();

    /**
     * @brief klipper信息初始化
     * @param  
     */
    void KlpperInfoInit();

    /**
     * @brief 供app端、切片软件端来握手检查设备是否在线
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    void DeviceCheck(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief dreame用户账号名称绑定信息设置
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    void AccountBindSet(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 格式化透传发送到klipper的json数据
     * @param req_json 
     * @return std::string 
     */
    std::string KlipperDataJsonParse(Json::Value req_json);

    /**
     * @brief 设置mqtt心跳包时间间隔
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    void SetMachineInfo(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 格式化透传发到cam_proc的json数据
     */
    std::string VideoDataJsonParse(Json::Value req_json);

    /**
     * @brief 处理来自klippy_proc的消息
     * @param req_json 
     */
    bool HandleFromKlippyProcMsg(Json::Value req_json);

    /**
     * @brief 处理来自cam_proc的消息
     * @param req_json 
     */
    bool HandleFromCamProcMsg(Json::Value req_json);

    /**
     * @brief 处理来自ota_proc的消息
     * @param req_json 
     */
    bool HandleFromOtaProcMsg(Json::Value req_json);

    /**
     * @brief 处理来自gui_proc的消息
     * @param req_json 
     */
    bool HandleFromGuiProcMsg(Json::Value recv_json);

    /**
     * @brief 处理来自acf_box_proc的消息
     * @param req_json 
     */
    bool HandleFromAcfBoxProcMsg(Json::Value req_json);

    /**
     * @brief 向服务端获取绑定用户的信息
     * @param dreame_sub_ptr 
     */
    void GetUserBindInfo(DreameSub* dreame_sub_ptr);

    /**
     * @brief 解绑用户
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    void UnbindUser();

    /**
     * @brief 检查机箱灯亮度值
     * @return true 
     * @return false 
     */
    void CheckLightChassis();

    /**
     * @brief 获取机箱灯亮度值
     * @return int 
     */
    void GetLightChassis(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 设置机箱灯亮度值
     * @param light_chassis 
     */
    void SetLightChassis(DreameSub* dreame_sub_ptr, Json::Value req_json);

};

#endif
