/**
 * @file config.h
 * <AUTHOR> (g<PERSON><PERSON><PERSON>@dreame.tech)
 * @brief 
 * @version 0.1
 * @date 2025-03-10
 * @copyright Copyright (c) {2025} 追觅科技有限公司版权所有
 */

#ifndef GLOBAL_CONFIG_H
#define GLOBAL_CONFIG_H

#include "jsonparse.h"
#include "utils.h"

// 声明一个全局的 Json::Value 对象
extern Json::Value globalConfigObject;

// 初始化配置的函数
bool LoadConfig(const std::string &file_path);

// 设置心跳周期
bool SetMachineKeepAlivePeriod(int period);

// 使用默认值初始化
void ConfigDefaultInit();

#endif