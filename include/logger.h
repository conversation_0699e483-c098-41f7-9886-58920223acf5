/**
 * @file logger.h
 * <AUTHOR> (g<PERSON><PERSON><PERSON>@dreame.tech)
 * @brief 
 * @version 0.1
 * @date 2025-03-07
 * @copyright Copyright (c) {2025} 追觅科技有限公司版权所有
 */
#ifndef LOGGING_H
#define LOGGING_H

#include <memory>
#include <spdlog/spdlog.h>
#include <spdlog/sinks/rotating_file_sink.h>

class Logger {
    public:
        // 初始化日志系统
        static void init();
    
        // 获取日志实例
        static std::shared_ptr<spdlog::logger> get();
    
    private:
        // 静态成员变量，存储logger实例
        static std::shared_ptr<spdlog::logger> logger_;
};
// 日志宏定义
#define LOG_TRACE(...)    SPDLOG_LOGGER_TRACE(Logger::get(), __VA_ARGS__)
#define LOG_DEBUG(...)    SPDLOG_LOGGER_DEBUG(Logger::get(), __VA_ARGS__)
#define LOG_INFO(...)     SPDLOG_LOGGER_INFO(Logger::get(), __VA_ARGS__)
#define LOG_WARN(...)     SPDLOG_LOGGER_WARN(Logger::get(), __VA_ARGS__)
#define LOG_ERROR(...)    SPDLOG_LOGGER_ERROR(Logger::get(), __VA_ARGS__)
#define LOG_CRITICAL(...) SPDLOG_LOGGER_CRITICAL(Logger::get(), __VA_ARGS__)

#endif
