/**
 * @file ota_handler.h
 * <AUTHOR> (g<PERSON><PERSON><PERSON>@dreame.tech)
 * @brief 
 * @version 0.1
 * @date 2025-03-27
 * @copyright Copyright (c) {2025} 追觅科技有限公司版权所有
 */

 #ifndef __OTA_HANDLER_H__
 #define __OTA_HANDLER_H__
 
 #include <iostream>
 #include "jsonparse.h"
 #include "dreame_3d_mqtt.h"
 #include "UnixDomainSocket.h"
 #include "dreame_uds.h"
 #include "dreame_sub.h"
 
// 前置声明 避免相互引用引起的编译失败
class DreameSub;

 struct OtaInfo {
     std::string printer_ver;
     std::string printer_url;
     std::string printer_md5;
     std::string acf_box_ver;
     std::string acf_box_url;
     std::string acf_box_md5;
     int status;
     int progress;
     Json::Value latest_ota_version;
     Json::Value current_ota_version;
 };
 
 
 class OtaHandler
 {
 private:
 
 public:
 
     O<PERSON><PERSON><PERSON><PERSON>();
     ~O<PERSON>Hand<PERSON>();

    /**
     * @brief 接收ota_proc进程推送过来的当前终端版本信息
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    void HandleOtaVersionFromOtaProc(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 向ota_proc进程获取当前终端版本信息
     * @param dreame_sub_ptr 
     */
    void GetClientCurrentOtaVersion(DreameSub* dreame_sub_ptr);

    /**
     * @brief 终端主动向服务器获取当前最新升级包信息
     * @param dreame_sub_ptr 
     */
    void OtaVersionFetch(DreameSub* dreame_sub_ptr);
 
     /**
      * @brief 终端上报当前版本信息到服务端
      * @param req_json 
      */
     void OtaVersionUpload(DreameSub* dreame_sub_ptr);

    /**
     * @brief 服务端下发最新升级包信息 将最新信息存储到 latest_ota_version
     * @param dreame_sub_ptr 
     * @param req_json 
     */
     void OtaVersionSet(DreameSub* dreame_sub_ptr, Json::Value req_json);
 
     /**
      * @brief 服务端查询终端版本信息
      * @param req_json 
      */
     void OtaVersionGet(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
      * @brief 服务端下发升级指令
      * @param req_json 
      */
     void OtaUpgradeStart(DreameSub* dreame_sub_ptr, Json::Value req_json);
 
     /**
      * @brief 服务端下发取消升级指令
      * @param req_json 
      */
     void OtaUpgradeCancel(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 获取最新版本号并返回给ota_proc进程
     * @param req_json 
     */
    void GetNewVer(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 更新升级状态和进度
     * @param req_json 
     */
    void SetOtaUpgradeProgress(DreameSub* dreame_sub_ptr, Json::Value req_json);

 };
 
 
 #endif