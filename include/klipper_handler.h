/**
 * @file klipper_handler.h
 * <AUTHOR> (g<PERSON><PERSON><PERSON>@dreame.tech)
 * @brief 
 * @version 0.1
 * @date 2025-03-26
 * @copyright Copyright (c) {2025} 追觅科技有限公司版权所有
 */

#ifndef __KLIPPER_INFO_H__
#define __KLIPPER_INFO_H__

#include <iostream>
#include "jsonparse.h"
#include "dreame_3d_mqtt.h"
#include "UnixDomainSocket.h"
#include "dreame_uds.h"
#include "dreame_sub.h"
 
// 前置声明 避免相互引用引起的编译失败
class DreameSub;

struct KlipperInfo {
    int machine_state;
    std::string klipper_state;
    int64_t update_time;
    std::string state;
    bool machine_is_homed;  // 归位状态
};

class KlipperHandler
{
private:
    std::string url_;
public:
    KlipperHandler();
    ~<PERSON>lipperHand<PERSON>();

    /**
     * @brief 立即上报一次打印机各项状态数据
     * @param dreame_sub_ptr 
     */
    void UploadStatusNow(DreameSub* dreame_sub_ptr);

    /**
     * @brief 服务端下发gcode指令
     * @param state 
     */
    void ScriptSet(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 命令分发, 根据不同的method做不同的处理
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    void CommandDispatch(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 上报机器klipper状态
     * @param dreame_sub_ptr 
     */
    void UploadMachineState(DreameSub* dreame_sub_ptr);

    /**
     * @brief 定时3s向klipper进程查询消息
     * @param dreame_sub_ptr 
     */
    void GetKlipperInfo(DreameSub* dreame_sub_ptr);

    /**
     * @brief 处理来自klipper上报的错误码
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    void HandleClientCode(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 生成最终错误码
     * @param req_json 
     * @return std::string 
     */
    std::string HandleErrorCodeCategory (Json::Value req_json);

};

#endif