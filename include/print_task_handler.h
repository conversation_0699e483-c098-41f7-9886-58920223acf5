/**
 * @file print_task_handler.h
 * <AUTHOR> (g<PERSON><PERSON><PERSON>@dreame.tech)
 * @brief 
 * @version 0.1
 * @date 2025-04-03
 * @copyright Copyright (c) {2025} 追觅科技有限公司版权所有
 */

#ifndef __PRINT_TASK_HANDLER_H__
#define __PRINT_TASK_HANDLER_H__

#include <iostream>
#include "jsonparse.h"
#include "dreame_3d_mqtt.h"
#include "UnixDomainSocket.h"
#include "dreame_uds.h"
#include "dreame_sub.h"
#include "logger.h"
#include "ErrorCodeMap.h"

// 前置声明 避免相互引用引起的编译失败
class DreameSub;

struct PrintTaskInfo {
    std::string printId;// 当前任务ID
    std::string filename;// 当前打印的文件名
    int currentLayer;   // 当前打印层
    int totalLayer;     // 总层数
    int printProgress;  // 打印进度
    int printDuration;  // 已打印时间
    int remainTime;     // 剩余打印时间
    int suppliesUsage;  // 材料使用量

};

enum MachineState {
    MACHINE_FREE = 1,         // 空闲中
    MACHINE_CALIBRATION = 2,  // 校准中
    MACHINE_PRINTING = 3,     // 打印中
    MACHINE_ERROR = 4,        // 故障中
    MACHINE_UPGRADE = 5      //  升级中
};

class PrintTaskHandler
{
private:
    const std::string src = "mqtt_proc";
    const std::string des = "klippy_proc";
public:
    PrintTaskHandler();
    ~PrintTaskHandler();

    std::shared_ptr<PrintTaskInfo> print_task_info;

    /**
     * @brief 下发打印任务
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    void PrintTaskStart(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 上报当前gcode下载进度
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    void UploadGcodeDownloadProgress(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief  gcode文件校验
     * @param dreame_sub_ptr 
     * @param req_json 
     * @param download_status 
     * @param filemd5 
     * @param local_path 
     * @return true 校验成功
     * @return false 校验失败
     */
    bool FileMd5Check(DreameSub* dreame_sub_ptr, Json::Value req_json, bool download_status, const std::string& filemd5, const std::string& local_path);

    /**
     * @brief 暂停打印
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    void PrintTaskPause(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 恢复打印
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    void PrintTaskResume(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 停止打印
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    void PrintTaskStop(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 处理下载gcode的过程
     * @param local_path 
     */
    void HandleDownloadGcode(DreameSub* dreame_sub_ptr, Json::Value req_json, bool& md5_status, bool& is_3mf_file);

    /**
     * @brief 根据file_key的参数生成获取下载链接的URL
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    Json::Value GetDownloadUrl(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 取出打印队列中的第一条数据, 然后进行打印
     * @param dreame_sub_ptr 
     */
    void HandlePrintQueue(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 获取打印队列中的所有数据
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    void GetPrintTaskQueueList(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 获取队列中指定打印任务id的任务详情参数
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    void GetPrintTaskInfoDetail(DreameSub* dreame_sub_ptr, Json::Value req_json);

    // 删除队列中指定task_id的任务
    void DeletePrintTaskQueueTargetId(DreameSub* dreame_sub_ptr, Json::Value req_json);

    /**
     * @brief 打印任务队列顺序调整
     * @param dreame_sub_ptr 
     * @param req_json 
     */
    void PrintTaskQueueAdjust(DreameSub* dreame_sub_ptr, Json::Value req_json);

};
#endif