{"mqtt_ssl": true, "mqtt_addr": "120.24.161.231", "mqtt_port": 8883, "machine_keep_alive_period": 20, "server_url": "http://47.107.130.160:85", "mqttproc_config_file_path": "/userdata/mqttproc/etc/config/mqtt_proc.json", "server_ca_cert_file": "/userdata/printer_data/certs/ca.pem", "client_cert_file": "/userdata/printer_data/certs/client.pem", "client_key_file": "/userdata/printer_data/certs/client.key", "mqttproc_uds": "/userdata/printer_data/comms/mqtt_proc.sock", "gcode_file_dir": "/userdata/printer_data/gcodes", "3mf_dir": "/userdata/printer_data/3mf_dir", "upgradeflag_path": "/userdata/upgradeflag", "brightness_path": "/sys/class/leds/line/brightness", "device_id": "", "device_cn": "", "log": {"log_path": "/userdata/printer_data/logs/mqtt_proc.log", "max_size": 20, "max_files": 3, "level": "debug"}}