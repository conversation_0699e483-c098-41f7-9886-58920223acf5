Header Only Version
==================================================================
Just copy the files to your build tree and use a C++11 compiler.  
Or use CMake:
``` 
  add_executable(example_header_only example.cpp)
  target_link_libraries(example_header_only spdlog::spdlog_header_only)
```

Compiled Library Version
==================================================================
CMake:
```  
  add_executable(example example.cpp)
  target_link_libraries(example spdlog::spdlog)
```

Or copy files src/*.cpp to your build tree and pass the -DSPDLOG_COMPILED_LIB to the compiler.

Important Information for Compilation:
==================================================================
* If you encounter compilation errors with gcc 4.8.x, please note that gcc 4.8.x does not fully support C++11. In such cases, consider upgrading your compiler or using a different version that fully supports C++11 standards

Tested on:  
gcc 4.8.1 and above
clang 3.5
Visual Studio 2013