<!--****************************************************************************
  Copyright (c) 2012, 2020 IBM Corp.

  All rights reserved. This program and the accompanying materials
  are made available under the terms of the Eclipse Public License v2.0
  and Eclipse Distribution License v1.0 which accompany this distribution.

  The Eclipse Public License is available at
     https://www.eclipse.org/legal/epl-2.0/
  and the Eclipse Distribution License is available at
    http://www.eclipse.org/org/documents/edl-v10.php.

  Contributors:
     <PERSON> - initial API and implementation and/or initial documentation
*******************************************************************************-->

<project name="MQTT C Client" default="full">

  <taskdef resource="net/sf/antcontrib/antlib.xml">
    <classpath>
      <pathelement location="/opt/public/cbi/build/3rdPartyJars/ant-contrib.jar" />
      <pathelement location="/usr/share/java/ant-contrib.jar" />
    </classpath>
  </taskdef>

  <property name="output.folder" value="build/output" />
  <loadfile property="version.major" srcFile="version.major"/>
  <loadfile property="version.minor" srcFile="version.minor"/>
  <loadfile property="version.patch" srcFile="version.patch"/>
  <property name="release.version" value="${version.major}.${version.minor}.${version.patch}" />

  <property name="libname" value="mqttv3c" />
  <property name="libname.ssl" value="mqttv3cs" />
  <property name="libname.async" value="mqttv3a" />
  <property name="libname.async.ssl" value="mqttv3as" />
  <property name="ssl" value="yes" />
  <property name="windows.openssl.folder" value="c:\openssl\bin" />
  <property name="test.hostname" value="iot.eclipse.org"/>
  <property name="test.port" value="1883"/>
  <property name="proxy.port" value="18883"/>
  <if>
    <os family="windows"/>
    <then>
      <property name="os.family" value="windows" />
    </then>
    <else>
      <if>
      <os family="mac"/>
      <then>
        <property name="os.family" value="mac" />
      </then>
      <else>
        <property name="os.family" value="unix" />
      </else>
      </if>
    </else>
  </if>
  <echo message="os.family is '${os.family}'" />

  <target name="init">
    <tstamp>
      <format property="buildTimestamp" pattern="yyyyMMddHHmm" />
    </tstamp>

    <fileset id="sync.source.fileset" dir="src">
      <include name="*.c"/>
      <exclude name="MQTTAsync.c"/>
      <exclude name="MQTTVersion.c"/>
    </fileset>
    <pathconvert refid="sync.source.fileset" property="sync.source.files" pathsep=" "/>

    <fileset id="async.source.fileset" dir="src">
      <include name="*.c"/>
      <exclude name="MQTTClient.c"/>
      <exclude name="MQTTVersion.c"/>
    </fileset>
    <pathconvert refid="async.source.fileset" property="async.source.files" pathsep=" "/>

  </target>

  <target name="version" depends="init" description="replace tags with the right levels">
    <property name="build.level" value="${DSTAMP}${TSTAMP}" />
    <copy file="src/VersionInfo.h.in" tofile="src/VersionInfo.h" overwrite="true"/>
    <replace file="src/VersionInfo.h" token="@BUILD_TIMESTAMP@" value="${build.level}" />
    <replace file="src/VersionInfo.h" token="@CLIENT_VERSION@" value="${release.version}" />
  </target>

  <target name="test" >
    <!-- display Python version -->
    <exec executable="python" failonerror="true">
      <arg line="-V"/>
    </exec>
    <exec executable="python" dir="test" spawn="true">
        <arg value="mqttsas2.py" />
        <arg value="${test.hostname}" />
        <arg value="${test.port}" />
        <arg value="${proxy.port}" />
    </exec>
    <if>
      <os family="windows"/>
    <then>
      <foreach target="runAtest" param="aTest" list="test1,test2,test4,test9"/>
    </then>
    <else>
      <foreach target="runAtest" param="aTest" list="test1,test2,test4,test9"/>
    </else>
    </if>
    <foreach target="runSSLtest" param="aTest" list="test3,test5"/>
  </target>

  <target name="runAtest">
    <if>
      <os family="windows"/>
    <then>
      <exec executable="cmd.exe" failonerror="true" dir="${output.folder}/test" >
        <arg value="/c" />
        <arg value="${aTest}.exe" />
        <arg value="--connection" />
        <arg value="tcp://${test.hostname}:${test.port}" />
        <arg value="--proxy_connection" />
        <arg value="tcp://localhost:${proxy.port}" />
        <env key="PATH" path="${output.folder}" />
      </exec>
    </then>
    <else>
      <exec executable="./${aTest}" failonerror="true" dir="${output.folder}/test" >
        <arg value="--connection" />
        <arg value="tcp://${test.hostname}:${test.port}" />
        <arg value="--proxy_connection" />
        <arg value="tcp://localhost:${proxy.port}" />
        <env key="LD_LIBRARY_PATH" path="${output.folder}" />
        <env key="DYLD_LIBRARY_PATH" path="${output.folder}" />
      </exec>
    </else>
    </if>
  </target>

  <target name="runSSLtest">
    <if>
      <os family="windows"/>
    <then>
      <exec executable="cmd.exe" failonerror="true" dir="${output.folder}/test" >
        <arg value="/c" />
        <arg value="${aTest}.exe" />
        <arg value="--hostname" />
        <arg value="${test.hostname}" />
        <env key="PATH" path="${output.folder};${windows.openssl.folder}" />
      </exec>
    </then>
    <else>
      <exec executable="./${aTest}" failonerror="true" dir="${output.folder}/test" >
        <arg value="--hostname" />
        <arg value="${test.hostname}" />
        <env key="LD_LIBRARY_PATH" path="${output.folder}" />
        <env key="DYLD_LIBRARY_PATH" path="${output.folder}" />
      </exec>
    </else>
    </if>
  </target>

  <target name="doc" >
    <if>
      <available file="/usr/bin/doxygen"/>
      <then>
        <mkdir dir="${output.folder}/doc"/>
        <exec executable="doxygen" dir="src">
          <arg value="../doc/DoxyfileV3ClientAPI"/>
        </exec>
        <exec executable="doxygen" dir="src">
          <arg value="../doc/DoxyfileV3AsyncAPI"/>
        </exec>
        <zip destfile="${output.folder}/MQTTClient_doc.zip">
          <zipfileset dir="${output.folder}/doc/MQTTClient" />
        </zip>
        <zip destfile="${output.folder}/MQTTAsync_doc.zip">
	        <zipfileset dir="${output.folder}/doc/MQTTAsync" prefix="MQTTAsync/"/>
        </zip>
        <delete dir="${output.folder}/doc" />
      </then>
      <else>
        <echo message="doxygen is not available" />
      </else>
    </if>
  </target>

  <target name="build" >
    <if>
    <os family="unix"/>
    <then>
    <delete dir="${output.folder}" />
    <!-- display gcc version -->
    <exec executable="gcc" failonerror="true">
      <arg line="-v"/>
    </exec>
    <if>
      <available file="/usr/bin/make"/>
      <then>
        <exec executable="make" dir="."/>
      </then>
    </if>
    </then>
    </if>
    <if>
    <os family="windows"/>
    <then>
    <delete dir="${output.folder}" />
    <!-- display gcc version -->
    <exec executable="cl" failonerror="true">
    </exec>
    <exec executable="msbuild" dir=".">
           <arg line='"Windows Build\Paho C MQTT APIs.sln"'/>
           <arg line="/p:Configuration=Release"/>
    </exec>
     </then>
    </if>
  </target>

  <target name="package">
    <mkdir dir="${output.folder}/include"/>
    <copy overwrite="true" todir="${output.folder}/include">
      <fileset dir="src" includes="MQTTClient.h,MQTTAsync.h,MQTTClientPersistence.h"/>
    </copy>
    <copy overwrite="true" todir="${output.folder}">
      <fileset dir="." includes="README.md,CONTRIBUTING.md,about.html,notice.html,edl-v10,epl-v20"/>
    </copy>
    <mkdir dir="${output.folder}/lib"/>
    <move overwrite="true" todir="${output.folder}/lib">
      <fileset dir="${output.folder}" includes="*paho*"/>
    </move>
    <mkdir dir="${output.folder}/bin"/>
    <move overwrite="true" todir="${output.folder}/bin">
      <fileset dir="${output.folder}/samples" includes="*"/>
      <fileset dir="${output.folder}" includes="MQTTVersion"/>
    </move>
    <copy overwrite="true" todir="${output.folder}/samples">
      <fileset dir="src/samples" includes="*"/>
    </copy>
    <delete>
       <fileset dir="." includes="eclipse-paho-mqtt-c-windows-${release.version}.zip"/>
       <fileset dir="." includes="eclipse-paho-mqtt-c-${os.family}-${release.version}.tar.gz"/>
    </delete>

   <if>
    <os family="windows"/>
    <then>
    <exec executable="c:\cygwin\bin\zip.exe" failonerror="true" dir="${output.folder}">
      <arg value="-r"/>
      <arg value="eclipse-paho-mqtt-c-windows-${release.version}.zip"/>
      <arg value="about.html"/>
      <arg value="notice.html"/>
      <arg value="README.md"/>
      <arg value="CONTRIBUTING.md"/>
      <arg value="epl-v20"/>
      <arg value="edl-v10"/>
      <arg value="include"/>
	  <arg value="samples"/>
      <arg value="lib"/>
      <arg value="bin"/>
    </exec>
    </then>
    <else>
    <exec executable="tar" failonerror="true" dir="${output.folder}">
      <arg value="czf"/>
      <arg value="eclipse-paho-mqtt-c-${os.family}-${release.version}.tar.gz"/>
      <arg value="about.html"/>
      <arg value="notice.html"/>
      <arg value="README.md"/>
      <arg value="CONTRIBUTING.md"/>
      <arg value="epl-v20"/>
      <arg value="edl-v10"/>
      <arg value="include"/>
	  <arg value="samples"/>
      <arg value="lib"/>
      <arg value="bin"/>
    </exec>
    </else>
    </if>

   <if>
    <os family="unix"/>
    <then>
    <exec executable="tar" failonerror="true" dir=".">
      <arg value="czf"/>
      <arg value="${output.folder}/eclipse-paho-mqtt-c-src-${release.version}.tar.gz"/>
      <arg value="about.html"/>
      <arg value="notice.html"/>
      <arg value="README.md"/>
      <arg value="CONTRIBUTING.md"/>
      <arg value="epl-v20"/>
      <arg value="edl-v10"/>
      <arg value="Makefile"/>
      <arg value="build.xml"/>
      <arg value="src"/>
      <arg value="test"/>
      <arg value="Windows Build"/>
    </exec>
    </then>
   </if>
  </target>

  <target name="copy">
    <if>
      <available file="/shared/technology"/>
      <then>
        <mkdir dir="/shared/technology/paho/C/${release.version}/${build.level}"/>
       	<echo message="Copying the build output to /shared" />
      	<copy overwrite="true" todir="/shared/technology/paho/C/${release.version}/${build.level}">
          <fileset dir="${output.folder}">
	          <include name="*.gz"/>
	          <include name="*.zip"/>
          </fileset>
        </copy>
      </then>
    </if>
  </target>

  <target name="full" depends="init, version, build, test, doc, package, copy" />

</project>
