.TH "qos" 3 "Tue Jan 7 2025 13:21:07" "Paho Asynchronous MQTT C Client Library" \" -*- nroff -*-
.ad l
.nh
.SH NAME
qos \- Quality of service 
.PP
The MQTT protocol provides three qualities of service for delivering messages between clients and servers: 'at most once', 'at least once' and 'exactly once'\&.

.PP
Quality of service (QoS) is an attribute of an individual message being published\&. An application sets the QoS for a specific message by setting the \fBMQTTAsync_message\&.qos\fP field to the required value\&.

.PP
A subscribing client can set the maximum quality of service a server uses to send messages that match the client subscriptions\&. The \fBMQTTAsync_subscribe()\fP and \fBMQTTAsync_subscribeMany()\fP functions set this maximum\&. The QoS of a message forwarded to a subscriber thus might be different to the QoS given to the message by the original publisher\&. The lower of the two values is used to forward a message\&.

.PP
The three levels are:

.PP
\fBQoS0, At most once:\fP The message is delivered at most once, or it may not be delivered at all\&. Its delivery across the network is not acknowledged\&. The message is not stored\&. The message could be lost if the client is disconnected, or if the server fails\&. QoS0 is the fastest mode of transfer\&. It is sometimes called 'fire and forget'\&.

.PP
The MQTT protocol does not require servers to forward publications at QoS0 to a client\&. If the client is disconnected at the time the server receives the publication, the publication might be discarded, depending on the server implementation\&.

.PP
\fBQoS1, At least once:\fP The message is always delivered at least once\&. It might be delivered multiple times if there is a failure before an acknowledgment is received by the sender\&. The message must be stored locally at the sender, until the sender receives confirmation that the message has been published by the receiver\&. The message is stored in case the message must be sent again\&.

.PP
\fBQoS2, Exactly once:\fP The message is always delivered exactly once\&. The message must be stored locally at the sender, until the sender receives confirmation that the message has been published by the receiver\&. The message is stored in case the message must be sent again\&. QoS2 is the safest, but slowest mode of transfer\&. A more sophisticated handshaking and acknowledgement sequence is used than for QoS1 to ensure no duplication of messages occurs\&. 
