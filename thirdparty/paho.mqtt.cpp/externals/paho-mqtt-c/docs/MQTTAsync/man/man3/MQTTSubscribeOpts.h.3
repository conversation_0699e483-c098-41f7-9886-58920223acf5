.TH "MQTTSubscribeOpts.h" 3 "Tue Jan 7 2025 13:21:07" "Paho Asynchronous MQTT C Client Library" \" -*- nroff -*-
.ad l
.nh
.SH NAME
MQTTSubscribeOpts.h
.SH SYNOPSIS
.br
.PP
.SS "Data Structures"

.in +1c
.ti -1c
.RI "struct \fBMQTTSubscribe_options\fP"
.br
.in -1c
.SS "Macros"

.in +1c
.ti -1c
.RI "#define \fBMQTTSubscribe_options_initializer\fP   { {'M', 'Q', 'S', 'O'}, 0, 0, 0, 0 }"
.br
.in -1c
.SS "Typedefs"

.in +1c
.ti -1c
.RI "typedef struct MQTTSubscribe_options \fBMQTTSubscribe_options\fP"
.br
.in -1c
.SH "Macro Definition Documentation"
.PP 
.SS "#define MQTTSubscribe_options_initializer   { {'M', 'Q', 'S', 'O'}, 0, 0, 0, 0 }"

.SH "Typedef Documentation"
.PP 
.SS "typedef struct MQTTSubscribe_options MQTTSubscribe_options"
The MQTT V5 subscribe options, apart from QoS which existed before V5\&. 
.SH "Author"
.PP 
Generated automatically by Doxygen for Paho Asynchronous MQTT C Client Library from the source code\&.
