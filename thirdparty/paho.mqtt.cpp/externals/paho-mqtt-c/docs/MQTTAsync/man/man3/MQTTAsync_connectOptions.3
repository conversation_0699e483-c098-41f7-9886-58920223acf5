.TH "MQTTAsync_connectOptions" 3 "Tue Jan 7 2025 13:21:07" "Paho Asynchronous MQTT C Client Library" \" -*- nroff -*-
.ad l
.nh
.SH NAME
MQTTAsync_connectOptions
.SH SYNOPSIS
.br
.PP
.PP
\fR#include <MQTTAsync\&.h>\fP
.SS "Data Fields"

.in +1c
.ti -1c
.RI "char \fBstruct_id\fP [4]"
.br
.ti -1c
.RI "int \fBstruct_version\fP"
.br
.ti -1c
.RI "int \fBkeepAliveInterval\fP"
.br
.ti -1c
.RI "int \fBcleansession\fP"
.br
.ti -1c
.RI "int \fBmaxInflight\fP"
.br
.ti -1c
.RI "\fBMQTTAsync_willOptions\fP * \fBwill\fP"
.br
.ti -1c
.RI "const char * \fBusername\fP"
.br
.ti -1c
.RI "const char * \fBpassword\fP"
.br
.ti -1c
.RI "int \fBconnectTimeout\fP"
.br
.ti -1c
.RI "int \fBretryInterval\fP"
.br
.ti -1c
.RI "\fBMQTTAsync_SSLOptions\fP * \fBssl\fP"
.br
.ti -1c
.RI "\fBMQTTAsync_onSuccess\fP * \fBonSuccess\fP"
.br
.ti -1c
.RI "\fBMQTTAsync_onFailure\fP * \fBonFailure\fP"
.br
.ti -1c
.RI "void * \fBcontext\fP"
.br
.ti -1c
.RI "int \fBserverURIcount\fP"
.br
.ti -1c
.RI "char *const  * \fBserverURIs\fP"
.br
.ti -1c
.RI "int \fBMQTTVersion\fP"
.br
.ti -1c
.RI "int \fBautomaticReconnect\fP"
.br
.ti -1c
.RI "int \fBminRetryInterval\fP"
.br
.ti -1c
.RI "int \fBmaxRetryInterval\fP"
.br
.ti -1c
.RI "struct {"
.br
.ti -1c
.RI "   int \fBlen\fP"
.br
.ti -1c
.RI "   const void * \fBdata\fP"
.br
.ti -1c
.RI "} \fBbinarypwd\fP"
.br
.ti -1c
.RI "int \fBcleanstart\fP"
.br
.ti -1c
.RI "\fBMQTTProperties\fP * \fBconnectProperties\fP"
.br
.ti -1c
.RI "\fBMQTTProperties\fP * \fBwillProperties\fP"
.br
.ti -1c
.RI "\fBMQTTAsync_onSuccess5\fP * \fBonSuccess5\fP"
.br
.ti -1c
.RI "\fBMQTTAsync_onFailure5\fP * \fBonFailure5\fP"
.br
.ti -1c
.RI "const \fBMQTTAsync_nameValue\fP * \fBhttpHeaders\fP"
.br
.ti -1c
.RI "const char * \fBhttpProxy\fP"
.br
.ti -1c
.RI "const char * \fBhttpsProxy\fP"
.br
.in -1c
.SH "Detailed Description"
.PP 
\fBMQTTAsync_connectOptions\fP defines several settings that control the way the client connects to an MQTT server\&.

.PP
Suitable default values are set in the following initializers:
.IP "\(bu" 2
MQTTAsync_connectOptions_initializer: for MQTT 3\&.1\&.1 non-WebSockets
.IP "\(bu" 2
MQTTAsync_connectOptions_initializer5: for MQTT 5\&.0 non-WebSockets
.IP "\(bu" 2
MQTTAsync_connectOptions_initializer_ws: for MQTT 3\&.1\&.1 WebSockets
.IP "\(bu" 2
MQTTAsync_connectOptions_initializer5_ws: for MQTT 5\&.0 WebSockets 
.PP

.SH "Field Documentation"
.PP 
.SS "char struct_id[4]"
The eyecatcher for this structure\&. must be MQTC\&. 
.SS "int struct_version"
The version number of this structure\&. Must be 0, 1, 2, 3 4 5 6, 7 or 8\&. 0 signifies no SSL options and no serverURIs 1 signifies no serverURIs 2 signifies no MQTTVersion 3 signifies no automatic reconnect options 4 signifies no binary password option (just string) 5 signifies no MQTTV5 properties 6 signifies no HTTP headers option 7 signifies no HTTP proxy and HTTPS proxy options 
.SS "int keepAliveInterval"
The 'keep alive' interval, measured in seconds, defines the maximum time that should pass without communication between the client and the server The client will ensure that at least one message travels across the network within each keep alive period\&. In the absence of a data-related message during the time period, the client sends a very small MQTT 'ping' message, which the server will acknowledge\&. The keep alive interval enables the client to detect when the server is no longer available without having to wait for the long TCP/IP timeout\&. Set to 0 if you do not want any keep alive processing\&. 
.SS "int cleansession"
This is a boolean value\&. The cleansession setting controls the behaviour of both the client and the server at connection and disconnection time\&. The client and server both maintain session state information\&. This information is used to ensure 'at least once' and 'exactly once' delivery, and 'exactly once' receipt of messages\&. Session state also includes subscriptions created by an MQTT client\&. You can choose to maintain or discard state information between sessions\&.

.PP
When cleansession is true, the state information is discarded at connect and disconnect\&. Setting cleansession to false keeps the state information\&. When you connect an MQTT client application with \fBMQTTAsync_connect()\fP, the client identifies the connection using the client identifier and the address of the server\&. The server checks whether session information for this client has been saved from a previous connection to the server\&. If a previous session still exists, and cleansession=true, then the previous session information at the client and server is cleared\&. If cleansession=false, the previous session is resumed\&. If no previous session exists, a new session is started\&. 
.SS "int maxInflight"
This controls how many messages can be in-flight simultaneously\&. 
.SS "\fBMQTTAsync_willOptions\fP* will"
This is a pointer to an \fBMQTTAsync_willOptions\fP structure\&. If your application does not make use of the Last Will and Testament feature, set this pointer to NULL\&. 
.SS "const char* username"
MQTT servers that support the MQTT v3\&.1 protocol provide authentication and authorisation by user name and password\&. This is the user name parameter\&. 
.SS "const char* password"
MQTT servers that support the MQTT v3\&.1 protocol provide authentication and authorisation by user name and password\&. This is the password parameter\&. 
.SS "int connectTimeout"
The time interval in seconds to allow a connect to complete\&. 
.SS "int retryInterval"
The time interval in seconds after which unacknowledged publish requests are retried during a TCP session\&. With MQTT 3\&.1\&.1 and later, retries are not required except on reconnect\&. 0 turns off in-session retries, and is the recommended setting\&. Adding retries to an already overloaded network only exacerbates the problem\&. 
.SS "\fBMQTTAsync_SSLOptions\fP* ssl"
This is a pointer to an \fBMQTTAsync_SSLOptions\fP structure\&. If your application does not make use of SSL, set this pointer to NULL\&. 
.SS "\fBMQTTAsync_onSuccess\fP* onSuccess"
A pointer to a callback function to be called if the connect successfully completes\&. Can be set to NULL, in which case no indication of successful completion will be received\&. 
.SS "\fBMQTTAsync_onFailure\fP* onFailure"
A pointer to a callback function to be called if the connect fails\&. Can be set to NULL, in which case no indication of unsuccessful completion will be received\&. 
.SS "void* context"

.PP
.nf
A pointer to any application-specific context\&. The

.fi
.PP
 the \fIcontext\fP pointer is passed to success or failure callback functions to provide access to the context information in the callback\&. 
.SS "int serverURIcount"
The number of entries in the serverURIs array\&. 
.SS "char* const* serverURIs"

.PP
.nf
An array of null-terminated strings specifying the servers to

.fi
.PP
 which the client will connect\&. Each string takes the form \fIprotocol://host:port\fP\&. \fIprotocol\fP must be \fItcp\fP, \fIssl\fP, \fIws\fP or \fIwss\fP\&. The TLS enabled prefixes (ssl, wss) are only valid if a TLS version of the library is linked with\&. For \fIhost\fP, you can specify either an IP address or a domain name\&. For instance, to connect to a server running on the local machines with the default MQTT port, specify \fItcp://localhost:1883\fP\&. 
.SS "int MQTTVersion"
Sets the version of MQTT to be used on the connect\&. MQTTVERSION_DEFAULT (0) = default: start with 3\&.1\&.1, and if that fails, fall back to 3\&.1 MQTTVERSION_3_1 (3) = only try version 3\&.1 MQTTVERSION_3_1_1 (4) = only try version 3\&.1\&.1 
.SS "int automaticReconnect"
Reconnect automatically in the case of a connection being lost\&. 0=false, 1=true 
.SS "int minRetryInterval"
The minimum automatic reconnect retry interval in seconds\&. Doubled on each failed retry\&. 
.SS "int maxRetryInterval"
The maximum automatic reconnect retry interval in seconds\&. The doubling stops here on failed retries\&. 
.SS "int len"
binary password length 
.SS "const void* data"
binary password data 
.SS "struct  { \&.\&.\&. }  binarypwd"
Optional binary password\&. Only checked and used if the password option is NULL 
.SS "int cleanstart"

.SS "\fBMQTTProperties\fP* connectProperties"
MQTT V5 properties for connect 
.SS "\fBMQTTProperties\fP* willProperties"
MQTT V5 properties for the will message in the connect 
.SS "\fBMQTTAsync_onSuccess5\fP* onSuccess5"
A pointer to a callback function to be called if the connect successfully completes\&. Can be set to NULL, in which case no indication of successful completion will be received\&. 
.SS "\fBMQTTAsync_onFailure5\fP* onFailure5"
A pointer to a callback function to be called if the connect fails\&. Can be set to NULL, in which case no indication of unsuccessful completion will be received\&. 
.SS "const \fBMQTTAsync_nameValue\fP* httpHeaders"
HTTP headers for websockets 
.SS "const char* httpProxy"
The string value of the HTTP proxy\&. Examples:
.IP "\(bu" 2
http://your.proxy.server:8080/
.IP "\(bu" 2
http://user:<EMAIL>:8080/ 
.PP

.SS "const char* httpsProxy"
HTTPS proxy setting\&. See \fBMQTTAsync_connectOptions\&.httpProxy\fP and the section \fBHTTP Proxies\fP\&. 

.SH "Author"
.PP 
Generated automatically by Doxygen for Paho Asynchronous MQTT C Client Library from the source code\&.
