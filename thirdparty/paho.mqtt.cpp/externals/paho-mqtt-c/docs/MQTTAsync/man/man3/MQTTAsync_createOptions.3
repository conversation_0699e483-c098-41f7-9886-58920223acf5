.TH "MQTTAsync_createOptions" 3 "Tue Jan 7 2025 13:21:07" "Paho Asynchronous MQTT C Client Library" \" -*- nroff -*-
.ad l
.nh
.SH NAME
MQTTAsync_createOptions
.SH SYNOPSIS
.br
.PP
.PP
\fR#include <MQTTAsync\&.h>\fP
.SS "Data Fields"

.in +1c
.ti -1c
.RI "char \fBstruct_id\fP [4]"
.br
.ti -1c
.RI "int \fBstruct_version\fP"
.br
.ti -1c
.RI "int \fBsendWhileDisconnected\fP"
.br
.ti -1c
.RI "int \fBmaxBufferedMessages\fP"
.br
.ti -1c
.RI "int \fBMQTTVersion\fP"
.br
.ti -1c
.RI "int \fBallowDisconnectedSendAtAnyTime\fP"
.br
.ti -1c
.RI "int \fBdeleteOldestMessages\fP"
.br
.ti -1c
.RI "int \fBrestoreMessages\fP"
.br
.ti -1c
.RI "int \fBpersistQoS0\fP"
.br
.in -1c
.SH "Detailed Description"
.PP 
Options for the \fBMQTTAsync_createWithOptions\fP call 
.SH "Field Documentation"
.PP 
.SS "char struct_id[4]"
The eyecatcher for this structure\&. must be MQCO\&. 
.SS "int struct_version"
The version number of this structure\&. Must be 0, 1, 2 or 3 0 means no MQTTVersion 1 means no allowDisconnectedSendAtAnyTime, deleteOldestMessages, restoreMessages 2 means no persistQoS0 
.SS "int sendWhileDisconnected"
Whether to allow messages to be sent when the client library is not connected\&. 
.SS "int maxBufferedMessages"
The maximum number of messages allowed to be buffered\&. This is intended to be used to limit the number of messages queued while the client is not connected\&. It also applies when the client is connected, however, so has to be greater than 0\&. 
.SS "int MQTTVersion"
Whether the MQTT version is 3\&.1, 3\&.1\&.1, or 5\&. To use V5, this must be set\&. MQTT V5 has to be chosen here, because during the create call the message persistence is initialized, and we want to know whether the format of any persisted messages is appropriate for the MQTT version we are going to connect with\&. Selecting 3\&.1 or 3\&.1\&.1 and attempting to read 5\&.0 persisted messages will result in an error on create\&. 
.br
 
.SS "int allowDisconnectedSendAtAnyTime"
Allow sending of messages while disconnected before a first successful connect\&. 
.SS "int deleteOldestMessages"

.SS "int restoreMessages"

.SS "int persistQoS0"


.SH "Author"
.PP 
Generated automatically by Doxygen for Paho Asynchronous MQTT C Client Library from the source code\&.
