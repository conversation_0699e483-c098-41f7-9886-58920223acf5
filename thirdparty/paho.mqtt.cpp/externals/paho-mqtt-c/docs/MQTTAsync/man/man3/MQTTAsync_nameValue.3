.TH "MQTTAsync_nameValue" 3 "<PERSON><PERSON> Jan 7 2025 13:21:08" "Paho Asynchronous MQTT C Client Library" \" -*- nroff -*-
.ad l
.nh
.SH NAME
MQTTAsync_nameValue
.SH SYNOPSIS
.br
.PP
.PP
\fR#include <MQTTAsync\&.h>\fP
.SS "Data Fields"

.in +1c
.ti -1c
.RI "const char * \fBname\fP"
.br
.ti -1c
.RI "const char * \fBvalue\fP"
.br
.in -1c
.SH "Detailed Description"
.PP 
Utility structure where name/value pairs are needed 
.SH "Field Documentation"
.PP 
.SS "const char* name"
name string 
.SS "const char* value"
value string 

.SH "Author"
.PP 
Generated automatically by Doxygen for Paho Asynchronous MQTT C Client Library from the source code\&.
