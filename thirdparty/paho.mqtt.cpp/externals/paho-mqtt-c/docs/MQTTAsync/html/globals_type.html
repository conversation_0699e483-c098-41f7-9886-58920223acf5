<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all typedefs with links to the files they belong to:</div><ul>
<li>MQTTAsync&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync.h</a></li>
<li>MQTTAsync_callOptions&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#ab6bfa6beae93c259220e1a131ba1cf9c">MQTTAsync.h</a></li>
<li>MQTTAsync_connected&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a34bb8d321e9d368780b5c832c058f223">MQTTAsync.h</a></li>
<li>MQTTAsync_connectionLost&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">MQTTAsync.h</a></li>
<li>MQTTAsync_deliveryComplete&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync.h</a></li>
<li>MQTTAsync_disconnected&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a52a1d9ab6e5d5064a3de42d0eec88f57">MQTTAsync.h</a></li>
<li>MQTTAsync_messageArrived&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync.h</a></li>
<li>MQTTAsync_onFailure&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync.h</a></li>
<li>MQTTAsync_onFailure5&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync.h</a></li>
<li>MQTTAsync_onSuccess&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync.h</a></li>
<li>MQTTAsync_onSuccess5&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync.h</a></li>
<li>MQTTAsync_responseOptions&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#ae1568d96d6418004cc79466c06f3d791">MQTTAsync.h</a></li>
<li>MQTTAsync_token&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync.h</a></li>
<li>MQTTAsync_traceCallback&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a65aba1caeae9b5af5d5b6c5598a75b02">MQTTAsync.h</a></li>
<li>MQTTAsync_updateConnectOptions&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a5e44304a2c011a7d61b72c779ad83979">MQTTAsync.h</a></li>
<li>MQTTPersistence_afterRead&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#af5a966a574c6ad7a35f1ebb7edd5c1c4">MQTTClientPersistence.h</a></li>
<li>MQTTPersistence_beforeWrite&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#ab865640a1cc53b68622004c5a2d29fae">MQTTClientPersistence.h</a></li>
<li>MQTTProperties&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a7758f1a5eceb6f46c8540630e39e2fb4">MQTTProperties.h</a></li>
<li>MQTTSubscribe_options&#160;:&#160;<a class="el" href="_m_q_t_t_subscribe_opts_8h.html#aa68db3eaed272ae1aaea294401079d8a">MQTTSubscribeOpts.h</a></li>
<li>Persistence_clear&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#acee7097c1a0ab44b98c870f533687887">MQTTClientPersistence.h</a></li>
<li>Persistence_close&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#a3582de2c87e89f617e8e553b2a0e279a">MQTTClientPersistence.h</a></li>
<li>Persistence_containskey&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#a753a0f9a9c51284d63a907af19c7bbba">MQTTClientPersistence.h</a></li>
<li>Persistence_get&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#adc3aff3c570fa5509e9d6814a85ab867">MQTTClientPersistence.h</a></li>
<li>Persistence_keys&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#a2601cc91eeabdbf9578f8dd45e4997a8">MQTTClientPersistence.h</a></li>
<li>Persistence_open&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#a4c7d332bb16907058ae3b375488b6008">MQTTClientPersistence.h</a></li>
<li>Persistence_put&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#a44679cab77cfbd6e2a4639cdd27ac80c">MQTTClientPersistence.h</a></li>
<li>Persistence_remove&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#a73350bf7208658bf5434a59f7bdbae90">MQTTClientPersistence.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:08 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
