<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all macros with links to the files they belong to:</div>

<h3><a id="index_m" name="index_m"></a>- m -</h3><ul>
<li>MQTT_BAD_SUBSCRIBE&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#ade337b363b7f4bc7c1a7b2858e0380bd">MQTTAsync.h</a></li>
<li>MQTT_INVALID_PROPERTY_ID&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#afc56d2e8937a0c8f180d68ad93945945">MQTTProperties.h</a></li>
<li>MQTT_SSL_VERSION_DEFAULT&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a2549ea897af26c76198284731db9e721">MQTTAsync.h</a></li>
<li>MQTT_SSL_VERSION_TLS_1_0&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a7e5da3d6f0d2b53409bbfcf6e56f3d2d">MQTTAsync.h</a></li>
<li>MQTT_SSL_VERSION_TLS_1_1&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#abdff87efa3f2ee473a1591e10638b537">MQTTAsync.h</a></li>
<li>MQTT_SSL_VERSION_TLS_1_2&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a3a94dbdeafbb73c73a068e7c2085fbab">MQTTAsync.h</a></li>
<li>MQTTASYNC_0_LEN_WILL_TOPIC&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a47b3aed75983f48a503e1cad6c862004">MQTTAsync.h</a></li>
<li>MQTTASYNC_BAD_MQTT_OPTION&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#af6f97562573876867ba77460a51ca1d1">MQTTAsync.h</a></li>
<li>MQTTASYNC_BAD_PROTOCOL&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a785250cd4a1938ffeeff67b3538abfba">MQTTAsync.h</a></li>
<li>MQTTASYNC_BAD_QOS&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a64d111778ce4e0d3a62808f6db11f224">MQTTAsync.h</a></li>
<li>MQTTASYNC_BAD_STRUCTURE&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a241fc8db46dca132d591bc2be92247ba">MQTTAsync.h</a></li>
<li>MQTTASYNC_BAD_UTF8_STRING&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a80cbe091930c11b67ca719b3e385aa26">MQTTAsync.h</a></li>
<li>MQTTAsync_callOptions_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a570185766fc8a9da410a6f84915b6df5">MQTTAsync.h</a></li>
<li>MQTTASYNC_COMMAND_IGNORED&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a8278cf4b50dd818c31fa12e45f074b5c">MQTTAsync.h</a></li>
<li>MQTTAsync_connectData_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a2e415e68016ae56f6bbbbdc9840a9c6e">MQTTAsync.h</a></li>
<li>MQTTAsync_connectOptions_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#ae18b51f22784a43803eb809d6a0c2492">MQTTAsync.h</a></li>
<li>MQTTAsync_connectOptions_initializer5&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#abd403ce21f7aa0348ae1d3eefd031a5d">MQTTAsync.h</a></li>
<li>MQTTAsync_connectOptions_initializer5_ws&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a513bfbec7b7d39c827240db75aa4044b">MQTTAsync.h</a></li>
<li>MQTTAsync_connectOptions_initializer_ws&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a080951d916d7a58c4ceff8c6bacfe313">MQTTAsync.h</a></li>
<li>MQTTAsync_createOptions_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a5fedeafef4753f09b1bcb92773564786">MQTTAsync.h</a></li>
<li>MQTTAsync_createOptions_initializer5&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a0008776a46e7268ccbef4774ce3d4579">MQTTAsync.h</a></li>
<li>MQTTASYNC_DISCONNECTED&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a66c0f30b329bc770145c2f04b3929df6">MQTTAsync.h</a></li>
<li>MQTTAsync_disconnectOptions_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a2fd5d6df31928ae468f3f2e522b9c707">MQTTAsync.h</a></li>
<li>MQTTAsync_disconnectOptions_initializer5&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#aaa278001953dc129003eff83c8e7b3db">MQTTAsync.h</a></li>
<li>MQTTASYNC_FAILURE&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a7c8230fef85fc04b8a1035501f3be406">MQTTAsync.h</a></li>
<li>MQTTAsync_failureData5_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a53ce2002ae2c2579575bb41c48c51c29">MQTTAsync.h</a></li>
<li>MQTTAsync_init_options_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a866e023f70141969d48597930c0ee313">MQTTAsync.h</a></li>
<li>MQTTASYNC_MAX_BUFFERED&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a2efee8e190e2c3690c680bde060f78ab">MQTTAsync.h</a></li>
<li>MQTTASYNC_MAX_BUFFERED_MESSAGES&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a4e338072cfd5291b579e4f0c99a6e773">MQTTAsync.h</a></li>
<li>MQTTASYNC_MAX_MESSAGES_INFLIGHT&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#ad577286d43c72fbc49818aac42f4e24a">MQTTAsync.h</a></li>
<li>MQTTAsync_message_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a6a85061dadab532f28e96e5ab3c600e9">MQTTAsync.h</a></li>
<li>MQTTASYNC_NO_MORE_MSGIDS&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#ab0f54d0bae2c74849022a8009e5d6ff7">MQTTAsync.h</a></li>
<li>MQTTASYNC_NULL_PARAMETER&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#ab88e1ebcee991099a72429e52a8253fd">MQTTAsync.h</a></li>
<li>MQTTASYNC_OPERATION_INCOMPLETE&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#aee1b79d0632bec0fe49eb7ea1abd3b2e">MQTTAsync.h</a></li>
<li>MQTTASYNC_PERSISTENCE_ERROR&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a4edf1249c75abd4975fec8ddeae2cdc9">MQTTAsync.h</a></li>
<li>MQTTAsync_responseOptions_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a3f8b408243b5c2369bc9758f2edf0878">MQTTAsync.h</a></li>
<li>MQTTASYNC_SSL_NOT_SUPPORTED&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a6992c00553db1608aef9e162c161d73c">MQTTAsync.h</a></li>
<li>MQTTAsync_SSLOptions_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#aac935e2e9d770a53ee8189f128530511">MQTTAsync.h</a></li>
<li>MQTTASYNC_SUCCESS&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTAsync.h</a></li>
<li>MQTTAsync_successData5_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a6182ec90ec4a134465f627b324ac5a41">MQTTAsync.h</a></li>
<li>MQTTASYNC_TOPICNAME_TRUNCATED&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a77a7106d97ff60be3fe70f90b1867800">MQTTAsync.h</a></li>
<li>MQTTASYNC_TRUE&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a61e6ee632e63312d382e2fcbe427f01a">MQTTAsync.h</a></li>
<li>MQTTAsync_willOptions_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a6c45768e1b28844f2ac0f6ac68709730">MQTTAsync.h</a></li>
<li>MQTTASYNC_WRONG_MQTT_VERSION&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#af5df806e9767e1e3182fe089a8ee551b">MQTTAsync.h</a></li>
<li>MQTTCLIENT_PERSISTENCE_DEFAULT&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#aaa948291718a9c06369b854b0f64bc32">MQTTClientPersistence.h</a></li>
<li>MQTTCLIENT_PERSISTENCE_ERROR&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#ab716e21e53c84a5ad62aa962a2a8f7db">MQTTClientPersistence.h</a></li>
<li>MQTTCLIENT_PERSISTENCE_NONE&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#ae01e089313a65ac4661ed216b6ac00fa">MQTTClientPersistence.h</a></li>
<li>MQTTCLIENT_PERSISTENCE_USER&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#a5dc68b8616e4041e037bad94ce07681b">MQTTClientPersistence.h</a></li>
<li>MQTTProperties_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a5a80e158486a414ccdfcdd7f75f23988">MQTTProperties.h</a></li>
<li>MQTTSubscribe_options_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_subscribe_opts_8h.html#aec3b45fd0367106eea344396f87cfda7">MQTTSubscribeOpts.h</a></li>
<li>MQTTVERSION_3_1&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a4603b988e76872e1f23f135d225ce2fb">MQTTAsync.h</a></li>
<li>MQTTVERSION_3_1_1&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#ac79cc6fdeaa9e3f4ee12c3418898b1ef">MQTTAsync.h</a></li>
<li>MQTTVERSION_5&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#af8b176fa4d5b89789767ce972338e1e3">MQTTAsync.h</a></li>
<li>MQTTVERSION_DEFAULT&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a75b80b01f98d5a1ffa2a4d42995a8397">MQTTAsync.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:08 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
