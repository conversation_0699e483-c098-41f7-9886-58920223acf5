<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: MQTTSubscribeOpts.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">MQTTSubscribeOpts.h</div></div>
</div><!--header-->
<div class="contents">
<a href="_m_q_t_t_subscribe_opts_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="comment">/*******************************************************************************</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span><span class="comment"> * Copyright (c) 2018 IBM Corp.</span></div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span><span class="comment"> *</span></div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="comment"> * All rights reserved. This program and the accompanying materials</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span><span class="comment"> * are made available under the terms of the Eclipse Public License v2.0</span></div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span><span class="comment"> * and Eclipse Distribution License v1.0 which accompany this distribution.</span></div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno">    7</span><span class="comment"> *</span></div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span><span class="comment"> * The Eclipse Public License is available at</span></div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno">    9</span><span class="comment"> *    https://www.eclipse.org/legal/epl-2.0/</span></div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno">   10</span><span class="comment"> * and the Eclipse Distribution License is available at</span></div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno">   11</span><span class="comment"> *   http://www.eclipse.org/org/documents/edl-v10.php.</span></div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno">   12</span><span class="comment"> *</span></div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span><span class="comment"> * Contributors:</span></div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno">   14</span><span class="comment"> *    Ian Craggs - initial API and implementation and/or initial documentation</span></div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span><span class="comment"> *******************************************************************************/</span></div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno">   16</span> </div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno">   17</span><span class="preprocessor">#if !defined(SUBOPTS_H)</span></div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno">   18</span><span class="preprocessor">#define SUBOPTS_H</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno">   19</span> </div>
<div class="foldopen" id="foldopen00021" data-start="{" data-end="};">
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_subscribe__options.html">   21</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a></div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno">   22</span>{</div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_subscribe__options.html#aa5326df180cb23c59afbcab711a06479">   24</a></span>        <span class="keywordtype">char</span> <a class="code hl_variable" href="struct_m_q_t_t_subscribe__options.html#aa5326df180cb23c59afbcab711a06479">struct_id</a>[4];</div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_subscribe__options.html#a0761a5e5be0383882e42924de8e51f82">   27</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_subscribe__options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_subscribe__options.html#abbb6a188886c12f305cbe69358515d8b">   31</a></span>        <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> <a class="code hl_variable" href="struct_m_q_t_t_subscribe__options.html#abbb6a188886c12f305cbe69358515d8b">noLocal</a>;</div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_subscribe__options.html#a8ba074ad218224ee4a8ca802c5e36944">   36</a></span>        <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> <a class="code hl_variable" href="struct_m_q_t_t_subscribe__options.html#a8ba074ad218224ee4a8ca802c5e36944">retainAsPublished</a>;</div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_subscribe__options.html#a11f17b62e40ecdfe107101ae164367a3">   41</a></span>        <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> <a class="code hl_variable" href="struct_m_q_t_t_subscribe__options.html#a11f17b62e40ecdfe107101ae164367a3">retainHandling</a>;</div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno"><a class="line" href="_m_q_t_t_subscribe_opts_8h.html#aa68db3eaed272ae1aaea294401079d8a">   42</a></span>} <a class="code hl_typedef" href="_m_q_t_t_subscribe_opts_8h.html#aa68db3eaed272ae1aaea294401079d8a">MQTTSubscribe_options</a>;</div>
</div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno">   43</span> </div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno"><a class="line" href="_m_q_t_t_subscribe_opts_8h.html#aec3b45fd0367106eea344396f87cfda7">   44</a></span><span class="preprocessor">#define MQTTSubscribe_options_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;S&#39;, &#39;O&#39;}, 0, 0, 0, 0 }</span></div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno">   45</span> </div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno">   46</span><span class="preprocessor">#endif</span></div>
<div class="ttc" id="a_m_q_t_t_subscribe_opts_8h_html_aa68db3eaed272ae1aaea294401079d8a"><div class="ttname"><a href="_m_q_t_t_subscribe_opts_8h.html#aa68db3eaed272ae1aaea294401079d8a">MQTTSubscribe_options</a></div><div class="ttdeci">struct MQTTSubscribe_options MQTTSubscribe_options</div></div>
<div class="ttc" id="astruct_m_q_t_t_subscribe__options_html"><div class="ttname"><a href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a></div><div class="ttdef"><b>Definition</b> MQTTSubscribeOpts.h:22</div></div>
<div class="ttc" id="astruct_m_q_t_t_subscribe__options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_subscribe__options.html#a0761a5e5be0383882e42924de8e51f82">MQTTSubscribe_options::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition</b> MQTTSubscribeOpts.h:27</div></div>
<div class="ttc" id="astruct_m_q_t_t_subscribe__options_html_a11f17b62e40ecdfe107101ae164367a3"><div class="ttname"><a href="struct_m_q_t_t_subscribe__options.html#a11f17b62e40ecdfe107101ae164367a3">MQTTSubscribe_options::retainHandling</a></div><div class="ttdeci">unsigned char retainHandling</div><div class="ttdef"><b>Definition</b> MQTTSubscribeOpts.h:41</div></div>
<div class="ttc" id="astruct_m_q_t_t_subscribe__options_html_a8ba074ad218224ee4a8ca802c5e36944"><div class="ttname"><a href="struct_m_q_t_t_subscribe__options.html#a8ba074ad218224ee4a8ca802c5e36944">MQTTSubscribe_options::retainAsPublished</a></div><div class="ttdeci">unsigned char retainAsPublished</div><div class="ttdef"><b>Definition</b> MQTTSubscribeOpts.h:36</div></div>
<div class="ttc" id="astruct_m_q_t_t_subscribe__options_html_aa5326df180cb23c59afbcab711a06479"><div class="ttname"><a href="struct_m_q_t_t_subscribe__options.html#aa5326df180cb23c59afbcab711a06479">MQTTSubscribe_options::struct_id</a></div><div class="ttdeci">char struct_id[4]</div><div class="ttdef"><b>Definition</b> MQTTSubscribeOpts.h:24</div></div>
<div class="ttc" id="astruct_m_q_t_t_subscribe__options_html_abbb6a188886c12f305cbe69358515d8b"><div class="ttname"><a href="struct_m_q_t_t_subscribe__options.html#abbb6a188886c12f305cbe69358515d8b">MQTTSubscribe_options::noLocal</a></div><div class="ttdeci">unsigned char noLocal</div><div class="ttdef"><b>Definition</b> MQTTSubscribeOpts.h:31</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:07 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
