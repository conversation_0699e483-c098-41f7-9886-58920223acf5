<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: MQTTAsync_successData5 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">MQTTAsync_successData5 Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="_m_q_t_t_async_8h_source.html">MQTTAsync.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:aa5326df180cb23c59afbcab711a06479" id="r_aa5326df180cb23c59afbcab711a06479"><td class="memItemLeft" align="right" valign="top">char&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa5326df180cb23c59afbcab711a06479">struct_id</a> [4]</td></tr>
<tr class="separator:aa5326df180cb23c59afbcab711a06479"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0761a5e5be0383882e42924de8e51f82" id="r_a0761a5e5be0383882e42924de8e51f82"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0761a5e5be0383882e42924de8e51f82">struct_version</a></td></tr>
<tr class="separator:a0761a5e5be0383882e42924de8e51f82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af8f771e67d284379111151b003c0d810" id="r_af8f771e67d284379111151b003c0d810"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af8f771e67d284379111151b003c0d810">token</a></td></tr>
<tr class="separator:af8f771e67d284379111151b003c0d810"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a580d8a8ecb285f5a86c2a3865438f8ee" id="r_a580d8a8ecb285f5a86c2a3865438f8ee"><td class="memItemLeft" align="right" valign="top">enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a580d8a8ecb285f5a86c2a3865438f8ee">reasonCode</a></td></tr>
<tr class="separator:a580d8a8ecb285f5a86c2a3865438f8ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1594008402f7307e4de8fa6131656dde" id="r_a1594008402f7307e4de8fa6131656dde"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1594008402f7307e4de8fa6131656dde">properties</a></td></tr>
<tr class="separator:a1594008402f7307e4de8fa6131656dde"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4bde812772718b8051b0d6e2000a5f5c" id="r_a4bde812772718b8051b0d6e2000a5f5c"><td class="memItemLeft" >union {&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a26c7d90bad0e00a056dff117b9111346" id="r_a26c7d90bad0e00a056dff117b9111346"><td class="memItemLeft" >&#160;&#160;&#160;struct {&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a26c7d90bad0e00a056dff117b9111346"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac97316626bd4faa6b71277c221275f4b" id="r_ac97316626bd4faa6b71277c221275f4b"><td class="memItemLeft" >&#160;&#160;&#160;&#160;&#160;&#160;int&#160;&#160;&#160;<a class="el" href="#ac97316626bd4faa6b71277c221275f4b">reasonCodeCount</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:ac97316626bd4faa6b71277c221275f4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2199c9d905dbfa279895cf8123c10f4f" id="r_a2199c9d905dbfa279895cf8123c10f4f"><td class="memItemLeft" >&#160;&#160;&#160;&#160;&#160;&#160;enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> *&#160;&#160;&#160;<a class="el" href="#a2199c9d905dbfa279895cf8123c10f4f">reasonCodes</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a2199c9d905dbfa279895cf8123c10f4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a26c7d90bad0e00a056dff117b9111346" id="r_a26c7d90bad0e00a056dff117b9111346"><td class="memItemLeft" valign="top">&#160;&#160;&#160;}&#160;&#160;&#160;<a class="el" href="#a26c7d90bad0e00a056dff117b9111346">sub</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a26c7d90bad0e00a056dff117b9111346"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5c41c63d6c37acbe3c493279c5d4c44a" id="r_a5c41c63d6c37acbe3c493279c5d4c44a"><td class="memItemLeft" >&#160;&#160;&#160;struct {&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a5c41c63d6c37acbe3c493279c5d4c44a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6ed8403758cecd2f762af6ba5e0ae525" id="r_a6ed8403758cecd2f762af6ba5e0ae525"><td class="memItemLeft" >&#160;&#160;&#160;&#160;&#160;&#160;<a class="el" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a>&#160;&#160;&#160;<a class="el" href="#a6ed8403758cecd2f762af6ba5e0ae525">message</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a6ed8403758cecd2f762af6ba5e0ae525"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae25f4a1d2a3fa952d052a965376d8fef" id="r_ae25f4a1d2a3fa952d052a965376d8fef"><td class="memItemLeft" >&#160;&#160;&#160;&#160;&#160;&#160;char *&#160;&#160;&#160;<a class="el" href="#ae25f4a1d2a3fa952d052a965376d8fef">destinationName</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:ae25f4a1d2a3fa952d052a965376d8fef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5c41c63d6c37acbe3c493279c5d4c44a" id="r_a5c41c63d6c37acbe3c493279c5d4c44a"><td class="memItemLeft" valign="top">&#160;&#160;&#160;}&#160;&#160;&#160;<a class="el" href="#a5c41c63d6c37acbe3c493279c5d4c44a">pub</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a5c41c63d6c37acbe3c493279c5d4c44a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac73a35b7229f7f4193127cac7b20bc8a" id="r_ac73a35b7229f7f4193127cac7b20bc8a"><td class="memItemLeft" >&#160;&#160;&#160;struct {&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:ac73a35b7229f7f4193127cac7b20bc8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a95309fdf27015b12bc4adf56306e557b" id="r_a95309fdf27015b12bc4adf56306e557b"><td class="memItemLeft" >&#160;&#160;&#160;&#160;&#160;&#160;char *&#160;&#160;&#160;<a class="el" href="#a95309fdf27015b12bc4adf56306e557b">serverURI</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a95309fdf27015b12bc4adf56306e557b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a12d546fd0ccf4e1091b18e1b735c7240" id="r_a12d546fd0ccf4e1091b18e1b735c7240"><td class="memItemLeft" >&#160;&#160;&#160;&#160;&#160;&#160;int&#160;&#160;&#160;<a class="el" href="#a12d546fd0ccf4e1091b18e1b735c7240">MQTTVersion</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a12d546fd0ccf4e1091b18e1b735c7240"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44baf2cb9a0bbcec3ed2eace43f832d1" id="r_a44baf2cb9a0bbcec3ed2eace43f832d1"><td class="memItemLeft" >&#160;&#160;&#160;&#160;&#160;&#160;int&#160;&#160;&#160;<a class="el" href="#a44baf2cb9a0bbcec3ed2eace43f832d1">sessionPresent</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a44baf2cb9a0bbcec3ed2eace43f832d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac73a35b7229f7f4193127cac7b20bc8a" id="r_ac73a35b7229f7f4193127cac7b20bc8a"><td class="memItemLeft" valign="top">&#160;&#160;&#160;}&#160;&#160;&#160;<a class="el" href="#ac73a35b7229f7f4193127cac7b20bc8a">connect</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:ac73a35b7229f7f4193127cac7b20bc8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a46b20b320d6951e567ebf678ea4ac1a3" id="r_a46b20b320d6951e567ebf678ea4ac1a3"><td class="memItemLeft" >&#160;&#160;&#160;struct {&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a46b20b320d6951e567ebf678ea4ac1a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac97316626bd4faa6b71277c221275f4b" id="r_ac97316626bd4faa6b71277c221275f4b"><td class="memItemLeft" >&#160;&#160;&#160;&#160;&#160;&#160;int&#160;&#160;&#160;<a class="el" href="#ac97316626bd4faa6b71277c221275f4b">reasonCodeCount</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:ac97316626bd4faa6b71277c221275f4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2199c9d905dbfa279895cf8123c10f4f" id="r_a2199c9d905dbfa279895cf8123c10f4f"><td class="memItemLeft" >&#160;&#160;&#160;&#160;&#160;&#160;enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> *&#160;&#160;&#160;<a class="el" href="#a2199c9d905dbfa279895cf8123c10f4f">reasonCodes</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a2199c9d905dbfa279895cf8123c10f4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a46b20b320d6951e567ebf678ea4ac1a3" id="r_a46b20b320d6951e567ebf678ea4ac1a3"><td class="memItemLeft" valign="top">&#160;&#160;&#160;}&#160;&#160;&#160;<a class="el" href="#a46b20b320d6951e567ebf678ea4ac1a3">unsub</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a46b20b320d6951e567ebf678ea4ac1a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4bde812772718b8051b0d6e2000a5f5c" id="r_a4bde812772718b8051b0d6e2000a5f5c"><td class="memItemLeft" valign="top">}&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4bde812772718b8051b0d6e2000a5f5c">alt</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a4bde812772718b8051b0d6e2000a5f5c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The data returned on completion of a successful API call in the response callback onSuccess. </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="aa5326df180cb23c59afbcab711a06479" name="aa5326df180cb23c59afbcab711a06479"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa5326df180cb23c59afbcab711a06479">&#9670;&#160;</a></span>struct_id</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char struct_id[4]</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The eyecatcher for this structure. Will be MQSD. </p>

</div>
</div>
<a id="a0761a5e5be0383882e42924de8e51f82" name="a0761a5e5be0383882e42924de8e51f82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0761a5e5be0383882e42924de8e51f82">&#9670;&#160;</a></span>struct_version</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int struct_version</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The version number of this structure. Will be 0 </p>

</div>
</div>
<a id="af8f771e67d284379111151b003c0d810" name="af8f771e67d284379111151b003c0d810"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af8f771e67d284379111151b003c0d810">&#9670;&#160;</a></span>token</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> token</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A token identifying the successful request. Can be used to refer to the request later. </p>

</div>
</div>
<a id="a580d8a8ecb285f5a86c2a3865438f8ee" name="a580d8a8ecb285f5a86c2a3865438f8ee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a580d8a8ecb285f5a86c2a3865438f8ee">&#9670;&#160;</a></span>reasonCode</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reasonCode</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>MQTT V5 reason code returned </p>

</div>
</div>
<a id="a1594008402f7307e4de8fa6131656dde" name="a1594008402f7307e4de8fa6131656dde"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1594008402f7307e4de8fa6131656dde">&#9670;&#160;</a></span>properties</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> properties</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>MQTT V5 properties returned, if any </p>

</div>
</div>
<a id="ac97316626bd4faa6b71277c221275f4b" name="ac97316626bd4faa6b71277c221275f4b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac97316626bd4faa6b71277c221275f4b">&#9670;&#160;</a></span>reasonCodeCount</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int reasonCodeCount</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>the number of reason codes in the reasonCodes array </p>

</div>
</div>
<a id="a2199c9d905dbfa279895cf8123c10f4f" name="a2199c9d905dbfa279895cf8123c10f4f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2199c9d905dbfa279895cf8123c10f4f">&#9670;&#160;</a></span>reasonCodes</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a>* reasonCodes</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>an array of reasonCodes </p>

</div>
</div>
<a id="a26c7d90bad0e00a056dff117b9111346" name="a26c7d90bad0e00a056dff117b9111346"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a26c7d90bad0e00a056dff117b9111346">&#9670;&#160;</a></span>[struct]</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct  { ... }  sub</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>For subscribeMany, the list of reasonCodes returned by the server. </p>

</div>
</div>
<a id="a6ed8403758cecd2f762af6ba5e0ae525" name="a6ed8403758cecd2f762af6ba5e0ae525"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6ed8403758cecd2f762af6ba5e0ae525">&#9670;&#160;</a></span>message</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a> message</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>the message being sent to the server </p>

</div>
</div>
<a id="ae25f4a1d2a3fa952d052a965376d8fef" name="ae25f4a1d2a3fa952d052a965376d8fef"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae25f4a1d2a3fa952d052a965376d8fef">&#9670;&#160;</a></span>destinationName</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char* destinationName</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>the topic destination for the message </p>

</div>
</div>
<a id="a5c41c63d6c37acbe3c493279c5d4c44a" name="a5c41c63d6c37acbe3c493279c5d4c44a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5c41c63d6c37acbe3c493279c5d4c44a">&#9670;&#160;</a></span>[struct]</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct  { ... }  pub</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>For publish, the message being sent to the server. </p>

</div>
</div>
<a id="a95309fdf27015b12bc4adf56306e557b" name="a95309fdf27015b12bc4adf56306e557b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a95309fdf27015b12bc4adf56306e557b">&#9670;&#160;</a></span>serverURI</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char* serverURI</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>the connection string of the server </p>

</div>
</div>
<a id="a12d546fd0ccf4e1091b18e1b735c7240" name="a12d546fd0ccf4e1091b18e1b735c7240"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a12d546fd0ccf4e1091b18e1b735c7240">&#9670;&#160;</a></span>MQTTVersion</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTVersion</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>the version of MQTT being used </p>

</div>
</div>
<a id="a44baf2cb9a0bbcec3ed2eace43f832d1" name="a44baf2cb9a0bbcec3ed2eace43f832d1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a44baf2cb9a0bbcec3ed2eace43f832d1">&#9670;&#160;</a></span>sessionPresent</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int sessionPresent</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>the session present flag returned from the server </p>

</div>
</div>
<a id="ac73a35b7229f7f4193127cac7b20bc8a" name="ac73a35b7229f7f4193127cac7b20bc8a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac73a35b7229f7f4193127cac7b20bc8a">&#9670;&#160;</a></span>[struct]</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct  { ... }  connect</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a46b20b320d6951e567ebf678ea4ac1a3" name="a46b20b320d6951e567ebf678ea4ac1a3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a46b20b320d6951e567ebf678ea4ac1a3">&#9670;&#160;</a></span>[struct]</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct  { ... }  unsub</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>For unsubscribeMany, the list of reasonCodes returned by the server. </p>

</div>
</div>
<a id="a4bde812772718b8051b0d6e2000a5f5c" name="a4bde812772718b8051b0d6e2000a5f5c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4bde812772718b8051b0d6e2000a5f5c">&#9670;&#160;</a></span>[union]</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union  { ... }  alt</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A union of the different values that can be returned for subscribe, unsubscribe and publish. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_m_q_t_t_async_8h_source.html">MQTTAsync.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:08 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
