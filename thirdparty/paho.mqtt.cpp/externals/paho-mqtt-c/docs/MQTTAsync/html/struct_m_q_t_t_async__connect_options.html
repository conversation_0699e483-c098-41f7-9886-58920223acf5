<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: MQTTAsync_connectOptions Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">MQTTAsync_connectOptions Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="_m_q_t_t_async_8h_source.html">MQTTAsync.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:aa5326df180cb23c59afbcab711a06479" id="r_aa5326df180cb23c59afbcab711a06479"><td class="memItemLeft" align="right" valign="top">char&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa5326df180cb23c59afbcab711a06479">struct_id</a> [4]</td></tr>
<tr class="separator:aa5326df180cb23c59afbcab711a06479"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0761a5e5be0383882e42924de8e51f82" id="r_a0761a5e5be0383882e42924de8e51f82"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0761a5e5be0383882e42924de8e51f82">struct_version</a></td></tr>
<tr class="separator:a0761a5e5be0383882e42924de8e51f82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac8dd0930672a9c7d71fc645aa1f0521d" id="r_ac8dd0930672a9c7d71fc645aa1f0521d"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac8dd0930672a9c7d71fc645aa1f0521d">keepAliveInterval</a></td></tr>
<tr class="separator:ac8dd0930672a9c7d71fc645aa1f0521d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a036c36a2a4d3a3ffae9ab4dd8b3e7f7b" id="r_a036c36a2a4d3a3ffae9ab4dd8b3e7f7b"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">cleansession</a></td></tr>
<tr class="separator:a036c36a2a4d3a3ffae9ab4dd8b3e7f7b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5c9d6c557453232a1b25cbbec5a31e8c" id="r_a5c9d6c557453232a1b25cbbec5a31e8c"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5c9d6c557453232a1b25cbbec5a31e8c">maxInflight</a></td></tr>
<tr class="separator:a5c9d6c557453232a1b25cbbec5a31e8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7a9c5105542460d6fd9323facca66648" id="r_a7a9c5105542460d6fd9323facca66648"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_async__will_options.html">MQTTAsync_willOptions</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7a9c5105542460d6fd9323facca66648">will</a></td></tr>
<tr class="separator:a7a9c5105542460d6fd9323facca66648"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba2dfcdfda80edcb531a5a7115d3e043" id="r_aba2dfcdfda80edcb531a5a7115d3e043"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aba2dfcdfda80edcb531a5a7115d3e043">username</a></td></tr>
<tr class="separator:aba2dfcdfda80edcb531a5a7115d3e043"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa4a2ebcb494493f648ae1e6975672575" id="r_aa4a2ebcb494493f648ae1e6975672575"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa4a2ebcb494493f648ae1e6975672575">password</a></td></tr>
<tr class="separator:aa4a2ebcb494493f648ae1e6975672575"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a38c6aa24b36d981c49405db425c24db0" id="r_a38c6aa24b36d981c49405db425c24db0"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a38c6aa24b36d981c49405db425c24db0">connectTimeout</a></td></tr>
<tr class="separator:a38c6aa24b36d981c49405db425c24db0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac73f57846c42bcaa9a47e6721a957748" id="r_ac73f57846c42bcaa9a47e6721a957748"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac73f57846c42bcaa9a47e6721a957748">retryInterval</a></td></tr>
<tr class="separator:ac73f57846c42bcaa9a47e6721a957748"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a86fd59846f3ba2082fd99906c6b496a6" id="r_a86fd59846f3ba2082fd99906c6b496a6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_async___s_s_l_options.html">MQTTAsync_SSLOptions</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a86fd59846f3ba2082fd99906c6b496a6">ssl</a></td></tr>
<tr class="separator:a86fd59846f3ba2082fd99906c6b496a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac13fb68f736854fcab131b34756bfceb" id="r_ac13fb68f736854fcab131b34756bfceb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac13fb68f736854fcab131b34756bfceb">onSuccess</a></td></tr>
<tr class="separator:ac13fb68f736854fcab131b34756bfceb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09ce26d7cff24e14a6844eaae7b15290" id="r_a09ce26d7cff24e14a6844eaae7b15290"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync_onFailure</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a09ce26d7cff24e14a6844eaae7b15290">onFailure</a></td></tr>
<tr class="separator:a09ce26d7cff24e14a6844eaae7b15290"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae376f130b17d169ee51be68077a89ed0" id="r_ae376f130b17d169ee51be68077a89ed0"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae376f130b17d169ee51be68077a89ed0">context</a></td></tr>
<tr class="separator:ae376f130b17d169ee51be68077a89ed0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa82629005937abd92e97084a428cd61f" id="r_aa82629005937abd92e97084a428cd61f"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa82629005937abd92e97084a428cd61f">serverURIcount</a></td></tr>
<tr class="separator:aa82629005937abd92e97084a428cd61f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba22d81c407fb2ba590dba476240d3e9" id="r_aba22d81c407fb2ba590dba476240d3e9"><td class="memItemLeft" align="right" valign="top">char *const  *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aba22d81c407fb2ba590dba476240d3e9">serverURIs</a></td></tr>
<tr class="separator:aba22d81c407fb2ba590dba476240d3e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a12d546fd0ccf4e1091b18e1b735c7240" id="r_a12d546fd0ccf4e1091b18e1b735c7240"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a12d546fd0ccf4e1091b18e1b735c7240">MQTTVersion</a></td></tr>
<tr class="separator:a12d546fd0ccf4e1091b18e1b735c7240"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7902ce4d11b96d8b19582bdd1f82b630" id="r_a7902ce4d11b96d8b19582bdd1f82b630"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7902ce4d11b96d8b19582bdd1f82b630">automaticReconnect</a></td></tr>
<tr class="separator:a7902ce4d11b96d8b19582bdd1f82b630"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a166ac1b967f09326b0187f66be3e69af" id="r_a166ac1b967f09326b0187f66be3e69af"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a166ac1b967f09326b0187f66be3e69af">minRetryInterval</a></td></tr>
<tr class="separator:a166ac1b967f09326b0187f66be3e69af"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a035ba380dd97a284db04f4eaae5e113b" id="r_a035ba380dd97a284db04f4eaae5e113b"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a035ba380dd97a284db04f4eaae5e113b">maxRetryInterval</a></td></tr>
<tr class="separator:a035ba380dd97a284db04f4eaae5e113b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3bccd0957cca80fa2200962051093931" id="r_a3bccd0957cca80fa2200962051093931"><td class="memItemLeft" >struct {&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afed088663f8704004425cdae2120b9b3" id="r_afed088663f8704004425cdae2120b9b3"><td class="memItemLeft" >&#160;&#160;&#160;int&#160;&#160;&#160;<a class="el" href="#afed088663f8704004425cdae2120b9b3">len</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:afed088663f8704004425cdae2120b9b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d49d74db4c035719c3867723cf7e779" id="r_a0d49d74db4c035719c3867723cf7e779"><td class="memItemLeft" >&#160;&#160;&#160;const void *&#160;&#160;&#160;<a class="el" href="#a0d49d74db4c035719c3867723cf7e779">data</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a0d49d74db4c035719c3867723cf7e779"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3bccd0957cca80fa2200962051093931" id="r_a3bccd0957cca80fa2200962051093931"><td class="memItemLeft" valign="top">}&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3bccd0957cca80fa2200962051093931">binarypwd</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a3bccd0957cca80fa2200962051093931"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acdcb75a5d5981da027bce83849140f7b" id="r_acdcb75a5d5981da027bce83849140f7b"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acdcb75a5d5981da027bce83849140f7b">cleanstart</a></td></tr>
<tr class="separator:acdcb75a5d5981da027bce83849140f7b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9f8b7ffb4a698eb151a3b090548b82e8" id="r_a9f8b7ffb4a698eb151a3b090548b82e8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9f8b7ffb4a698eb151a3b090548b82e8">connectProperties</a></td></tr>
<tr class="separator:a9f8b7ffb4a698eb151a3b090548b82e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac31f13e964ffb7e3696caef47ecc0641" id="r_ac31f13e964ffb7e3696caef47ecc0641"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac31f13e964ffb7e3696caef47ecc0641">willProperties</a></td></tr>
<tr class="separator:ac31f13e964ffb7e3696caef47ecc0641"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1c23c490f06428725345de68a4ff0a3e" id="r_a1c23c490f06428725345de68a4ff0a3e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync_onSuccess5</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1c23c490f06428725345de68a4ff0a3e">onSuccess5</a></td></tr>
<tr class="separator:a1c23c490f06428725345de68a4ff0a3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4dad726f2b6f79ca5847689c5f2f2ec2" id="r_a4dad726f2b6f79ca5847689c5f2f2ec2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync_onFailure5</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4dad726f2b6f79ca5847689c5f2f2ec2">onFailure5</a></td></tr>
<tr class="separator:a4dad726f2b6f79ca5847689c5f2f2ec2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac4098248961a1ee89f40353eeebab58b" id="r_ac4098248961a1ee89f40353eeebab58b"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="struct_m_q_t_t_async__name_value.html">MQTTAsync_nameValue</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac4098248961a1ee89f40353eeebab58b">httpHeaders</a></td></tr>
<tr class="separator:ac4098248961a1ee89f40353eeebab58b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add124780ab2de397a96780576c2f112c" id="r_add124780ab2de397a96780576c2f112c"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#add124780ab2de397a96780576c2f112c">httpProxy</a></td></tr>
<tr class="separator:add124780ab2de397a96780576c2f112c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a388b78d8a75658928238f700f207ad92" id="r_a388b78d8a75658928238f700f207ad92"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a388b78d8a75658928238f700f207ad92">httpsProxy</a></td></tr>
<tr class="separator:a388b78d8a75658928238f700f207ad92"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="struct_m_q_t_t_async__connect_options.html">MQTTAsync_connectOptions</a> defines several settings that control the way the client connects to an MQTT server.</p>
<p>Suitable default values are set in the following initializers:</p><ul>
<li>MQTTAsync_connectOptions_initializer: for MQTT 3.1.1 non-WebSockets</li>
<li>MQTTAsync_connectOptions_initializer5: for MQTT 5.0 non-WebSockets</li>
<li>MQTTAsync_connectOptions_initializer_ws: for MQTT 3.1.1 WebSockets</li>
<li>MQTTAsync_connectOptions_initializer5_ws: for MQTT 5.0 WebSockets </li>
</ul>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="aa5326df180cb23c59afbcab711a06479" name="aa5326df180cb23c59afbcab711a06479"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa5326df180cb23c59afbcab711a06479">&#9670;&#160;</a></span>struct_id</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char struct_id[4]</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The eyecatcher for this structure. must be MQTC. </p>

</div>
</div>
<a id="a0761a5e5be0383882e42924de8e51f82" name="a0761a5e5be0383882e42924de8e51f82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0761a5e5be0383882e42924de8e51f82">&#9670;&#160;</a></span>struct_version</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int struct_version</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The version number of this structure. Must be 0, 1, 2, 3 4 5 6, 7 or 8. 0 signifies no SSL options and no serverURIs 1 signifies no serverURIs 2 signifies no MQTTVersion 3 signifies no automatic reconnect options 4 signifies no binary password option (just string) 5 signifies no MQTTV5 properties 6 signifies no HTTP headers option 7 signifies no HTTP proxy and HTTPS proxy options </p>

</div>
</div>
<a id="ac8dd0930672a9c7d71fc645aa1f0521d" name="ac8dd0930672a9c7d71fc645aa1f0521d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac8dd0930672a9c7d71fc645aa1f0521d">&#9670;&#160;</a></span>keepAliveInterval</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int keepAliveInterval</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The "keep alive" interval, measured in seconds, defines the maximum time that should pass without communication between the client and the server The client will ensure that at least one message travels across the network within each keep alive period. In the absence of a data-related message during the time period, the client sends a very small MQTT "ping" message, which the server will acknowledge. The keep alive interval enables the client to detect when the server is no longer available without having to wait for the long TCP/IP timeout. Set to 0 if you do not want any keep alive processing. </p>

</div>
</div>
<a id="a036c36a2a4d3a3ffae9ab4dd8b3e7f7b" name="a036c36a2a4d3a3ffae9ab4dd8b3e7f7b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">&#9670;&#160;</a></span>cleansession</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int cleansession</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a boolean value. The cleansession setting controls the behaviour of both the client and the server at connection and disconnection time. The client and server both maintain session state information. This information is used to ensure "at least once" and "exactly once" delivery, and "exactly once" receipt of messages. Session state also includes subscriptions created by an MQTT client. You can choose to maintain or discard state information between sessions.</p>
<p>When cleansession is true, the state information is discarded at connect and disconnect. Setting cleansession to false keeps the state information. When you connect an MQTT client application with <a class="el" href="_m_q_t_t_async_8h.html#a0388b226a414b09fa733f6d65004ec32">MQTTAsync_connect()</a>, the client identifies the connection using the client identifier and the address of the server. The server checks whether session information for this client has been saved from a previous connection to the server. If a previous session still exists, and cleansession=true, then the previous session information at the client and server is cleared. If cleansession=false, the previous session is resumed. If no previous session exists, a new session is started. </p>

</div>
</div>
<a id="a5c9d6c557453232a1b25cbbec5a31e8c" name="a5c9d6c557453232a1b25cbbec5a31e8c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5c9d6c557453232a1b25cbbec5a31e8c">&#9670;&#160;</a></span>maxInflight</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int maxInflight</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This controls how many messages can be in-flight simultaneously. </p>

</div>
</div>
<a id="a7a9c5105542460d6fd9323facca66648" name="a7a9c5105542460d6fd9323facca66648"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7a9c5105542460d6fd9323facca66648">&#9670;&#160;</a></span>will</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_async__will_options.html">MQTTAsync_willOptions</a>* will</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a pointer to an <a class="el" href="struct_m_q_t_t_async__will_options.html">MQTTAsync_willOptions</a> structure. If your application does not make use of the Last Will and Testament feature, set this pointer to NULL. </p>

</div>
</div>
<a id="aba2dfcdfda80edcb531a5a7115d3e043" name="aba2dfcdfda80edcb531a5a7115d3e043"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aba2dfcdfda80edcb531a5a7115d3e043">&#9670;&#160;</a></span>username</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* username</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>MQTT servers that support the MQTT v3.1 protocol provide authentication and authorisation by user name and password. This is the user name parameter. </p>

</div>
</div>
<a id="aa4a2ebcb494493f648ae1e6975672575" name="aa4a2ebcb494493f648ae1e6975672575"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa4a2ebcb494493f648ae1e6975672575">&#9670;&#160;</a></span>password</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* password</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>MQTT servers that support the MQTT v3.1 protocol provide authentication and authorisation by user name and password. This is the password parameter. </p>

</div>
</div>
<a id="a38c6aa24b36d981c49405db425c24db0" name="a38c6aa24b36d981c49405db425c24db0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a38c6aa24b36d981c49405db425c24db0">&#9670;&#160;</a></span>connectTimeout</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int connectTimeout</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The time interval in seconds to allow a connect to complete. </p>

</div>
</div>
<a id="ac73f57846c42bcaa9a47e6721a957748" name="ac73f57846c42bcaa9a47e6721a957748"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac73f57846c42bcaa9a47e6721a957748">&#9670;&#160;</a></span>retryInterval</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int retryInterval</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The time interval in seconds after which unacknowledged publish requests are retried during a TCP session. With MQTT 3.1.1 and later, retries are not required except on reconnect. 0 turns off in-session retries, and is the recommended setting. Adding retries to an already overloaded network only exacerbates the problem. </p>

</div>
</div>
<a id="a86fd59846f3ba2082fd99906c6b496a6" name="a86fd59846f3ba2082fd99906c6b496a6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a86fd59846f3ba2082fd99906c6b496a6">&#9670;&#160;</a></span>ssl</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_async___s_s_l_options.html">MQTTAsync_SSLOptions</a>* ssl</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a pointer to an <a class="el" href="struct_m_q_t_t_async___s_s_l_options.html">MQTTAsync_SSLOptions</a> structure. If your application does not make use of SSL, set this pointer to NULL. </p>

</div>
</div>
<a id="ac13fb68f736854fcab131b34756bfceb" name="ac13fb68f736854fcab131b34756bfceb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac13fb68f736854fcab131b34756bfceb">&#9670;&#160;</a></span>onSuccess</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess</a>* onSuccess</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A pointer to a callback function to be called if the connect successfully completes. Can be set to NULL, in which case no indication of successful completion will be received. </p>

</div>
</div>
<a id="a09ce26d7cff24e14a6844eaae7b15290" name="a09ce26d7cff24e14a6844eaae7b15290"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a09ce26d7cff24e14a6844eaae7b15290">&#9670;&#160;</a></span>onFailure</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync_onFailure</a>* onFailure</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A pointer to a callback function to be called if the connect fails. Can be set to NULL, in which case no indication of unsuccessful completion will be received. </p>

</div>
</div>
<a id="ae376f130b17d169ee51be68077a89ed0" name="ae376f130b17d169ee51be68077a89ed0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae376f130b17d169ee51be68077a89ed0">&#9670;&#160;</a></span>context</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* context</td>
        </tr>
      </table>
</div><div class="memdoc">
<pre class="fragment">A pointer to any application-specific context. The
</pre><p> the <em>context</em> pointer is passed to success or failure callback functions to provide access to the context information in the callback. </p>

</div>
</div>
<a id="aa82629005937abd92e97084a428cd61f" name="aa82629005937abd92e97084a428cd61f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa82629005937abd92e97084a428cd61f">&#9670;&#160;</a></span>serverURIcount</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int serverURIcount</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The number of entries in the serverURIs array. </p>

</div>
</div>
<a id="aba22d81c407fb2ba590dba476240d3e9" name="aba22d81c407fb2ba590dba476240d3e9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aba22d81c407fb2ba590dba476240d3e9">&#9670;&#160;</a></span>serverURIs</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char* const* serverURIs</td>
        </tr>
      </table>
</div><div class="memdoc">
<pre class="fragment">An array of null-terminated strings specifying the servers to
</pre><p> which the client will connect. Each string takes the form <em>protocol://host:port</em>. <em>protocol</em> must be <em>tcp</em>, <em>ssl</em>, <em>ws</em> or <em>wss</em>. The TLS enabled prefixes (ssl, wss) are only valid if a TLS version of the library is linked with. For <em>host</em>, you can specify either an IP address or a domain name. For instance, to connect to a server running on the local machines with the default MQTT port, specify <em>tcp://localhost:1883</em>. </p>

</div>
</div>
<a id="a12d546fd0ccf4e1091b18e1b735c7240" name="a12d546fd0ccf4e1091b18e1b735c7240"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a12d546fd0ccf4e1091b18e1b735c7240">&#9670;&#160;</a></span>MQTTVersion</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTVersion</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Sets the version of MQTT to be used on the connect. MQTTVERSION_DEFAULT (0) = default: start with 3.1.1, and if that fails, fall back to 3.1 MQTTVERSION_3_1 (3) = only try version 3.1 MQTTVERSION_3_1_1 (4) = only try version 3.1.1 </p>

</div>
</div>
<a id="a7902ce4d11b96d8b19582bdd1f82b630" name="a7902ce4d11b96d8b19582bdd1f82b630"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7902ce4d11b96d8b19582bdd1f82b630">&#9670;&#160;</a></span>automaticReconnect</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int automaticReconnect</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Reconnect automatically in the case of a connection being lost. 0=false, 1=true </p>

</div>
</div>
<a id="a166ac1b967f09326b0187f66be3e69af" name="a166ac1b967f09326b0187f66be3e69af"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a166ac1b967f09326b0187f66be3e69af">&#9670;&#160;</a></span>minRetryInterval</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int minRetryInterval</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The minimum automatic reconnect retry interval in seconds. Doubled on each failed retry. </p>

</div>
</div>
<a id="a035ba380dd97a284db04f4eaae5e113b" name="a035ba380dd97a284db04f4eaae5e113b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a035ba380dd97a284db04f4eaae5e113b">&#9670;&#160;</a></span>maxRetryInterval</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int maxRetryInterval</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The maximum automatic reconnect retry interval in seconds. The doubling stops here on failed retries. </p>

</div>
</div>
<a id="afed088663f8704004425cdae2120b9b3" name="afed088663f8704004425cdae2120b9b3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afed088663f8704004425cdae2120b9b3">&#9670;&#160;</a></span>len</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int len</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>binary password length </p>

</div>
</div>
<a id="a0d49d74db4c035719c3867723cf7e779" name="a0d49d74db4c035719c3867723cf7e779"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0d49d74db4c035719c3867723cf7e779">&#9670;&#160;</a></span>data</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const void* data</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>binary password data </p>

</div>
</div>
<a id="a3bccd0957cca80fa2200962051093931" name="a3bccd0957cca80fa2200962051093931"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3bccd0957cca80fa2200962051093931">&#9670;&#160;</a></span>[struct]</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct  { ... }  binarypwd</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Optional binary password. Only checked and used if the password option is NULL </p>

</div>
</div>
<a id="acdcb75a5d5981da027bce83849140f7b" name="acdcb75a5d5981da027bce83849140f7b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acdcb75a5d5981da027bce83849140f7b">&#9670;&#160;</a></span>cleanstart</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int cleanstart</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9f8b7ffb4a698eb151a3b090548b82e8" name="a9f8b7ffb4a698eb151a3b090548b82e8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9f8b7ffb4a698eb151a3b090548b82e8">&#9670;&#160;</a></span>connectProperties</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* connectProperties</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>MQTT V5 properties for connect </p>

</div>
</div>
<a id="ac31f13e964ffb7e3696caef47ecc0641" name="ac31f13e964ffb7e3696caef47ecc0641"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac31f13e964ffb7e3696caef47ecc0641">&#9670;&#160;</a></span>willProperties</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* willProperties</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>MQTT V5 properties for the will message in the connect </p>

</div>
</div>
<a id="a1c23c490f06428725345de68a4ff0a3e" name="a1c23c490f06428725345de68a4ff0a3e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1c23c490f06428725345de68a4ff0a3e">&#9670;&#160;</a></span>onSuccess5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync_onSuccess5</a>* onSuccess5</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A pointer to a callback function to be called if the connect successfully completes. Can be set to NULL, in which case no indication of successful completion will be received. </p>

</div>
</div>
<a id="a4dad726f2b6f79ca5847689c5f2f2ec2" name="a4dad726f2b6f79ca5847689c5f2f2ec2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4dad726f2b6f79ca5847689c5f2f2ec2">&#9670;&#160;</a></span>onFailure5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync_onFailure5</a>* onFailure5</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A pointer to a callback function to be called if the connect fails. Can be set to NULL, in which case no indication of unsuccessful completion will be received. </p>

</div>
</div>
<a id="ac4098248961a1ee89f40353eeebab58b" name="ac4098248961a1ee89f40353eeebab58b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac4098248961a1ee89f40353eeebab58b">&#9670;&#160;</a></span>httpHeaders</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="struct_m_q_t_t_async__name_value.html">MQTTAsync_nameValue</a>* httpHeaders</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>HTTP headers for websockets </p>

</div>
</div>
<a id="add124780ab2de397a96780576c2f112c" name="add124780ab2de397a96780576c2f112c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#add124780ab2de397a96780576c2f112c">&#9670;&#160;</a></span>httpProxy</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* httpProxy</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The string value of the HTTP proxy. Examples:</p><ul>
<li><a href="http://your.proxy.server:8080/">http://your.proxy.server:8080/</a></li>
<li><a href="http://user:<EMAIL>:8080/">http://user:<EMAIL>:8080/</a> </li>
</ul>

</div>
</div>
<a id="a388b78d8a75658928238f700f207ad92" name="a388b78d8a75658928238f700f207ad92"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a388b78d8a75658928238f700f207ad92">&#9670;&#160;</a></span>httpsProxy</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* httpsProxy</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>HTTPS proxy setting. See <a class="el" href="#add124780ab2de397a96780576c2f112c">MQTTAsync_connectOptions.httpProxy</a> and the section <a class="el" href="_h_t_t_p_proxies.html">HTTP Proxies</a>. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_m_q_t_t_async_8h_source.html">MQTTAsync.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:07 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
