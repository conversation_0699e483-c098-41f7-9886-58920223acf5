<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: MQTTAsync.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">MQTTAsync.h</div></div>
</div><!--header-->
<div class="contents">
<a href="_m_q_t_t_async_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="comment">/*******************************************************************************</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span><span class="comment"> * Copyright (c) 2009, 2025 IBM Corp., Ian Craggs and others</span></div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span><span class="comment"> *</span></div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="comment"> * All rights reserved. This program and the accompanying materials</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span><span class="comment"> * are made available under the terms of the Eclipse Public License v2.0</span></div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span><span class="comment"> * and Eclipse Distribution License v1.0 which accompany this distribution.</span></div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno">    7</span><span class="comment"> *</span></div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span><span class="comment"> * The Eclipse Public License is available at</span></div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno">    9</span><span class="comment"> *    https://www.eclipse.org/legal/epl-2.0/</span></div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno">   10</span><span class="comment"> * and the Eclipse Distribution License is available at</span></div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno">   11</span><span class="comment"> *   http://www.eclipse.org/org/documents/edl-v10.php.</span></div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno">   12</span><span class="comment"> *</span></div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span><span class="comment"> * Contributors:</span></div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno">   14</span><span class="comment"> *    Ian Craggs - initial API and implementation</span></div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span><span class="comment"> *    Ian Craggs, Allan Stockdill-Mander - SSL connections</span></div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno">   16</span><span class="comment"> *    Ian Craggs - multiple server connection support</span></div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno">   17</span><span class="comment"> *    Ian Craggs - MQTT 3.1.1 support</span></div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno">   18</span><span class="comment"> *    Ian Craggs - fix for bug 444103 - success/failure callbacks not invoked</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno">   19</span><span class="comment"> *    Ian Craggs - automatic reconnect and offline buffering (send while disconnected)</span></div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno">   20</span><span class="comment"> *    Ian Craggs - binary will message</span></div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno">   21</span><span class="comment"> *    Ian Craggs - binary password</span></div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno">   22</span><span class="comment"> *    Ian Craggs - remove const on eyecatchers #168</span></div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno">   23</span><span class="comment"> *    Ian Craggs - MQTT 5.0</span></div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno">   24</span><span class="comment"> *******************************************************************************/</span></div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno">   25</span> </div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno">   26</span><span class="comment">/********************************************************************/</span></div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno">   27</span> </div>
<div class="line"><a id="l00088" name="l00088"></a><span class="lineno">   88</span><span class="comment">/*</span></div>
<div class="line"><a id="l00090" name="l00090"></a><span class="lineno">   90</span>*/</div>
<div class="line"><a id="l00091" name="l00091"></a><span class="lineno">   91</span><span class="preprocessor">#if !defined(MQTTASYNC_H)</span></div>
<div class="line"><a id="l00092" name="l00092"></a><span class="lineno">   92</span><span class="preprocessor">#define MQTTASYNC_H</span></div>
<div class="line"><a id="l00093" name="l00093"></a><span class="lineno">   93</span> </div>
<div class="line"><a id="l00094" name="l00094"></a><span class="lineno">   94</span><span class="preprocessor">#if defined(__cplusplus)</span></div>
<div class="line"><a id="l00095" name="l00095"></a><span class="lineno">   95</span> <span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> {</div>
<div class="line"><a id="l00096" name="l00096"></a><span class="lineno">   96</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00097" name="l00097"></a><span class="lineno">   97</span> </div>
<div class="line"><a id="l00098" name="l00098"></a><span class="lineno">   98</span><span class="preprocessor">#include &lt;stdio.h&gt;</span></div>
<div class="line"><a id="l00099" name="l00099"></a><span class="lineno">   99</span><span class="comment">/*</span></div>
<div class="line"><a id="l00101" name="l00101"></a><span class="lineno">  101</span>*/</div>
<div class="line"><a id="l00102" name="l00102"></a><span class="lineno">  102</span> </div>
<div class="line"><a id="l00103" name="l00103"></a><span class="lineno">  103</span><span class="preprocessor">#include &quot;MQTTExportDeclarations.h&quot;</span></div>
<div class="line"><a id="l00104" name="l00104"></a><span class="lineno">  104</span> </div>
<div class="line"><a id="l00105" name="l00105"></a><span class="lineno">  105</span><span class="preprocessor">#include &quot;<a class="code" href="_m_q_t_t_properties_8h.html">MQTTProperties.h</a>&quot;</span></div>
<div class="line"><a id="l00106" name="l00106"></a><span class="lineno">  106</span><span class="preprocessor">#include &quot;<a class="code" href="_m_q_t_t_reason_codes_8h.html">MQTTReasonCodes.h</a>&quot;</span></div>
<div class="line"><a id="l00107" name="l00107"></a><span class="lineno">  107</span><span class="preprocessor">#include &quot;<a class="code" href="_m_q_t_t_subscribe_opts_8h.html">MQTTSubscribeOpts.h</a>&quot;</span></div>
<div class="line"><a id="l00108" name="l00108"></a><span class="lineno">  108</span><span class="preprocessor">#if !defined(NO_PERSISTENCE)</span></div>
<div class="line"><a id="l00109" name="l00109"></a><span class="lineno">  109</span><span class="preprocessor">#include &quot;<a class="code" href="_m_q_t_t_client_persistence_8h.html">MQTTClientPersistence.h</a>&quot;</span></div>
<div class="line"><a id="l00110" name="l00110"></a><span class="lineno">  110</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l00111" name="l00111"></a><span class="lineno">  111</span><span class="preprocessor">#define MQTTCLIENT_PERSISTENCE_NONE 1</span></div>
<div class="line"><a id="l00112" name="l00112"></a><span class="lineno">  112</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00113" name="l00113"></a><span class="lineno">  113</span> </div>
<div class="line"><a id="l00118" name="l00118"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">  118</a></span><span class="preprocessor">#define MQTTASYNC_SUCCESS 0</span></div>
<div class="line"><a id="l00123" name="l00123"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a7c8230fef85fc04b8a1035501f3be406">  123</a></span><span class="preprocessor">#define MQTTASYNC_FAILURE -1</span></div>
<div class="line"><a id="l00124" name="l00124"></a><span class="lineno">  124</span> </div>
<div class="line"><a id="l00125" name="l00125"></a><span class="lineno">  125</span><span class="comment">/* error code -2 is MQTTAsync_PERSISTENCE_ERROR */</span></div>
<div class="line"><a id="l00126" name="l00126"></a><span class="lineno">  126</span> </div>
<div class="line"><a id="l00127" name="l00127"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a4edf1249c75abd4975fec8ddeae2cdc9">  127</a></span><span class="preprocessor">#define MQTTASYNC_PERSISTENCE_ERROR -2</span></div>
<div class="line"><a id="l00128" name="l00128"></a><span class="lineno">  128</span> </div>
<div class="line"><a id="l00132" name="l00132"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a66c0f30b329bc770145c2f04b3929df6">  132</a></span><span class="preprocessor">#define MQTTASYNC_DISCONNECTED -3</span></div>
<div class="line"><a id="l00137" name="l00137"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ad577286d43c72fbc49818aac42f4e24a">  137</a></span><span class="preprocessor">#define MQTTASYNC_MAX_MESSAGES_INFLIGHT -4</span></div>
<div class="line"><a id="l00141" name="l00141"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a80cbe091930c11b67ca719b3e385aa26">  141</a></span><span class="preprocessor">#define MQTTASYNC_BAD_UTF8_STRING -5</span></div>
<div class="line"><a id="l00145" name="l00145"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ab88e1ebcee991099a72429e52a8253fd">  145</a></span><span class="preprocessor">#define MQTTASYNC_NULL_PARAMETER -6</span></div>
<div class="line"><a id="l00151" name="l00151"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a77a7106d97ff60be3fe70f90b1867800">  151</a></span><span class="preprocessor">#define MQTTASYNC_TOPICNAME_TRUNCATED -7</span></div>
<div class="line"><a id="l00156" name="l00156"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a241fc8db46dca132d591bc2be92247ba">  156</a></span><span class="preprocessor">#define MQTTASYNC_BAD_STRUCTURE -8</span></div>
<div class="line"><a id="l00160" name="l00160"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a64d111778ce4e0d3a62808f6db11f224">  160</a></span><span class="preprocessor">#define MQTTASYNC_BAD_QOS -9</span></div>
<div class="line"><a id="l00164" name="l00164"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ab0f54d0bae2c74849022a8009e5d6ff7">  164</a></span><span class="preprocessor">#define MQTTASYNC_NO_MORE_MSGIDS -10</span></div>
<div class="line"><a id="l00168" name="l00168"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#aee1b79d0632bec0fe49eb7ea1abd3b2e">  168</a></span><span class="preprocessor">#define MQTTASYNC_OPERATION_INCOMPLETE -11</span></div>
<div class="line"><a id="l00172" name="l00172"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a4e338072cfd5291b579e4f0c99a6e773">  172</a></span><span class="preprocessor">#define MQTTASYNC_MAX_BUFFERED_MESSAGES -12</span></div>
<div class="line"><a id="l00176" name="l00176"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a6992c00553db1608aef9e162c161d73c">  176</a></span><span class="preprocessor">#define MQTTASYNC_SSL_NOT_SUPPORTED -13</span></div>
<div class="line"><a id="l00187" name="l00187"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a785250cd4a1938ffeeff67b3538abfba">  187</a></span><span class="preprocessor">#define MQTTASYNC_BAD_PROTOCOL -14</span></div>
<div class="line"><a id="l00191" name="l00191"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#af6f97562573876867ba77460a51ca1d1">  191</a></span><span class="preprocessor">#define MQTTASYNC_BAD_MQTT_OPTION -15</span></div>
<div class="line"><a id="l00195" name="l00195"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#af5df806e9767e1e3182fe089a8ee551b">  195</a></span><span class="preprocessor">#define MQTTASYNC_WRONG_MQTT_VERSION -16</span></div>
<div class="line"><a id="l00199" name="l00199"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a47b3aed75983f48a503e1cad6c862004">  199</a></span><span class="preprocessor">#define MQTTASYNC_0_LEN_WILL_TOPIC -17</span></div>
<div class="line"><a id="l00200" name="l00200"></a><span class="lineno">  200</span><span class="comment">/*</span></div>
<div class="line"><a id="l00201" name="l00201"></a><span class="lineno">  201</span><span class="comment"> * Return code: connect or disconnect command ignored because there is already a connect or disconnect</span></div>
<div class="line"><a id="l00202" name="l00202"></a><span class="lineno">  202</span><span class="comment"> * command at the head of the list waiting to be processed. Use the onSuccess/onFailure callbacks to wait</span></div>
<div class="line"><a id="l00203" name="l00203"></a><span class="lineno">  203</span><span class="comment"> * for the previous connect or disconnect command to be complete.</span></div>
<div class="line"><a id="l00204" name="l00204"></a><span class="lineno">  204</span><span class="comment"> */</span></div>
<div class="line"><a id="l00205" name="l00205"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a8278cf4b50dd818c31fa12e45f074b5c">  205</a></span><span class="preprocessor">#define MQTTASYNC_COMMAND_IGNORED -18</span></div>
<div class="line"><a id="l00206" name="l00206"></a><span class="lineno">  206</span> <span class="comment">/*</span></div>
<div class="line"><a id="l00207" name="l00207"></a><span class="lineno">  207</span><span class="comment">  * Return code: maxBufferedMessages in the connect options must be &gt;= 0</span></div>
<div class="line"><a id="l00208" name="l00208"></a><span class="lineno">  208</span><span class="comment">  */</span></div>
<div class="line"><a id="l00209" name="l00209"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a2efee8e190e2c3690c680bde060f78ab">  209</a></span><span class="preprocessor"> #define MQTTASYNC_MAX_BUFFERED -19</span></div>
<div class="line"><a id="l00210" name="l00210"></a><span class="lineno">  210</span> </div>
<div class="line"><a id="l00214" name="l00214"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a75b80b01f98d5a1ffa2a4d42995a8397">  214</a></span><span class="preprocessor">#define MQTTVERSION_DEFAULT 0</span></div>
<div class="line"><a id="l00218" name="l00218"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a4603b988e76872e1f23f135d225ce2fb">  218</a></span><span class="preprocessor">#define MQTTVERSION_3_1 3</span></div>
<div class="line"><a id="l00222" name="l00222"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ac79cc6fdeaa9e3f4ee12c3418898b1ef">  222</a></span><span class="preprocessor">#define MQTTVERSION_3_1_1 4</span></div>
<div class="line"><a id="l00226" name="l00226"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#af8b176fa4d5b89789767ce972338e1e3">  226</a></span><span class="preprocessor">#define MQTTVERSION_5 5</span></div>
<div class="line"><a id="l00230" name="l00230"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ade337b363b7f4bc7c1a7b2858e0380bd">  230</a></span><span class="preprocessor">#define MQTT_BAD_SUBSCRIBE 0x80</span></div>
<div class="line"><a id="l00231" name="l00231"></a><span class="lineno">  231</span> </div>
<div class="line"><a id="l00232" name="l00232"></a><span class="lineno">  232</span> </div>
<div class="foldopen" id="foldopen00236" data-start="{" data-end="};">
<div class="line"><a id="l00236" name="l00236"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__init__options.html">  236</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l00237" name="l00237"></a><span class="lineno">  237</span>{</div>
<div class="line"><a id="l00239" name="l00239"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__init__options.html#aa5326df180cb23c59afbcab711a06479">  239</a></span>        <span class="keywordtype">char</span> struct_id[4];</div>
<div class="line"><a id="l00241" name="l00241"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__init__options.html#a0761a5e5be0383882e42924de8e51f82">  241</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__init__options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a id="l00243" name="l00243"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__init__options.html#a5929146596391e2838ef95feb89776da">  243</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__init__options.html#a5929146596391e2838ef95feb89776da">do_openssl_init</a>;</div>
<div class="line"><a id="l00244" name="l00244"></a><span class="lineno">  244</span>} <a class="code hl_struct" href="struct_m_q_t_t_async__init__options.html">MQTTAsync_init_options</a>;</div>
</div>
<div class="line"><a id="l00245" name="l00245"></a><span class="lineno">  245</span> </div>
<div class="line"><a id="l00246" name="l00246"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a866e023f70141969d48597930c0ee313">  246</a></span><span class="preprocessor">#define MQTTAsync_init_options_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;G&#39;}, 0, 0 }</span></div>
<div class="line"><a id="l00247" name="l00247"></a><span class="lineno">  247</span> </div>
<div class="line"><a id="l00252" name="l00252"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a1705e75a48999cb45bf85c15608478f5">  252</a></span>LIBMQTT_API <span class="keywordtype">void</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#a1705e75a48999cb45bf85c15608478f5">MQTTAsync_global_init</a>(<a class="code hl_struct" href="struct_m_q_t_t_async__init__options.html">MQTTAsync_init_options</a>* inits);</div>
<div class="line"><a id="l00253" name="l00253"></a><span class="lineno">  253</span> </div>
<div class="line"><a id="l00258" name="l00258"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">  258</a></span><span class="keyword">typedef</span> <span class="keywordtype">void</span>* <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>;</div>
<div class="line"><a id="l00268" name="l00268"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">  268</a></span><span class="keyword">typedef</span> <span class="keywordtype">int</span> <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a>;</div>
<div class="line"><a id="l00269" name="l00269"></a><span class="lineno">  269</span> </div>
<div class="foldopen" id="foldopen00276" data-start="{" data-end="};">
<div class="line"><a id="l00276" name="l00276"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html">  276</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l00277" name="l00277"></a><span class="lineno">  277</span>{</div>
<div class="line"><a id="l00279" name="l00279"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html#aa5326df180cb23c59afbcab711a06479">  279</a></span>        <span class="keywordtype">char</span> struct_id[4];</div>
<div class="line"><a id="l00282" name="l00282"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html#a0761a5e5be0383882e42924de8e51f82">  282</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__message.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a id="l00284" name="l00284"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html#aa3cb44feb3ae6d11b3a4cad2d94cb33a">  284</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__message.html#aa3cb44feb3ae6d11b3a4cad2d94cb33a">payloadlen</a>;</div>
<div class="line"><a id="l00286" name="l00286"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html#a9eff55064941fb604452abb0050ea99d">  286</a></span>        <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__message.html#a9eff55064941fb604452abb0050ea99d">payload</a>;</div>
<div class="line"><a id="l00300" name="l00300"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html#a35738099155a0e4f54050da474bab2e7">  300</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__message.html#a35738099155a0e4f54050da474bab2e7">qos</a>;</div>
<div class="line"><a id="l00319" name="l00319"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html#a6a4904c112507a43e7dc8495b62cc0fc">  319</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__message.html#a6a4904c112507a43e7dc8495b62cc0fc">retained</a>;</div>
<div class="line"><a id="l00326" name="l00326"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html#adc4cf3f551bb367858644559d69cfdf5">  326</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__message.html#adc4cf3f551bb367858644559d69cfdf5">dup</a>;</div>
<div class="line"><a id="l00332" name="l00332"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html#a6174c42da8c55c86e7255be2848dc4ac">  332</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__message.html#a6174c42da8c55c86e7255be2848dc4ac">msgid</a>;</div>
<div class="line"><a id="l00336" name="l00336"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html#a1594008402f7307e4de8fa6131656dde">  336</a></span>        <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a> <a class="code hl_variable" href="struct_m_q_t_t_async__message.html#a1594008402f7307e4de8fa6131656dde">properties</a>;</div>
<div class="line"><a id="l00337" name="l00337"></a><span class="lineno">  337</span>} <a class="code hl_struct" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a>;</div>
</div>
<div class="line"><a id="l00338" name="l00338"></a><span class="lineno">  338</span> </div>
<div class="line"><a id="l00339" name="l00339"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a6a85061dadab532f28e96e5ab3c600e9">  339</a></span><span class="preprocessor">#define MQTTAsync_message_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;M&#39;}, 1, 0, NULL, 0, 0, 0, 0, MQTTProperties_initializer }</span></div>
<div class="line"><a id="l00340" name="l00340"></a><span class="lineno">  340</span> </div>
<div class="line"><a id="l00374" name="l00374"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">  374</a></span><span class="keyword">typedef</span> <span class="keywordtype">int</span> <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync_messageArrived</a>(<span class="keywordtype">void</span>* context, <span class="keywordtype">char</span>* topicName, <span class="keywordtype">int</span> topicLen, <a class="code hl_struct" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a>* message);</div>
<div class="line"><a id="l00375" name="l00375"></a><span class="lineno">  375</span> </div>
<div class="line"><a id="l00397" name="l00397"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">  397</a></span><span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync_deliveryComplete</a>(<span class="keywordtype">void</span>* context, <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> token);</div>
<div class="line"><a id="l00398" name="l00398"></a><span class="lineno">  398</span> </div>
<div class="line"><a id="l00417" name="l00417"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">  417</a></span><span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">MQTTAsync_connectionLost</a>(<span class="keywordtype">void</span>* context, <span class="keywordtype">char</span>* cause);</div>
<div class="line"><a id="l00418" name="l00418"></a><span class="lineno">  418</span> </div>
<div class="line"><a id="l00419" name="l00419"></a><span class="lineno">  419</span> </div>
<div class="line"><a id="l00435" name="l00435"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a34bb8d321e9d368780b5c832c058f223">  435</a></span><span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a34bb8d321e9d368780b5c832c058f223">MQTTAsync_connected</a>(<span class="keywordtype">void</span>* context, <span class="keywordtype">char</span>* cause);</div>
<div class="line"><a id="l00436" name="l00436"></a><span class="lineno">  436</span> </div>
<div class="line"><a id="l00448" name="l00448"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a52a1d9ab6e5d5064a3de42d0eec88f57">  448</a></span><span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a52a1d9ab6e5d5064a3de42d0eec88f57">MQTTAsync_disconnected</a>(<span class="keywordtype">void</span>* context, <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* properties,</div>
<div class="line"><a id="l00449" name="l00449"></a><span class="lineno">  449</span>                <span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reasonCode);</div>
<div class="line"><a id="l00450" name="l00450"></a><span class="lineno">  450</span> </div>
<div class="line"><a id="l00466" name="l00466"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ada4dd26d23c8849c51e4ab8200339040">  466</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#ada4dd26d23c8849c51e4ab8200339040">MQTTAsync_setDisconnected</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">void</span>* context, <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a52a1d9ab6e5d5064a3de42d0eec88f57">MQTTAsync_disconnected</a>* co);</div>
<div class="line"><a id="l00467" name="l00467"></a><span class="lineno">  467</span> </div>
<div class="foldopen" id="foldopen00469" data-start="{" data-end="};">
<div class="line"><a id="l00469" name="l00469"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_data.html">  469</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l00470" name="l00470"></a><span class="lineno">  470</span>{</div>
<div class="line"><a id="l00472" name="l00472"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_data.html#aa5326df180cb23c59afbcab711a06479">  472</a></span>        <span class="keywordtype">char</span> struct_id[4];</div>
<div class="line"><a id="l00474" name="l00474"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_data.html#a0761a5e5be0383882e42924de8e51f82">  474</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__connect_data.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a id="l00481" name="l00481"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_data.html#aba2dfcdfda80edcb531a5a7115d3e043">  481</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__connect_data.html#aba2dfcdfda80edcb531a5a7115d3e043">username</a>;</div>
<div class="line"><a id="l00487" name="l00487"></a><span class="lineno">  487</span>        <span class="keyword">struct </span>{</div>
<div class="line"><a id="l00488" name="l00488"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_data.html#afed088663f8704004425cdae2120b9b3">  488</a></span>                <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__connect_data.html#afed088663f8704004425cdae2120b9b3">len</a>;           </div>
<div class="line"><a id="l00489" name="l00489"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_data.html#a0d49d74db4c035719c3867723cf7e779">  489</a></span>                <span class="keyword">const</span> <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__connect_data.html#a0d49d74db4c035719c3867723cf7e779">data</a>;  </div>
<div class="line"><a id="l00490" name="l00490"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_data.html#ad5c523e5e6dc0105cc7b4a296451915b">  490</a></span>        } binarypwd;</div>
<div class="line"><a id="l00491" name="l00491"></a><span class="lineno">  491</span>} <a class="code hl_struct" href="struct_m_q_t_t_async__connect_data.html">MQTTAsync_connectData</a>;</div>
</div>
<div class="line"><a id="l00492" name="l00492"></a><span class="lineno">  492</span> </div>
<div class="line"><a id="l00493" name="l00493"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a2e415e68016ae56f6bbbbdc9840a9c6e">  493</a></span><span class="preprocessor">#define MQTTAsync_connectData_initializer {{&#39;M&#39;, &#39;Q&#39;, &#39;C&#39;, &#39;D&#39;}, 0, NULL, {0, NULL}}</span></div>
<div class="line"><a id="l00494" name="l00494"></a><span class="lineno">  494</span> </div>
<div class="line"><a id="l00501" name="l00501"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5e44304a2c011a7d61b72c779ad83979">  501</a></span><span class="keyword">typedef</span> <span class="keywordtype">int</span> <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a5e44304a2c011a7d61b72c779ad83979">MQTTAsync_updateConnectOptions</a>(<span class="keywordtype">void</span>* context, <a class="code hl_struct" href="struct_m_q_t_t_async__connect_data.html">MQTTAsync_connectData</a>* data);</div>
<div class="line"><a id="l00502" name="l00502"></a><span class="lineno">  502</span> </div>
<div class="line"><a id="l00512" name="l00512"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#aa078aec3eba83481f63db3c3939a5da9">  512</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#aa078aec3eba83481f63db3c3939a5da9">MQTTAsync_setUpdateConnectOptions</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">void</span>* context, <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a5e44304a2c011a7d61b72c779ad83979">MQTTAsync_updateConnectOptions</a>* co);</div>
<div class="line"><a id="l00513" name="l00513"></a><span class="lineno">  513</span> </div>
<div class="line"><a id="l00514" name="l00514"></a><span class="lineno">  514</span><span class="preprocessor">#if !defined(NO_PERSISTENCE)</span></div>
<div class="line"><a id="l00524" name="l00524"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a1002b09c62a096578c9b3e0135eb98c1">  524</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#a1002b09c62a096578c9b3e0135eb98c1">MQTTAsync_setBeforePersistenceWrite</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">void</span>* context, <a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#ab865640a1cc53b68622004c5a2d29fae">MQTTPersistence_beforeWrite</a>* co);</div>
<div class="line"><a id="l00525" name="l00525"></a><span class="lineno">  525</span> </div>
<div class="line"><a id="l00526" name="l00526"></a><span class="lineno">  526</span> </div>
<div class="line"><a id="l00536" name="l00536"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ab4d16e3c57502be6a7d1b1d3bcc382f3">  536</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#ab4d16e3c57502be6a7d1b1d3bcc382f3">MQTTAsync_setAfterPersistenceRead</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">void</span>* context, <a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#af5a966a574c6ad7a35f1ebb7edd5c1c4">MQTTPersistence_afterRead</a>* co);</div>
<div class="line"><a id="l00537" name="l00537"></a><span class="lineno">  537</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00538" name="l00538"></a><span class="lineno">  538</span> </div>
<div class="foldopen" id="foldopen00540" data-start="{" data-end="};">
<div class="line"><a id="l00540" name="l00540"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data.html">  540</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l00541" name="l00541"></a><span class="lineno">  541</span>{</div>
<div class="line"><a id="l00543" name="l00543"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data.html#af8f771e67d284379111151b003c0d810">  543</a></span>        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> <a class="code hl_variable" href="struct_m_q_t_t_async__failure_data.html#af8f771e67d284379111151b003c0d810">token</a>;</div>
<div class="line"><a id="l00545" name="l00545"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data.html#a45a5b7c00a796a23f01673cef1dbe0a9">  545</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__failure_data.html#a45a5b7c00a796a23f01673cef1dbe0a9">code</a>;</div>
<div class="line"><a id="l00547" name="l00547"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data.html#a254bf0858da09c96a48daf64404eb4f8">  547</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span> *<a class="code hl_variable" href="struct_m_q_t_t_async__failure_data.html#a254bf0858da09c96a48daf64404eb4f8">message</a>;</div>
<div class="line"><a id="l00548" name="l00548"></a><span class="lineno">  548</span>} <a class="code hl_struct" href="struct_m_q_t_t_async__failure_data.html">MQTTAsync_failureData</a>;</div>
</div>
<div class="line"><a id="l00549" name="l00549"></a><span class="lineno">  549</span> </div>
<div class="line"><a id="l00550" name="l00550"></a><span class="lineno">  550</span> </div>
<div class="foldopen" id="foldopen00552" data-start="{" data-end="};">
<div class="line"><a id="l00552" name="l00552"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data5.html">  552</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l00553" name="l00553"></a><span class="lineno">  553</span>{</div>
<div class="line"><a id="l00555" name="l00555"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data5.html#aa5326df180cb23c59afbcab711a06479">  555</a></span>        <span class="keywordtype">char</span> struct_id[4];</div>
<div class="line"><a id="l00557" name="l00557"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data5.html#a0761a5e5be0383882e42924de8e51f82">  557</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__failure_data5.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a id="l00559" name="l00559"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data5.html#af8f771e67d284379111151b003c0d810">  559</a></span>        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> <a class="code hl_variable" href="struct_m_q_t_t_async__failure_data5.html#af8f771e67d284379111151b003c0d810">token</a>;</div>
<div class="line"><a id="l00561" name="l00561"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data5.html#a580d8a8ecb285f5a86c2a3865438f8ee">  561</a></span>        <span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> <a class="code hl_variable" href="struct_m_q_t_t_async__failure_data5.html#a580d8a8ecb285f5a86c2a3865438f8ee">reasonCode</a>;</div>
<div class="line"><a id="l00563" name="l00563"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data5.html#a1594008402f7307e4de8fa6131656dde">  563</a></span>        <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a> <a class="code hl_variable" href="struct_m_q_t_t_async__failure_data5.html#a1594008402f7307e4de8fa6131656dde">properties</a>;</div>
<div class="line"><a id="l00565" name="l00565"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data5.html#a45a5b7c00a796a23f01673cef1dbe0a9">  565</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__failure_data5.html#a45a5b7c00a796a23f01673cef1dbe0a9">code</a>;</div>
<div class="line"><a id="l00567" name="l00567"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data5.html#a254bf0858da09c96a48daf64404eb4f8">  567</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span> *<a class="code hl_variable" href="struct_m_q_t_t_async__failure_data5.html#a254bf0858da09c96a48daf64404eb4f8">message</a>;</div>
<div class="line"><a id="l00569" name="l00569"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data5.html#a38dfee9f038f473c95af46fcef5dd3e9">  569</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__failure_data5.html#a38dfee9f038f473c95af46fcef5dd3e9">packet_type</a>;</div>
<div class="line"><a id="l00570" name="l00570"></a><span class="lineno">  570</span>} <a class="code hl_struct" href="struct_m_q_t_t_async__failure_data5.html">MQTTAsync_failureData5</a>;</div>
</div>
<div class="line"><a id="l00571" name="l00571"></a><span class="lineno">  571</span> </div>
<div class="line"><a id="l00572" name="l00572"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a53ce2002ae2c2579575bb41c48c51c29">  572</a></span><span class="preprocessor">#define MQTTAsync_failureData5_initializer {{&#39;M&#39;, &#39;Q&#39;, &#39;F&#39;, &#39;D&#39;}, 0, 0, MQTTREASONCODE_SUCCESS, MQTTProperties_initializer, 0, NULL, 0}</span></div>
<div class="line"><a id="l00573" name="l00573"></a><span class="lineno">  573</span> </div>
<div class="foldopen" id="foldopen00575" data-start="{" data-end="};">
<div class="line"><a id="l00575" name="l00575"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html">  575</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l00576" name="l00576"></a><span class="lineno">  576</span>{</div>
<div class="line"><a id="l00578" name="l00578"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#af8f771e67d284379111151b003c0d810">  578</a></span>        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> <a class="code hl_variable" href="struct_m_q_t_t_async__success_data.html#af8f771e67d284379111151b003c0d810">token</a>;</div>
<div class="line"><a id="l00580" name="l00580"></a><span class="lineno">  580</span>        <span class="keyword">union</span></div>
<div class="line"><a id="l00581" name="l00581"></a><span class="lineno">  581</span>        {</div>
<div class="line"><a id="l00584" name="l00584"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#a35738099155a0e4f54050da474bab2e7">  584</a></span>                <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__success_data.html#a35738099155a0e4f54050da474bab2e7">qos</a>;</div>
<div class="line"><a id="l00587" name="l00587"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#a82786d9ba5cae39873f378a48b36c23b">  587</a></span>                <span class="keywordtype">int</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__success_data.html#a82786d9ba5cae39873f378a48b36c23b">qosList</a>;</div>
<div class="line"><a id="l00589" name="l00589"></a><span class="lineno">  589</span>                <span class="keyword">struct</span></div>
<div class="line"><a id="l00590" name="l00590"></a><span class="lineno">  590</span>                {</div>
<div class="line"><a id="l00591" name="l00591"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#a6ed8403758cecd2f762af6ba5e0ae525">  591</a></span>                        <a class="code hl_struct" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a> <a class="code hl_variable" href="struct_m_q_t_t_async__success_data.html#a6ed8403758cecd2f762af6ba5e0ae525">message</a>; </div>
<div class="line"><a id="l00592" name="l00592"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#ae25f4a1d2a3fa952d052a965376d8fef">  592</a></span>                        <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__success_data.html#ae25f4a1d2a3fa952d052a965376d8fef">destinationName</a>;     </div>
<div class="line"><a id="l00593" name="l00593"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#a7f044c3b839c17a8840cd6f00d7a0e90">  593</a></span>                } pub;</div>
<div class="line"><a id="l00594" name="l00594"></a><span class="lineno">  594</span>                <span class="comment">/* For connect, the server connected to, MQTT version used, and sessionPresent flag */</span></div>
<div class="line"><a id="l00595" name="l00595"></a><span class="lineno">  595</span>                <span class="keyword">struct</span></div>
<div class="line"><a id="l00596" name="l00596"></a><span class="lineno">  596</span>                {</div>
<div class="line"><a id="l00597" name="l00597"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#a95309fdf27015b12bc4adf56306e557b">  597</a></span>                        <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__success_data.html#a95309fdf27015b12bc4adf56306e557b">serverURI</a>; </div>
<div class="line"><a id="l00598" name="l00598"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#a12d546fd0ccf4e1091b18e1b735c7240">  598</a></span>                        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__success_data.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTVersion</a>; </div>
<div class="line"><a id="l00599" name="l00599"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#a44baf2cb9a0bbcec3ed2eace43f832d1">  599</a></span>                        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__success_data.html#a44baf2cb9a0bbcec3ed2eace43f832d1">sessionPresent</a>; </div>
<div class="line"><a id="l00600" name="l00600"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#a028701cd79a4923d1d2172422c022447">  600</a></span>                } connect;</div>
<div class="line"><a id="l00601" name="l00601"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#afbc1fee4467369fefa30cb07047fca14">  601</a></span>        } alt;</div>
<div class="line"><a id="l00602" name="l00602"></a><span class="lineno">  602</span>} <a class="code hl_struct" href="struct_m_q_t_t_async__success_data.html">MQTTAsync_successData</a>;</div>
</div>
<div class="line"><a id="l00603" name="l00603"></a><span class="lineno">  603</span> </div>
<div class="line"><a id="l00604" name="l00604"></a><span class="lineno">  604</span> </div>
<div class="foldopen" id="foldopen00606" data-start="{" data-end="};">
<div class="line"><a id="l00606" name="l00606"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html">  606</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l00607" name="l00607"></a><span class="lineno">  607</span>{</div>
<div class="line"><a id="l00608" name="l00608"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#aa5326df180cb23c59afbcab711a06479">  608</a></span>        <span class="keywordtype">char</span> struct_id[4];      </div>
<div class="line"><a id="l00609" name="l00609"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a0761a5e5be0383882e42924de8e51f82">  609</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__success_data5.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;     </div>
<div class="line"><a id="l00611" name="l00611"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#af8f771e67d284379111151b003c0d810">  611</a></span>        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> <a class="code hl_variable" href="struct_m_q_t_t_async__success_data5.html#af8f771e67d284379111151b003c0d810">token</a>;</div>
<div class="line"><a id="l00612" name="l00612"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a580d8a8ecb285f5a86c2a3865438f8ee">  612</a></span>        <span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> <a class="code hl_variable" href="struct_m_q_t_t_async__success_data5.html#a580d8a8ecb285f5a86c2a3865438f8ee">reasonCode</a>;        </div>
<div class="line"><a id="l00613" name="l00613"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a1594008402f7307e4de8fa6131656dde">  613</a></span>        <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a> <a class="code hl_variable" href="struct_m_q_t_t_async__success_data5.html#a1594008402f7307e4de8fa6131656dde">properties</a>;              </div>
<div class="line"><a id="l00615" name="l00615"></a><span class="lineno">  615</span>        <span class="keyword">union</span></div>
<div class="line"><a id="l00616" name="l00616"></a><span class="lineno">  616</span>        {</div>
<div class="line"><a id="l00618" name="l00618"></a><span class="lineno">  618</span>                <span class="keyword">struct</span></div>
<div class="line"><a id="l00619" name="l00619"></a><span class="lineno">  619</span>                {</div>
<div class="line"><a id="l00620" name="l00620"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#ac97316626bd4faa6b71277c221275f4b">  620</a></span>                        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__success_data5.html#ac97316626bd4faa6b71277c221275f4b">reasonCodeCount</a>; </div>
<div class="line"><a id="l00621" name="l00621"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a2199c9d905dbfa279895cf8123c10f4f">  621</a></span>                        <span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a>* <a class="code hl_variable" href="struct_m_q_t_t_async__success_data5.html#a2199c9d905dbfa279895cf8123c10f4f">reasonCodes</a>; </div>
<div class="line"><a id="l00622" name="l00622"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a26c7d90bad0e00a056dff117b9111346">  622</a></span>                } sub;</div>
<div class="line"><a id="l00624" name="l00624"></a><span class="lineno">  624</span>                <span class="keyword">struct</span></div>
<div class="line"><a id="l00625" name="l00625"></a><span class="lineno">  625</span>                {</div>
<div class="line"><a id="l00626" name="l00626"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a6ed8403758cecd2f762af6ba5e0ae525">  626</a></span>                        <a class="code hl_struct" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a> <a class="code hl_variable" href="struct_m_q_t_t_async__success_data5.html#a6ed8403758cecd2f762af6ba5e0ae525">message</a>; </div>
<div class="line"><a id="l00627" name="l00627"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#ae25f4a1d2a3fa952d052a965376d8fef">  627</a></span>                        <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__success_data5.html#ae25f4a1d2a3fa952d052a965376d8fef">destinationName</a>;     </div>
<div class="line"><a id="l00628" name="l00628"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a5c41c63d6c37acbe3c493279c5d4c44a">  628</a></span>                } pub;</div>
<div class="line"><a id="l00629" name="l00629"></a><span class="lineno">  629</span>                <span class="comment">/* For connect, the server connected to, MQTT version used, and sessionPresent flag */</span></div>
<div class="line"><a id="l00630" name="l00630"></a><span class="lineno">  630</span>                <span class="keyword">struct</span></div>
<div class="line"><a id="l00631" name="l00631"></a><span class="lineno">  631</span>                {</div>
<div class="line"><a id="l00632" name="l00632"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a95309fdf27015b12bc4adf56306e557b">  632</a></span>                        <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__success_data5.html#a95309fdf27015b12bc4adf56306e557b">serverURI</a>;  </div>
<div class="line"><a id="l00633" name="l00633"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a12d546fd0ccf4e1091b18e1b735c7240">  633</a></span>                        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__success_data5.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTVersion</a>;  </div>
<div class="line"><a id="l00634" name="l00634"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a44baf2cb9a0bbcec3ed2eace43f832d1">  634</a></span>                        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__success_data5.html#a44baf2cb9a0bbcec3ed2eace43f832d1">sessionPresent</a>;  </div>
<div class="line"><a id="l00635" name="l00635"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#ac73a35b7229f7f4193127cac7b20bc8a">  635</a></span>                } connect;</div>
<div class="line"><a id="l00637" name="l00637"></a><span class="lineno">  637</span>                <span class="keyword">struct</span></div>
<div class="line"><a id="l00638" name="l00638"></a><span class="lineno">  638</span>                {</div>
<div class="line"><a id="l00639" name="l00639"></a><span class="lineno">  639</span>                        <span class="keywordtype">int</span> reasonCodeCount; </div>
<div class="line"><a id="l00640" name="l00640"></a><span class="lineno">  640</span>                        <span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a>* reasonCodes; </div>
<div class="line"><a id="l00641" name="l00641"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a46b20b320d6951e567ebf678ea4ac1a3">  641</a></span>                } unsub;</div>
<div class="line"><a id="l00642" name="l00642"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a4bde812772718b8051b0d6e2000a5f5c">  642</a></span>        } alt;</div>
<div class="line"><a id="l00643" name="l00643"></a><span class="lineno">  643</span>} <a class="code hl_struct" href="struct_m_q_t_t_async__success_data5.html">MQTTAsync_successData5</a>;</div>
</div>
<div class="line"><a id="l00644" name="l00644"></a><span class="lineno">  644</span> </div>
<div class="line"><a id="l00645" name="l00645"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a6182ec90ec4a134465f627b324ac5a41">  645</a></span><span class="preprocessor">#define MQTTAsync_successData5_initializer {{&#39;M&#39;, &#39;Q&#39;, &#39;S&#39;, &#39;D&#39;}, 0, 0, MQTTREASONCODE_SUCCESS, MQTTProperties_initializer, {.sub={0,0}}}</span></div>
<div class="line"><a id="l00646" name="l00646"></a><span class="lineno">  646</span> </div>
<div class="line"><a id="l00660" name="l00660"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">  660</a></span><span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess</a>(<span class="keywordtype">void</span>* context, <a class="code hl_struct" href="struct_m_q_t_t_async__success_data.html">MQTTAsync_successData</a>* response);</div>
<div class="line"><a id="l00661" name="l00661"></a><span class="lineno">  661</span> </div>
<div class="line"><a id="l00676" name="l00676"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">  676</a></span><span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync_onSuccess5</a>(<span class="keywordtype">void</span>* context, <a class="code hl_struct" href="struct_m_q_t_t_async__success_data5.html">MQTTAsync_successData5</a>* response);</div>
<div class="line"><a id="l00677" name="l00677"></a><span class="lineno">  677</span> </div>
<div class="line"><a id="l00691" name="l00691"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">  691</a></span><span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync_onFailure</a>(<span class="keywordtype">void</span>* context,  <a class="code hl_struct" href="struct_m_q_t_t_async__failure_data.html">MQTTAsync_failureData</a>* response);</div>
<div class="line"><a id="l00692" name="l00692"></a><span class="lineno">  692</span> </div>
<div class="line"><a id="l00706" name="l00706"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">  706</a></span><span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync_onFailure5</a>(<span class="keywordtype">void</span>* context,  <a class="code hl_struct" href="struct_m_q_t_t_async__failure_data5.html">MQTTAsync_failureData5</a>* response);</div>
<div class="line"><a id="l00707" name="l00707"></a><span class="lineno">  707</span> </div>
<div class="foldopen" id="foldopen00713" data-start="{" data-end="};">
<div class="line"><a id="l00713" name="l00713"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html">  713</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a></div>
<div class="line"><a id="l00714" name="l00714"></a><span class="lineno">  714</span>{</div>
<div class="line"><a id="l00716" name="l00716"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#aa5326df180cb23c59afbcab711a06479">  716</a></span>        <span class="keywordtype">char</span> <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#aa5326df180cb23c59afbcab711a06479">struct_id</a>[4];</div>
<div class="line"><a id="l00719" name="l00719"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#a0761a5e5be0383882e42924de8e51f82">  719</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a id="l00725" name="l00725"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#ac13fb68f736854fcab131b34756bfceb">  725</a></span>        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess</a>* <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#ac13fb68f736854fcab131b34756bfceb">onSuccess</a>;</div>
<div class="line"><a id="l00731" name="l00731"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#a09ce26d7cff24e14a6844eaae7b15290">  731</a></span>        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync_onFailure</a>* <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#a09ce26d7cff24e14a6844eaae7b15290">onFailure</a>;</div>
<div class="line"><a id="l00737" name="l00737"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#ae376f130b17d169ee51be68077a89ed0">  737</a></span>        <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#ae376f130b17d169ee51be68077a89ed0">context</a>;</div>
<div class="line"><a id="l00744" name="l00744"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#af8f771e67d284379111151b003c0d810">  744</a></span>        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#af8f771e67d284379111151b003c0d810">token</a>;</div>
<div class="line"><a id="l00750" name="l00750"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#a1c23c490f06428725345de68a4ff0a3e">  750</a></span>        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync_onSuccess5</a>* <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#a1c23c490f06428725345de68a4ff0a3e">onSuccess5</a>;</div>
<div class="line"><a id="l00756" name="l00756"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#a4dad726f2b6f79ca5847689c5f2f2ec2">  756</a></span>        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync_onFailure5</a>* <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#a4dad726f2b6f79ca5847689c5f2f2ec2">onFailure5</a>;</div>
<div class="line"><a id="l00760" name="l00760"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#a1594008402f7307e4de8fa6131656dde">  760</a></span>        <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a> <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#a1594008402f7307e4de8fa6131656dde">properties</a>;</div>
<div class="line"><a id="l00761" name="l00761"></a><span class="lineno">  761</span>        <span class="comment">/*</span></div>
<div class="line"><a id="l00762" name="l00762"></a><span class="lineno">  762</span><span class="comment">         * MQTT V5 subscribe options, when used with subscribe only.</span></div>
<div class="line"><a id="l00763" name="l00763"></a><span class="lineno">  763</span><span class="comment">         */</span></div>
<div class="line"><a id="l00764" name="l00764"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#a16a3cd2a8c69669e9ed6e420ccd9c517">  764</a></span>        <a class="code hl_struct" href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a> <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#a16a3cd2a8c69669e9ed6e420ccd9c517">subscribeOptions</a>;</div>
<div class="line"><a id="l00765" name="l00765"></a><span class="lineno">  765</span>        <span class="comment">/*</span></div>
<div class="line"><a id="l00766" name="l00766"></a><span class="lineno">  766</span><span class="comment">         * MQTT V5 subscribe option count, when used with subscribeMany only.</span></div>
<div class="line"><a id="l00767" name="l00767"></a><span class="lineno">  767</span><span class="comment">         * The number of entries in the subscribe_options_list array.</span></div>
<div class="line"><a id="l00768" name="l00768"></a><span class="lineno">  768</span><span class="comment">         */</span></div>
<div class="line"><a id="l00769" name="l00769"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#a1a4b9bb2780472ec7bb65d0df1bf5d26">  769</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#a1a4b9bb2780472ec7bb65d0df1bf5d26">subscribeOptionsCount</a>;</div>
<div class="line"><a id="l00770" name="l00770"></a><span class="lineno">  770</span>        <span class="comment">/*</span></div>
<div class="line"><a id="l00771" name="l00771"></a><span class="lineno">  771</span><span class="comment">         * MQTT V5 subscribe option array, when used with subscribeMany only.</span></div>
<div class="line"><a id="l00772" name="l00772"></a><span class="lineno">  772</span><span class="comment">         */</span></div>
<div class="line"><a id="l00773" name="l00773"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#a98f71c5d03dc5ee86fd9dc0119ccb961">  773</a></span>        <a class="code hl_struct" href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a>* <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#a98f71c5d03dc5ee86fd9dc0119ccb961">subscribeOptionsList</a>;</div>
<div class="line"><a id="l00774" name="l00774"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ae1568d96d6418004cc79466c06f3d791">  774</a></span>} <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#ae1568d96d6418004cc79466c06f3d791">MQTTAsync_responseOptions</a>;</div>
</div>
<div class="line"><a id="l00775" name="l00775"></a><span class="lineno">  775</span> </div>
<div class="line"><a id="l00776" name="l00776"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a3f8b408243b5c2369bc9758f2edf0878">  776</a></span><span class="preprocessor">#define MQTTAsync_responseOptions_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;R&#39;}, 1, NULL, NULL, 0, 0, NULL, NULL, MQTTProperties_initializer, MQTTSubscribe_options_initializer, 0, NULL}</span></div>
<div class="line"><a id="l00777" name="l00777"></a><span class="lineno">  777</span> </div>
<div class="line"><a id="l00779" name="l00779"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ab6bfa6beae93c259220e1a131ba1cf9c">  779</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> <a class="code hl_struct" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_callOptions</a>;</div>
<div class="line"><a id="l00780" name="l00780"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a570185766fc8a9da410a6f84915b6df5">  780</a></span><span class="preprocessor">#define MQTTAsync_callOptions_initializer MQTTAsync_responseOptions_initializer</span></div>
<div class="line"><a id="l00781" name="l00781"></a><span class="lineno">  781</span> </div>
<div class="line"><a id="l00810" name="l00810"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">  810</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">MQTTAsync_setCallbacks</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#ae376f130b17d169ee51be68077a89ed0">context</a>, <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">MQTTAsync_connectionLost</a>* cl,</div>
<div class="line"><a id="l00811" name="l00811"></a><span class="lineno">  811</span>                                                                         <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync_messageArrived</a>* ma, <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync_deliveryComplete</a>* dc);</div>
<div class="line"><a id="l00812" name="l00812"></a><span class="lineno">  812</span> </div>
<div class="line"><a id="l00833" name="l00833"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#aee15bbd9224efd9dcce9b4ae491b2e2e">  833</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#aee15bbd9224efd9dcce9b4ae491b2e2e">MQTTAsync_setConnectionLostCallback</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#ae376f130b17d169ee51be68077a89ed0">context</a>,</div>
<div class="line"><a id="l00834" name="l00834"></a><span class="lineno">  834</span>                                                                                                  <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">MQTTAsync_connectionLost</a>* cl);</div>
<div class="line"><a id="l00835" name="l00835"></a><span class="lineno">  835</span> </div>
<div class="line"><a id="l00857" name="l00857"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a44abc360051b918a39b0596a137775ae">  857</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#a44abc360051b918a39b0596a137775ae">MQTTAsync_setMessageArrivedCallback</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#ae376f130b17d169ee51be68077a89ed0">context</a>,</div>
<div class="line"><a id="l00858" name="l00858"></a><span class="lineno">  858</span>                                                                                                  <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync_messageArrived</a>* ma);</div>
<div class="line"><a id="l00859" name="l00859"></a><span class="lineno">  859</span> </div>
<div class="line"><a id="l00879" name="l00879"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a94ec624ee22cc01d2ca58a9e646a2665">  879</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#a94ec624ee22cc01d2ca58a9e646a2665">MQTTAsync_setDeliveryCompleteCallback</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#ae376f130b17d169ee51be68077a89ed0">context</a>,</div>
<div class="line"><a id="l00880" name="l00880"></a><span class="lineno">  880</span>                                                                                                        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync_deliveryComplete</a>* dc);</div>
<div class="line"><a id="l00881" name="l00881"></a><span class="lineno">  881</span> </div>
<div class="line"><a id="l00894" name="l00894"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a18cc19740d9b00c629dc53a4420ecf1f">  894</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#a18cc19740d9b00c629dc53a4420ecf1f">MQTTAsync_setConnected</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#ae376f130b17d169ee51be68077a89ed0">context</a>, <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a34bb8d321e9d368780b5c832c058f223">MQTTAsync_connected</a>* co);</div>
<div class="line"><a id="l00895" name="l00895"></a><span class="lineno">  895</span> </div>
<div class="line"><a id="l00896" name="l00896"></a><span class="lineno">  896</span> </div>
<div class="line"><a id="l00905" name="l00905"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#abd3ea01869b89ff23f9522640479c395">  905</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#abd3ea01869b89ff23f9522640479c395">MQTTAsync_reconnect</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle);</div>
<div class="line"><a id="l00906" name="l00906"></a><span class="lineno">  906</span> </div>
<div class="line"><a id="l00907" name="l00907"></a><span class="lineno">  907</span> </div>
<div class="line"><a id="l00960" name="l00960"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">  960</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>* handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* serverURI, <span class="keyword">const</span> <span class="keywordtype">char</span>* clientId,</div>
<div class="line"><a id="l00961" name="l00961"></a><span class="lineno">  961</span>                <span class="keywordtype">int</span> persistence_type, <span class="keywordtype">void</span>* persistence_context);</div>
<div class="line"><a id="l00962" name="l00962"></a><span class="lineno">  962</span> </div>
<div class="foldopen" id="foldopen00964" data-start="{" data-end="};">
<div class="line"><a id="l00964" name="l00964"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html">  964</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l00965" name="l00965"></a><span class="lineno">  965</span>{</div>
<div class="line"><a id="l00967" name="l00967"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html#aa5326df180cb23c59afbcab711a06479">  967</a></span>        <span class="keywordtype">char</span> <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#aa5326df180cb23c59afbcab711a06479">struct_id</a>[4];</div>
<div class="line"><a id="l00973" name="l00973"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html#a0761a5e5be0383882e42924de8e51f82">  973</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__create_options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a id="l00975" name="l00975"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html#a078cd68d8f896ce7eac0cc83d4486a2c">  975</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__create_options.html#a078cd68d8f896ce7eac0cc83d4486a2c">sendWhileDisconnected</a>;</div>
<div class="line"><a id="l00979" name="l00979"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html#a3b74acf6f315bb5fe36266bc9647ee97">  979</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__create_options.html#a3b74acf6f315bb5fe36266bc9647ee97">maxBufferedMessages</a>;</div>
<div class="line"><a id="l00985" name="l00985"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html#a12d546fd0ccf4e1091b18e1b735c7240">  985</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__create_options.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTVersion</a>;</div>
<div class="line"><a id="l00989" name="l00989"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html#abe7fdbe18bfd3577a75d3b386d69406c">  989</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__create_options.html#abe7fdbe18bfd3577a75d3b386d69406c">allowDisconnectedSendAtAnyTime</a>;</div>
<div class="line"><a id="l00990" name="l00990"></a><span class="lineno">  990</span>        <span class="comment">/*</span></div>
<div class="line"><a id="l00991" name="l00991"></a><span class="lineno">  991</span><span class="comment">         * When the maximum number of buffered messages is reached, delete the oldest rather than the newest.</span></div>
<div class="line"><a id="l00992" name="l00992"></a><span class="lineno">  992</span><span class="comment">         */</span></div>
<div class="line"><a id="l00993" name="l00993"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html#a76de37b3cff885e83db204a347fe0a2d">  993</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__create_options.html#a76de37b3cff885e83db204a347fe0a2d">deleteOldestMessages</a>;</div>
<div class="line"><a id="l00994" name="l00994"></a><span class="lineno">  994</span>        <span class="comment">/*</span></div>
<div class="line"><a id="l00995" name="l00995"></a><span class="lineno">  995</span><span class="comment">         * Restore messages from persistence on create - or clear it.</span></div>
<div class="line"><a id="l00996" name="l00996"></a><span class="lineno">  996</span><span class="comment">         */</span></div>
<div class="line"><a id="l00997" name="l00997"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html#a231b8890c3bc2ea07f7c599896f30691">  997</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__create_options.html#a231b8890c3bc2ea07f7c599896f30691">restoreMessages</a>;</div>
<div class="line"><a id="l00998" name="l00998"></a><span class="lineno">  998</span>        <span class="comment">/*</span></div>
<div class="line"><a id="l00999" name="l00999"></a><span class="lineno">  999</span><span class="comment">         * Persist QoS0 publish commands - an option to not persist them.</span></div>
<div class="line"><a id="l01000" name="l01000"></a><span class="lineno"> 1000</span><span class="comment">         */</span></div>
<div class="line"><a id="l01001" name="l01001"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html#a0c3ea2641e188542c787e71e2c521a0b"> 1001</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__create_options.html#a0c3ea2641e188542c787e71e2c521a0b">persistQoS0</a>;</div>
<div class="line"><a id="l01002" name="l01002"></a><span class="lineno"> 1002</span>} <a class="code hl_struct" href="struct_m_q_t_t_async__create_options.html">MQTTAsync_createOptions</a>;</div>
</div>
<div class="line"><a id="l01003" name="l01003"></a><span class="lineno"> 1003</span> </div>
<div class="line"><a id="l01004" name="l01004"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5fedeafef4753f09b1bcb92773564786"> 1004</a></span><span class="preprocessor">#define MQTTAsync_createOptions_initializer  { {&#39;M&#39;, &#39;Q&#39;, &#39;C&#39;, &#39;O&#39;}, 2, 0, 100, MQTTVERSION_DEFAULT, 0, 0, 1, 1}</span></div>
<div class="line"><a id="l01005" name="l01005"></a><span class="lineno"> 1005</span> </div>
<div class="line"><a id="l01006" name="l01006"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a0008776a46e7268ccbef4774ce3d4579"> 1006</a></span><span class="preprocessor">#define MQTTAsync_createOptions_initializer5 { {&#39;M&#39;, &#39;Q&#39;, &#39;C&#39;, &#39;O&#39;}, 2, 0, 100, MQTTVERSION_5, 0, 0, 1, 1}</span></div>
<div class="line"><a id="l01007" name="l01007"></a><span class="lineno"> 1007</span> </div>
<div class="line"><a id="l01008" name="l01008"></a><span class="lineno"> 1008</span> </div>
<div class="line"><a id="l01009" name="l01009"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a78cbe1b851fea48001112f7ba9e4ea62"> 1009</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#a78cbe1b851fea48001112f7ba9e4ea62">MQTTAsync_createWithOptions</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>* handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* serverURI, <span class="keyword">const</span> <span class="keywordtype">char</span>* clientId,</div>
<div class="line"><a id="l01010" name="l01010"></a><span class="lineno"> 1010</span>                <span class="keywordtype">int</span> persistence_type, <span class="keywordtype">void</span>* persistence_context, <a class="code hl_struct" href="struct_m_q_t_t_async__create_options.html">MQTTAsync_createOptions</a>* options);</div>
<div class="line"><a id="l01011" name="l01011"></a><span class="lineno"> 1011</span> </div>
<div class="foldopen" id="foldopen01024" data-start="{" data-end="};">
<div class="line"><a id="l01024" name="l01024"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html"> 1024</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l01025" name="l01025"></a><span class="lineno"> 1025</span>{</div>
<div class="line"><a id="l01027" name="l01027"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html#aa5326df180cb23c59afbcab711a06479"> 1027</a></span>        <span class="keywordtype">char</span> <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#aa5326df180cb23c59afbcab711a06479">struct_id</a>[4];</div>
<div class="line"><a id="l01031" name="l01031"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html#a0761a5e5be0383882e42924de8e51f82"> 1031</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__will_options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a id="l01033" name="l01033"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html#a0e20a7b350881d05108d6342884198a5"> 1033</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__will_options.html#a0e20a7b350881d05108d6342884198a5">topicName</a>;</div>
<div class="line"><a id="l01035" name="l01035"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html#a254bf0858da09c96a48daf64404eb4f8"> 1035</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__will_options.html#a254bf0858da09c96a48daf64404eb4f8">message</a>;</div>
<div class="line"><a id="l01039" name="l01039"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html#a6a4904c112507a43e7dc8495b62cc0fc"> 1039</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__will_options.html#a6a4904c112507a43e7dc8495b62cc0fc">retained</a>;</div>
<div class="line"><a id="l01044" name="l01044"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html#a35738099155a0e4f54050da474bab2e7"> 1044</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__will_options.html#a35738099155a0e4f54050da474bab2e7">qos</a>;</div>
<div class="line"><a id="l01046" name="l01046"></a><span class="lineno"> 1046</span>        <span class="keyword">struct</span></div>
<div class="line"><a id="l01047" name="l01047"></a><span class="lineno"> 1047</span>        {</div>
<div class="line"><a id="l01048" name="l01048"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html#afed088663f8704004425cdae2120b9b3"> 1048</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__will_options.html#afed088663f8704004425cdae2120b9b3">len</a>;            </div>
<div class="line"><a id="l01049" name="l01049"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html#a0d49d74db4c035719c3867723cf7e779"> 1049</a></span>                <span class="keyword">const</span> <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__will_options.html#a0d49d74db4c035719c3867723cf7e779">data</a>;  </div>
<div class="line"><a id="l01050" name="l01050"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html#a93e9de18277b05bc7a033bdee98c908a"> 1050</a></span>        } payload;</div>
<div class="line"><a id="l01051" name="l01051"></a><span class="lineno"> 1051</span>} <a class="code hl_struct" href="struct_m_q_t_t_async__will_options.html">MQTTAsync_willOptions</a>;</div>
</div>
<div class="line"><a id="l01052" name="l01052"></a><span class="lineno"> 1052</span> </div>
<div class="line"><a id="l01053" name="l01053"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a6c45768e1b28844f2ac0f6ac68709730"> 1053</a></span><span class="preprocessor">#define MQTTAsync_willOptions_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;W&#39;}, 1, NULL, NULL, 0, 0, { 0, NULL } }</span></div>
<div class="line"><a id="l01054" name="l01054"></a><span class="lineno"> 1054</span> </div>
<div class="line"><a id="l01055" name="l01055"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a2549ea897af26c76198284731db9e721"> 1055</a></span><span class="preprocessor">#define MQTT_SSL_VERSION_DEFAULT 0</span></div>
<div class="line"><a id="l01056" name="l01056"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a7e5da3d6f0d2b53409bbfcf6e56f3d2d"> 1056</a></span><span class="preprocessor">#define MQTT_SSL_VERSION_TLS_1_0 1</span></div>
<div class="line"><a id="l01057" name="l01057"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#abdff87efa3f2ee473a1591e10638b537"> 1057</a></span><span class="preprocessor">#define MQTT_SSL_VERSION_TLS_1_1 2</span></div>
<div class="line"><a id="l01058" name="l01058"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a3a94dbdeafbb73c73a068e7c2085fbab"> 1058</a></span><span class="preprocessor">#define MQTT_SSL_VERSION_TLS_1_2 3</span></div>
<div class="line"><a id="l01059" name="l01059"></a><span class="lineno"> 1059</span> </div>
<div class="foldopen" id="foldopen01072" data-start="{" data-end="};">
<div class="line"><a id="l01072" name="l01072"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html"> 1072</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l01073" name="l01073"></a><span class="lineno"> 1073</span>{</div>
<div class="line"><a id="l01075" name="l01075"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#aa5326df180cb23c59afbcab711a06479"> 1075</a></span>        <span class="keywordtype">char</span> <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#aa5326df180cb23c59afbcab711a06479">struct_id</a>[4];</div>
<div class="line"><a id="l01076" name="l01076"></a><span class="lineno"> 1076</span> </div>
<div class="line"><a id="l01084" name="l01084"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a0761a5e5be0383882e42924de8e51f82"> 1084</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async___s_s_l_options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a id="l01085" name="l01085"></a><span class="lineno"> 1085</span> </div>
<div class="line"><a id="l01087" name="l01087"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a032835d4c4a1c1e19b53c330a673a6e0"> 1087</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async___s_s_l_options.html#a032835d4c4a1c1e19b53c330a673a6e0">trustStore</a>;</div>
<div class="line"><a id="l01088" name="l01088"></a><span class="lineno"> 1088</span> </div>
<div class="line"><a id="l01092" name="l01092"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a32b476382955289ce427112b59f21c3e"> 1092</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async___s_s_l_options.html#a32b476382955289ce427112b59f21c3e">keyStore</a>;</div>
<div class="line"><a id="l01093" name="l01093"></a><span class="lineno"> 1093</span> </div>
<div class="line"><a id="l01097" name="l01097"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a7dd436cbb916fba200595c3519f09ec4"> 1097</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async___s_s_l_options.html#a7dd436cbb916fba200595c3519f09ec4">privateKey</a>;</div>
<div class="line"><a id="l01098" name="l01098"></a><span class="lineno"> 1098</span> </div>
<div class="line"><a id="l01100" name="l01100"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#abb427571ba37b51f6985f1a6906ca031"> 1100</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async___s_s_l_options.html#abb427571ba37b51f6985f1a6906ca031">privateKeyPassword</a>;</div>
<div class="line"><a id="l01101" name="l01101"></a><span class="lineno"> 1101</span> </div>
<div class="line"><a id="l01110" name="l01110"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#aa683926d52134077f27d6dc67bda13ab"> 1110</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async___s_s_l_options.html#aa683926d52134077f27d6dc67bda13ab">enabledCipherSuites</a>;</div>
<div class="line"><a id="l01111" name="l01111"></a><span class="lineno"> 1111</span> </div>
<div class="line"><a id="l01113" name="l01113"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a75f6c13b7634e15f96dd9f17db6cf0be"> 1113</a></span>    <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async___s_s_l_options.html#a75f6c13b7634e15f96dd9f17db6cf0be">enableServerCertAuth</a>;</div>
<div class="line"><a id="l01114" name="l01114"></a><span class="lineno"> 1114</span> </div>
<div class="line"><a id="l01119" name="l01119"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a3543ea1481b68d73cdde833280bb9c45"> 1119</a></span>    <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async___s_s_l_options.html#a3543ea1481b68d73cdde833280bb9c45">sslVersion</a>;</div>
<div class="line"><a id="l01120" name="l01120"></a><span class="lineno"> 1120</span> </div>
<div class="line"><a id="l01126" name="l01126"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a94900629685d5ed08f66fd2931f573ce"> 1126</a></span>    <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async___s_s_l_options.html#a94900629685d5ed08f66fd2931f573ce">verify</a>;</div>
<div class="line"><a id="l01127" name="l01127"></a><span class="lineno"> 1127</span> </div>
<div class="line"><a id="l01133" name="l01133"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a3078b3c824cc9753a57898072445c34d"> 1133</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async___s_s_l_options.html#a3078b3c824cc9753a57898072445c34d">CApath</a>;</div>
<div class="line"><a id="l01134" name="l01134"></a><span class="lineno"> 1134</span> </div>
<div class="line"><a id="l01139" name="l01139"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a21b6ca8a73ba197e65f6a93365d39c04"> 1139</a></span>    int (*ssl_error_cb) (<span class="keyword">const</span> <span class="keywordtype">char</span> *str, <span class="keywordtype">size_t</span> len, <span class="keywordtype">void</span> *u);</div>
<div class="line"><a id="l01140" name="l01140"></a><span class="lineno"> 1140</span> </div>
<div class="line"><a id="l01145" name="l01145"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a189f11195f4d5a70024adffdb050885f"> 1145</a></span>    <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_async___s_s_l_options.html#a189f11195f4d5a70024adffdb050885f">ssl_error_context</a>;</div>
<div class="line"><a id="l01146" name="l01146"></a><span class="lineno"> 1146</span> </div>
<div class="line"><a id="l01152" name="l01152"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a94317cdaf352f9ae496976f8a30f8fee"> 1152</a></span>        <span class="keywordtype">unsigned</span> int (*ssl_psk_cb) (<span class="keyword">const</span> <span class="keywordtype">char</span> *hint, <span class="keywordtype">char</span> *identity, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> max_identity_len, <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> *psk, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> max_psk_len, <span class="keywordtype">void</span> *u);</div>
<div class="line"><a id="l01153" name="l01153"></a><span class="lineno"> 1153</span> </div>
<div class="line"><a id="l01158" name="l01158"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#ab7f597518dd5b9db5a515081f8e0bd1f"> 1158</a></span>        <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_async___s_s_l_options.html#ab7f597518dd5b9db5a515081f8e0bd1f">ssl_psk_context</a>;</div>
<div class="line"><a id="l01159" name="l01159"></a><span class="lineno"> 1159</span> </div>
<div class="line"><a id="l01165" name="l01165"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a0826fcae7c2816e04772c61542c6846b"> 1165</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async___s_s_l_options.html#a0826fcae7c2816e04772c61542c6846b">disableDefaultTrustStore</a>;</div>
<div class="line"><a id="l01166" name="l01166"></a><span class="lineno"> 1166</span> </div>
<div class="line"><a id="l01174" name="l01174"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a4f8661600fb8bacf031150f8dcd293a5"> 1174</a></span>        <span class="keyword">const</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> *<a class="code hl_variable" href="struct_m_q_t_t_async___s_s_l_options.html#a4f8661600fb8bacf031150f8dcd293a5">protos</a>;</div>
<div class="line"><a id="l01175" name="l01175"></a><span class="lineno"> 1175</span> </div>
<div class="line"><a id="l01180" name="l01180"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a26f5d839c92f9772c2a5d05486277a42"> 1180</a></span>        <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async___s_s_l_options.html#a26f5d839c92f9772c2a5d05486277a42">protos_len</a>;</div>
<div class="line"><a id="l01181" name="l01181"></a><span class="lineno"> 1181</span>} <a class="code hl_struct" href="struct_m_q_t_t_async___s_s_l_options.html">MQTTAsync_SSLOptions</a>;</div>
</div>
<div class="line"><a id="l01182" name="l01182"></a><span class="lineno"> 1182</span> </div>
<div class="line"><a id="l01183" name="l01183"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#aac935e2e9d770a53ee8189f128530511"> 1183</a></span><span class="preprocessor">#define MQTTAsync_SSLOptions_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;S&#39;}, 5, NULL, NULL, NULL, NULL, NULL, 1, MQTT_SSL_VERSION_DEFAULT, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0 }</span></div>
<div class="line"><a id="l01184" name="l01184"></a><span class="lineno"> 1184</span> </div>
<div class="foldopen" id="foldopen01186" data-start="{" data-end="};">
<div class="line"><a id="l01186" name="l01186"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__name_value.html"> 1186</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l01187" name="l01187"></a><span class="lineno"> 1187</span>{</div>
<div class="line"><a id="l01188" name="l01188"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__name_value.html#a8f8f80d37794cde9472343e4487ba3eb"> 1188</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__name_value.html#a8f8f80d37794cde9472343e4487ba3eb">name</a>; </div>
<div class="line"><a id="l01189" name="l01189"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__name_value.html#a8556878012feffc9e0beb86cd78f424d"> 1189</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__name_value.html#a8556878012feffc9e0beb86cd78f424d">value</a>; </div>
<div class="line"><a id="l01190" name="l01190"></a><span class="lineno"> 1190</span>} <a class="code hl_struct" href="struct_m_q_t_t_async__name_value.html">MQTTAsync_nameValue</a>;</div>
</div>
<div class="line"><a id="l01191" name="l01191"></a><span class="lineno"> 1191</span> </div>
<div class="foldopen" id="foldopen01202" data-start="{" data-end="};">
<div class="line"><a id="l01202" name="l01202"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html"> 1202</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l01203" name="l01203"></a><span class="lineno"> 1203</span>{</div>
<div class="line"><a id="l01205" name="l01205"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#aa5326df180cb23c59afbcab711a06479"> 1205</a></span>        <span class="keywordtype">char</span> <a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#aa5326df180cb23c59afbcab711a06479">struct_id</a>[4];</div>
<div class="line"><a id="l01216" name="l01216"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a0761a5e5be0383882e42924de8e51f82"> 1216</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a id="l01227" name="l01227"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#ac8dd0930672a9c7d71fc645aa1f0521d"> 1227</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#ac8dd0930672a9c7d71fc645aa1f0521d">keepAliveInterval</a>;</div>
<div class="line"><a id="l01249" name="l01249"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b"> 1249</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">cleansession</a>;</div>
<div class="line"><a id="l01253" name="l01253"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a5c9d6c557453232a1b25cbbec5a31e8c"> 1253</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a5c9d6c557453232a1b25cbbec5a31e8c">maxInflight</a>;</div>
<div class="line"><a id="l01259" name="l01259"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a7a9c5105542460d6fd9323facca66648"> 1259</a></span>        <a class="code hl_struct" href="struct_m_q_t_t_async__will_options.html">MQTTAsync_willOptions</a>* <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a7a9c5105542460d6fd9323facca66648">will</a>;</div>
<div class="line"><a id="l01265" name="l01265"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#aba2dfcdfda80edcb531a5a7115d3e043"> 1265</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#aba2dfcdfda80edcb531a5a7115d3e043">username</a>;</div>
<div class="line"><a id="l01271" name="l01271"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#aa4a2ebcb494493f648ae1e6975672575"> 1271</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#aa4a2ebcb494493f648ae1e6975672575">password</a>;</div>
<div class="line"><a id="l01275" name="l01275"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a38c6aa24b36d981c49405db425c24db0"> 1275</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a38c6aa24b36d981c49405db425c24db0">connectTimeout</a>;</div>
<div class="line"><a id="l01283" name="l01283"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#ac73f57846c42bcaa9a47e6721a957748"> 1283</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#ac73f57846c42bcaa9a47e6721a957748">retryInterval</a>;</div>
<div class="line"><a id="l01288" name="l01288"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a86fd59846f3ba2082fd99906c6b496a6"> 1288</a></span>        <a class="code hl_struct" href="struct_m_q_t_t_async___s_s_l_options.html">MQTTAsync_SSLOptions</a>* <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a86fd59846f3ba2082fd99906c6b496a6">ssl</a>;</div>
<div class="line"><a id="l01294" name="l01294"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#ac13fb68f736854fcab131b34756bfceb"> 1294</a></span>        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess</a>* <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#ac13fb68f736854fcab131b34756bfceb">onSuccess</a>;</div>
<div class="line"><a id="l01300" name="l01300"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a09ce26d7cff24e14a6844eaae7b15290"> 1300</a></span>        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync_onFailure</a>* <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a09ce26d7cff24e14a6844eaae7b15290">onFailure</a>;</div>
<div class="line"><a id="l01306" name="l01306"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#ae376f130b17d169ee51be68077a89ed0"> 1306</a></span>        <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#ae376f130b17d169ee51be68077a89ed0">context</a>;</div>
<div class="line"><a id="l01310" name="l01310"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#aa82629005937abd92e97084a428cd61f"> 1310</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#aa82629005937abd92e97084a428cd61f">serverURIcount</a>;</div>
<div class="line"><a id="l01322" name="l01322"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#aba22d81c407fb2ba590dba476240d3e9"> 1322</a></span>        <span class="keywordtype">char</span>* <span class="keyword">const</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#aba22d81c407fb2ba590dba476240d3e9">serverURIs</a>;</div>
<div class="line"><a id="l01329" name="l01329"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a12d546fd0ccf4e1091b18e1b735c7240"> 1329</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTVersion</a>;</div>
<div class="line"><a id="l01333" name="l01333"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a7902ce4d11b96d8b19582bdd1f82b630"> 1333</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a7902ce4d11b96d8b19582bdd1f82b630">automaticReconnect</a>;</div>
<div class="line"><a id="l01337" name="l01337"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a166ac1b967f09326b0187f66be3e69af"> 1337</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a166ac1b967f09326b0187f66be3e69af">minRetryInterval</a>;</div>
<div class="line"><a id="l01341" name="l01341"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a035ba380dd97a284db04f4eaae5e113b"> 1341</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a035ba380dd97a284db04f4eaae5e113b">maxRetryInterval</a>;</div>
<div class="line"><a id="l01345" name="l01345"></a><span class="lineno"> 1345</span>        <span class="keyword">struct </span>{</div>
<div class="line"><a id="l01346" name="l01346"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#afed088663f8704004425cdae2120b9b3"> 1346</a></span>                <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#afed088663f8704004425cdae2120b9b3">len</a>;            </div>
<div class="line"><a id="l01347" name="l01347"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a0d49d74db4c035719c3867723cf7e779"> 1347</a></span>                <span class="keyword">const</span> <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a0d49d74db4c035719c3867723cf7e779">data</a>;  </div>
<div class="line"><a id="l01348" name="l01348"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a3bccd0957cca80fa2200962051093931"> 1348</a></span>        } binarypwd;</div>
<div class="line"><a id="l01349" name="l01349"></a><span class="lineno"> 1349</span>        <span class="comment">/*</span></div>
<div class="line"><a id="l01350" name="l01350"></a><span class="lineno"> 1350</span><span class="comment">         * MQTT V5 clean start flag.  Only clears state at the beginning of the session.</span></div>
<div class="line"><a id="l01351" name="l01351"></a><span class="lineno"> 1351</span><span class="comment">         */</span></div>
<div class="line"><a id="l01352" name="l01352"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#acdcb75a5d5981da027bce83849140f7b"> 1352</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#acdcb75a5d5981da027bce83849140f7b">cleanstart</a>;</div>
<div class="line"><a id="l01356" name="l01356"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a9f8b7ffb4a698eb151a3b090548b82e8"> 1356</a></span>        <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *<a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a9f8b7ffb4a698eb151a3b090548b82e8">connectProperties</a>;</div>
<div class="line"><a id="l01360" name="l01360"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#ac31f13e964ffb7e3696caef47ecc0641"> 1360</a></span>        <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *<a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#ac31f13e964ffb7e3696caef47ecc0641">willProperties</a>;</div>
<div class="line"><a id="l01366" name="l01366"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a1c23c490f06428725345de68a4ff0a3e"> 1366</a></span>        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync_onSuccess5</a>* <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a1c23c490f06428725345de68a4ff0a3e">onSuccess5</a>;</div>
<div class="line"><a id="l01372" name="l01372"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a4dad726f2b6f79ca5847689c5f2f2ec2"> 1372</a></span>        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync_onFailure5</a>* <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a4dad726f2b6f79ca5847689c5f2f2ec2">onFailure5</a>;</div>
<div class="line"><a id="l01376" name="l01376"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#ac4098248961a1ee89f40353eeebab58b"> 1376</a></span>        <span class="keyword">const</span> <a class="code hl_struct" href="struct_m_q_t_t_async__name_value.html">MQTTAsync_nameValue</a>* <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#ac4098248961a1ee89f40353eeebab58b">httpHeaders</a>;</div>
<div class="line"><a id="l01382" name="l01382"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#add124780ab2de397a96780576c2f112c"> 1382</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#add124780ab2de397a96780576c2f112c">httpProxy</a>;</div>
<div class="line"><a id="l01386" name="l01386"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a388b78d8a75658928238f700f207ad92"> 1386</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a388b78d8a75658928238f700f207ad92">httpsProxy</a>;</div>
<div class="line"><a id="l01387" name="l01387"></a><span class="lineno"> 1387</span>} <a class="code hl_struct" href="struct_m_q_t_t_async__connect_options.html">MQTTAsync_connectOptions</a>;</div>
</div>
<div class="line"><a id="l01388" name="l01388"></a><span class="lineno"> 1388</span> </div>
<div class="foldopen" id="foldopen01390" data-start="" data-end="">
<div class="line"><a id="l01390" name="l01390"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ae18b51f22784a43803eb809d6a0c2492"> 1390</a></span><span class="preprocessor">#define MQTTAsync_connectOptions_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;C&#39;}, 8, 60, 1, 65535, NULL, NULL, NULL, 30, 0,\</span></div>
<div class="line"><a id="l01391" name="l01391"></a><span class="lineno"> 1391</span><span class="preprocessor">NULL, NULL, NULL, NULL, 0, NULL, MQTTVERSION_DEFAULT, 0, 1, 60, {0, NULL}, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL}</span></div>
</div>
<div class="line"><a id="l01392" name="l01392"></a><span class="lineno"> 1392</span> </div>
<div class="foldopen" id="foldopen01394" data-start="" data-end="">
<div class="line"><a id="l01394" name="l01394"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#abd403ce21f7aa0348ae1d3eefd031a5d"> 1394</a></span><span class="preprocessor">#define MQTTAsync_connectOptions_initializer5 { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;C&#39;}, 8, 60, 0, 65535, NULL, NULL, NULL, 30, 0,\</span></div>
<div class="line"><a id="l01395" name="l01395"></a><span class="lineno"> 1395</span><span class="preprocessor">NULL, NULL, NULL, NULL, 0, NULL, MQTTVERSION_5, 0, 1, 60, {0, NULL}, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL}</span></div>
</div>
<div class="line"><a id="l01396" name="l01396"></a><span class="lineno"> 1396</span> </div>
<div class="foldopen" id="foldopen01400" data-start="" data-end="">
<div class="line"><a id="l01400" name="l01400"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a080951d916d7a58c4ceff8c6bacfe313"> 1400</a></span><span class="preprocessor">#define MQTTAsync_connectOptions_initializer_ws { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;C&#39;}, 8, 45, 1, 65535, NULL, NULL, NULL, 30, 0,\</span></div>
<div class="line"><a id="l01401" name="l01401"></a><span class="lineno"> 1401</span><span class="preprocessor">NULL, NULL, NULL, NULL, 0, NULL, MQTTVERSION_DEFAULT, 0, 1, 60, {0, NULL}, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL}</span></div>
</div>
<div class="line"><a id="l01402" name="l01402"></a><span class="lineno"> 1402</span> </div>
<div class="foldopen" id="foldopen01406" data-start="" data-end="">
<div class="line"><a id="l01406" name="l01406"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a513bfbec7b7d39c827240db75aa4044b"> 1406</a></span><span class="preprocessor">#define MQTTAsync_connectOptions_initializer5_ws { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;C&#39;}, 8, 45, 0, 65535, NULL, NULL, NULL, 30, 0,\</span></div>
<div class="line"><a id="l01407" name="l01407"></a><span class="lineno"> 1407</span><span class="preprocessor">NULL, NULL, NULL, NULL, 0, NULL, MQTTVERSION_5, 0, 1, 60, {0, NULL}, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL}</span></div>
</div>
<div class="line"><a id="l01408" name="l01408"></a><span class="lineno"> 1408</span> </div>
<div class="line"><a id="l01409" name="l01409"></a><span class="lineno"> 1409</span> </div>
<div class="line"><a id="l01430" name="l01430"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a0388b226a414b09fa733f6d65004ec32"> 1430</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#a0388b226a414b09fa733f6d65004ec32">MQTTAsync_connect</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keyword">const</span> <a class="code hl_struct" href="struct_m_q_t_t_async__connect_options.html">MQTTAsync_connectOptions</a>* options);</div>
<div class="line"><a id="l01431" name="l01431"></a><span class="lineno"> 1431</span> </div>
<div class="foldopen" id="foldopen01433" data-start="{" data-end="};">
<div class="line"><a id="l01433" name="l01433"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html"> 1433</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l01434" name="l01434"></a><span class="lineno"> 1434</span>{</div>
<div class="line"><a id="l01436" name="l01436"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#aa5326df180cb23c59afbcab711a06479"> 1436</a></span>        <span class="keywordtype">char</span> struct_id[4];</div>
<div class="line"><a id="l01438" name="l01438"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#a0761a5e5be0383882e42924de8e51f82"> 1438</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__disconnect_options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a id="l01443" name="l01443"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#a493b57f443cc38b3d3df9c1e584d9d82"> 1443</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_async__disconnect_options.html#a493b57f443cc38b3d3df9c1e584d9d82">timeout</a>;</div>
<div class="line"><a id="l01449" name="l01449"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#ac13fb68f736854fcab131b34756bfceb"> 1449</a></span>        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess</a>* <a class="code hl_variable" href="struct_m_q_t_t_async__disconnect_options.html#ac13fb68f736854fcab131b34756bfceb">onSuccess</a>;</div>
<div class="line"><a id="l01455" name="l01455"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#a09ce26d7cff24e14a6844eaae7b15290"> 1455</a></span>        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync_onFailure</a>* <a class="code hl_variable" href="struct_m_q_t_t_async__disconnect_options.html#a09ce26d7cff24e14a6844eaae7b15290">onFailure</a>;</div>
<div class="line"><a id="l01461" name="l01461"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#ae376f130b17d169ee51be68077a89ed0"> 1461</a></span>        <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_async__disconnect_options.html#ae376f130b17d169ee51be68077a89ed0">context</a>;</div>
<div class="line"><a id="l01465" name="l01465"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#a1594008402f7307e4de8fa6131656dde"> 1465</a></span>        <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a> <a class="code hl_variable" href="struct_m_q_t_t_async__disconnect_options.html#a1594008402f7307e4de8fa6131656dde">properties</a>;</div>
<div class="line"><a id="l01469" name="l01469"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#a580d8a8ecb285f5a86c2a3865438f8ee"> 1469</a></span>        <span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> <a class="code hl_variable" href="struct_m_q_t_t_async__disconnect_options.html#a580d8a8ecb285f5a86c2a3865438f8ee">reasonCode</a>;</div>
<div class="line"><a id="l01475" name="l01475"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#a1c23c490f06428725345de68a4ff0a3e"> 1475</a></span>        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync_onSuccess5</a>* <a class="code hl_variable" href="struct_m_q_t_t_async__disconnect_options.html#a1c23c490f06428725345de68a4ff0a3e">onSuccess5</a>;</div>
<div class="line"><a id="l01481" name="l01481"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#a4dad726f2b6f79ca5847689c5f2f2ec2"> 1481</a></span>        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync_onFailure5</a>* <a class="code hl_variable" href="struct_m_q_t_t_async__disconnect_options.html#a4dad726f2b6f79ca5847689c5f2f2ec2">onFailure5</a>;</div>
<div class="line"><a id="l01482" name="l01482"></a><span class="lineno"> 1482</span>} <a class="code hl_struct" href="struct_m_q_t_t_async__disconnect_options.html">MQTTAsync_disconnectOptions</a>;</div>
</div>
<div class="line"><a id="l01483" name="l01483"></a><span class="lineno"> 1483</span> </div>
<div class="foldopen" id="foldopen01484" data-start="" data-end="">
<div class="line"><a id="l01484" name="l01484"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a2fd5d6df31928ae468f3f2e522b9c707"> 1484</a></span><span class="preprocessor">#define MQTTAsync_disconnectOptions_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;D&#39;}, 0, 0, NULL, NULL, NULL,\</span></div>
<div class="line"><a id="l01485" name="l01485"></a><span class="lineno"> 1485</span><span class="preprocessor">        MQTTProperties_initializer, MQTTREASONCODE_SUCCESS, NULL, NULL }</span></div>
</div>
<div class="line"><a id="l01486" name="l01486"></a><span class="lineno"> 1486</span> </div>
<div class="foldopen" id="foldopen01487" data-start="" data-end="">
<div class="line"><a id="l01487" name="l01487"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#aaa278001953dc129003eff83c8e7b3db"> 1487</a></span><span class="preprocessor">#define MQTTAsync_disconnectOptions_initializer5 { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;D&#39;}, 1, 0, NULL, NULL, NULL,\</span></div>
<div class="line"><a id="l01488" name="l01488"></a><span class="lineno"> 1488</span><span class="preprocessor">        MQTTProperties_initializer, MQTTREASONCODE_SUCCESS, NULL, NULL }</span></div>
</div>
<div class="line"><a id="l01489" name="l01489"></a><span class="lineno"> 1489</span> </div>
<div class="line"><a id="l01508" name="l01508"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#adc69afa4725f8321bdaa5a05aec5cfd5"> 1508</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#adc69afa4725f8321bdaa5a05aec5cfd5">MQTTAsync_disconnect</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keyword">const</span> <a class="code hl_struct" href="struct_m_q_t_t_async__disconnect_options.html">MQTTAsync_disconnectOptions</a>* options);</div>
<div class="line"><a id="l01509" name="l01509"></a><span class="lineno"> 1509</span> </div>
<div class="line"><a id="l01510" name="l01510"></a><span class="lineno"> 1510</span> </div>
<div class="line"><a id="l01518" name="l01518"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a46c332245c379629ae11f457fc179457"> 1518</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#a46c332245c379629ae11f457fc179457">MQTTAsync_isConnected</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle);</div>
<div class="line"><a id="l01519" name="l01519"></a><span class="lineno"> 1519</span> </div>
<div class="line"><a id="l01520" name="l01520"></a><span class="lineno"> 1520</span> </div>
<div class="line"><a id="l01535" name="l01535"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ae10bd009934b3bb4a9f4abae7424a611"> 1535</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#ae10bd009934b3bb4a9f4abae7424a611">MQTTAsync_subscribe</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* topic, <span class="keywordtype">int</span> qos, <a class="code hl_struct" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>* response);</div>
<div class="line"><a id="l01536" name="l01536"></a><span class="lineno"> 1536</span> </div>
<div class="line"><a id="l01537" name="l01537"></a><span class="lineno"> 1537</span> </div>
<div class="line"><a id="l01555" name="l01555"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ac78620b33434a187255bd1a3faec1578"> 1555</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#ac78620b33434a187255bd1a3faec1578">MQTTAsync_subscribeMany</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">int</span> count, <span class="keywordtype">char</span>* <span class="keyword">const</span>* topic, <span class="keyword">const</span> <span class="keywordtype">int</span>* qos, <a class="code hl_struct" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>* response);</div>
<div class="line"><a id="l01556" name="l01556"></a><span class="lineno"> 1556</span> </div>
<div class="line"><a id="l01569" name="l01569"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a08d18ece91c1b011011354570d8ac1ab"> 1569</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#a08d18ece91c1b011011354570d8ac1ab">MQTTAsync_unsubscribe</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* topic, <a class="code hl_struct" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>* response);</div>
<div class="line"><a id="l01570" name="l01570"></a><span class="lineno"> 1570</span> </div>
<div class="line"><a id="l01583" name="l01583"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a69fd433ce1b9b6a1b3b453c4793a9311"> 1583</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#a69fd433ce1b9b6a1b3b453c4793a9311">MQTTAsync_unsubscribeMany</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">int</span> count, <span class="keywordtype">char</span>* <span class="keyword">const</span>* topic, <a class="code hl_struct" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>* response);</div>
<div class="line"><a id="l01584" name="l01584"></a><span class="lineno"> 1584</span> </div>
<div class="line"><a id="l01585" name="l01585"></a><span class="lineno"> 1585</span> </div>
<div class="line"><a id="l01605" name="l01605"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a63c66a311ab16239a4175ff671871bf2"> 1605</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#a63c66a311ab16239a4175ff671871bf2">MQTTAsync_send</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* destinationName, <span class="keywordtype">int</span> payloadlen, <span class="keyword">const</span> <span class="keywordtype">void</span>* payload, <span class="keywordtype">int</span> qos,</div>
<div class="line"><a id="l01606" name="l01606"></a><span class="lineno"> 1606</span>                <span class="keywordtype">int</span> retained, <a class="code hl_struct" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>* response);</div>
<div class="line"><a id="l01607" name="l01607"></a><span class="lineno"> 1607</span> </div>
<div class="line"><a id="l01624" name="l01624"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5687171e67e98f9ea590c9e3b64cde18"> 1624</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#a5687171e67e98f9ea590c9e3b64cde18">MQTTAsync_sendMessage</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* destinationName, <span class="keyword">const</span> <a class="code hl_struct" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a>* msg, <a class="code hl_struct" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>* response);</div>
<div class="line"><a id="l01625" name="l01625"></a><span class="lineno"> 1625</span> </div>
<div class="line"><a id="l01626" name="l01626"></a><span class="lineno"> 1626</span> </div>
<div class="line"><a id="l01645" name="l01645"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#abc92f60743fc471643b473abbc987be0"> 1645</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#abc92f60743fc471643b473abbc987be0">MQTTAsync_getPendingTokens</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> **tokens);</div>
<div class="line"><a id="l01646" name="l01646"></a><span class="lineno"> 1646</span> </div>
<div class="line"><a id="l01655" name="l01655"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a61e6ee632e63312d382e2fcbe427f01a"> 1655</a></span><span class="preprocessor">#define MQTTASYNC_TRUE 1</span></div>
<div class="line"><a id="l01656" name="l01656"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ab207095cab6f9a48b52cdb593b8456f4"> 1656</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#ab207095cab6f9a48b52cdb593b8456f4">MQTTAsync_isComplete</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> token);</div>
<div class="line"><a id="l01657" name="l01657"></a><span class="lineno"> 1657</span> </div>
<div class="line"><a id="l01658" name="l01658"></a><span class="lineno"> 1658</span> </div>
<div class="line"><a id="l01671" name="l01671"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a4fe09cc9c976b1cf424e13765d6cd8c9"> 1671</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#a4fe09cc9c976b1cf424e13765d6cd8c9">MQTTAsync_waitForCompletion</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> token, <span class="keywordtype">unsigned</span> <span class="keywordtype">long</span> timeout);</div>
<div class="line"><a id="l01672" name="l01672"></a><span class="lineno"> 1672</span> </div>
<div class="line"><a id="l01673" name="l01673"></a><span class="lineno"> 1673</span> </div>
<div class="line"><a id="l01684" name="l01684"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a9b45db63052fe29ab1fad22d2a00c91c"> 1684</a></span>LIBMQTT_API <span class="keywordtype">void</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#a9b45db63052fe29ab1fad22d2a00c91c">MQTTAsync_freeMessage</a>(<a class="code hl_struct" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a>** msg);</div>
<div class="line"><a id="l01685" name="l01685"></a><span class="lineno"> 1685</span> </div>
<div class="line"><a id="l01694" name="l01694"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a2b836f58612a2c4627e40ae848da190d"> 1694</a></span>LIBMQTT_API <span class="keywordtype">void</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#a2b836f58612a2c4627e40ae848da190d">MQTTAsync_free</a>(<span class="keywordtype">void</span>* ptr);</div>
<div class="line"><a id="l01695" name="l01695"></a><span class="lineno"> 1695</span> </div>
<div class="line"><a id="l01703" name="l01703"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a8aa49e9e5fd6b6e1ca34cbaee9a28ee4"> 1703</a></span>LIBMQTT_API <span class="keywordtype">void</span>* <a class="code hl_function" href="_m_q_t_t_async_8h.html#a8aa49e9e5fd6b6e1ca34cbaee9a28ee4">MQTTAsync_malloc</a>(<span class="keywordtype">size_t</span> size);</div>
<div class="line"><a id="l01704" name="l01704"></a><span class="lineno"> 1704</span> </div>
<div class="line"><a id="l01712" name="l01712"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887"> 1712</a></span>LIBMQTT_API <span class="keywordtype">void</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync_destroy</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>* handle);</div>
<div class="line"><a id="l01713" name="l01713"></a><span class="lineno"> 1713</span> </div>
<div class="line"><a id="l01714" name="l01714"></a><span class="lineno"> 1714</span> </div>
<div class="line"><a id="l01715" name="l01715"></a><span class="lineno"> 1715</span> </div>
<div class="foldopen" id="foldopen01716" data-start="{" data-end="};">
<div class="line"><a id="l01716" name="l01716"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5"> 1716</a></span><span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5">MQTTASYNC_TRACE_LEVELS</a></div>
<div class="line"><a id="l01717" name="l01717"></a><span class="lineno"> 1717</span>{</div>
<div class="line"><a id="l01718" name="l01718"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5af684f42971cced68693ce993703548c1"> 1718</a></span>        <a class="code hl_enumvalue" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5af684f42971cced68693ce993703548c1">MQTTASYNC_TRACE_MAXIMUM</a> = 1,</div>
<div class="line"><a id="l01719" name="l01719"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a133c380b84d75477ff31a2ad732133ce"> 1719</a></span>        <a class="code hl_enumvalue" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a133c380b84d75477ff31a2ad732133ce">MQTTASYNC_TRACE_MEDIUM</a>,</div>
<div class="line"><a id="l01720" name="l01720"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a7a45c26816b1cac1fde02d79a9f4337b"> 1720</a></span>        <a class="code hl_enumvalue" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a7a45c26816b1cac1fde02d79a9f4337b">MQTTASYNC_TRACE_MINIMUM</a>,</div>
<div class="line"><a id="l01721" name="l01721"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a6a719b2b7fc4dfc41494370ff96fec3e"> 1721</a></span>        <a class="code hl_enumvalue" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a6a719b2b7fc4dfc41494370ff96fec3e">MQTTASYNC_TRACE_PROTOCOL</a>,</div>
<div class="line"><a id="l01722" name="l01722"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5ac428f74ca453dacb7b8271ca741266e8"> 1722</a></span>        <a class="code hl_enumvalue" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5ac428f74ca453dacb7b8271ca741266e8">MQTTASYNC_TRACE_ERROR</a>,</div>
<div class="line"><a id="l01723" name="l01723"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a3084770185f384398cefe4aaba533d40"> 1723</a></span>        <a class="code hl_enumvalue" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a3084770185f384398cefe4aaba533d40">MQTTASYNC_TRACE_SEVERE</a>,</div>
<div class="line"><a id="l01724" name="l01724"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a0b91d2213ebb6655e41a7f6ce1a42295"> 1724</a></span>        <a class="code hl_enumvalue" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a0b91d2213ebb6655e41a7f6ce1a42295">MQTTASYNC_TRACE_FATAL</a>,</div>
<div class="line"><a id="l01725" name="l01725"></a><span class="lineno"> 1725</span>};</div>
</div>
<div class="line"><a id="l01726" name="l01726"></a><span class="lineno"> 1726</span> </div>
<div class="line"><a id="l01727" name="l01727"></a><span class="lineno"> 1727</span> </div>
<div class="line"><a id="l01733" name="l01733"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ac7fbab13a0b2e5dd4ee11efbbb9f6a3a"> 1733</a></span>LIBMQTT_API <span class="keywordtype">void</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#ac7fbab13a0b2e5dd4ee11efbbb9f6a3a">MQTTAsync_setTraceLevel</a>(<span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5">MQTTASYNC_TRACE_LEVELS</a> level);</div>
<div class="line"><a id="l01734" name="l01734"></a><span class="lineno"> 1734</span> </div>
<div class="line"><a id="l01735" name="l01735"></a><span class="lineno"> 1735</span> </div>
<div class="line"><a id="l01745" name="l01745"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a65aba1caeae9b5af5d5b6c5598a75b02"> 1745</a></span><span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a65aba1caeae9b5af5d5b6c5598a75b02">MQTTAsync_traceCallback</a>(<span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5">MQTTASYNC_TRACE_LEVELS</a> level, <span class="keywordtype">char</span>* message);</div>
<div class="line"><a id="l01746" name="l01746"></a><span class="lineno"> 1746</span> </div>
<div class="line"><a id="l01753" name="l01753"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a0b350581324a4ff0eaee71e7a6721388"> 1753</a></span>LIBMQTT_API <span class="keywordtype">void</span> <a class="code hl_function" href="_m_q_t_t_async_8h.html#a0b350581324a4ff0eaee71e7a6721388">MQTTAsync_setTraceCallback</a>(<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a65aba1caeae9b5af5d5b6c5598a75b02">MQTTAsync_traceCallback</a>* callback);</div>
<div class="line"><a id="l01754" name="l01754"></a><span class="lineno"> 1754</span> </div>
<div class="line"><a id="l01761" name="l01761"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5b87bb24923a91e4947a6d598f278aa3"> 1761</a></span>LIBMQTT_API <a class="code hl_struct" href="struct_m_q_t_t_async__name_value.html">MQTTAsync_nameValue</a>* <a class="code hl_function" href="_m_q_t_t_async_8h.html#a5b87bb24923a91e4947a6d598f278aa3">MQTTAsync_getVersionInfo</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l01762" name="l01762"></a><span class="lineno"> 1762</span> </div>
<div class="line"><a id="l01769" name="l01769"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a7e739a09dbee6a0bda493222747a145f"> 1769</a></span>LIBMQTT_API <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_function" href="_m_q_t_t_async_8h.html#a7e739a09dbee6a0bda493222747a145f">MQTTAsync_strerror</a>(<span class="keywordtype">int</span> code);</div>
<div class="line"><a id="l01770" name="l01770"></a><span class="lineno"> 1770</span> </div>
<div class="line"><a id="l01771" name="l01771"></a><span class="lineno"> 1771</span> </div>
<div class="line"><a id="l02400" name="l02400"></a><span class="lineno"> 2400</span><span class="preprocessor">#if defined(__cplusplus)</span></div>
<div class="line"><a id="l02401" name="l02401"></a><span class="lineno"> 2401</span>     }</div>
<div class="line"><a id="l02402" name="l02402"></a><span class="lineno"> 2402</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02403" name="l02403"></a><span class="lineno"> 2403</span> </div>
<div class="line"><a id="l02404" name="l02404"></a><span class="lineno"> 2404</span><span class="preprocessor">#endif</span></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a0388b226a414b09fa733f6d65004ec32"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a0388b226a414b09fa733f6d65004ec32">MQTTAsync_connect</a></div><div class="ttdeci">int MQTTAsync_connect(MQTTAsync handle, const MQTTAsync_connectOptions *options)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a08d18ece91c1b011011354570d8ac1ab"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a08d18ece91c1b011011354570d8ac1ab">MQTTAsync_unsubscribe</a></div><div class="ttdeci">int MQTTAsync_unsubscribe(MQTTAsync handle, const char *topic, MQTTAsync_responseOptions *response)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a0b350581324a4ff0eaee71e7a6721388"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a0b350581324a4ff0eaee71e7a6721388">MQTTAsync_setTraceCallback</a></div><div class="ttdeci">void MQTTAsync_setTraceCallback(MQTTAsync_traceCallback *callback)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a0db1d736cdc0c864fe41abb3afd605bd"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a></div><div class="ttdeci">void * MQTTAsync</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:258</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a1002b09c62a096578c9b3e0135eb98c1"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a1002b09c62a096578c9b3e0135eb98c1">MQTTAsync_setBeforePersistenceWrite</a></div><div class="ttdeci">int MQTTAsync_setBeforePersistenceWrite(MQTTAsync handle, void *context, MQTTPersistence_beforeWrite *co)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a1705e75a48999cb45bf85c15608478f5"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a1705e75a48999cb45bf85c15608478f5">MQTTAsync_global_init</a></div><div class="ttdeci">void MQTTAsync_global_init(MQTTAsync_init_options *inits)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a18cc19740d9b00c629dc53a4420ecf1f"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a18cc19740d9b00c629dc53a4420ecf1f">MQTTAsync_setConnected</a></div><div class="ttdeci">int MQTTAsync_setConnected(MQTTAsync handle, void *context, MQTTAsync_connected *co)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a2b836f58612a2c4627e40ae848da190d"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a2b836f58612a2c4627e40ae848da190d">MQTTAsync_free</a></div><div class="ttdeci">void MQTTAsync_free(void *ptr)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a34bb8d321e9d368780b5c832c058f223"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a34bb8d321e9d368780b5c832c058f223">MQTTAsync_connected</a></div><div class="ttdeci">void MQTTAsync_connected(void *context, char *cause)</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:435</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a3900a98d7b1d58ad6e686bfce298bb6c"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">MQTTAsync_connectionLost</a></div><div class="ttdeci">void MQTTAsync_connectionLost(void *context, char *cause)</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:417</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a3918ead59b56816a8d7544def184e48e"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync_messageArrived</a></div><div class="ttdeci">int MQTTAsync_messageArrived(void *context, char *topicName, int topicLen, MQTTAsync_message *message)</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:374</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a44abc360051b918a39b0596a137775ae"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a44abc360051b918a39b0596a137775ae">MQTTAsync_setMessageArrivedCallback</a></div><div class="ttdeci">int MQTTAsync_setMessageArrivedCallback(MQTTAsync handle, void *context, MQTTAsync_messageArrived *ma)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a46c332245c379629ae11f457fc179457"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a46c332245c379629ae11f457fc179457">MQTTAsync_isConnected</a></div><div class="ttdeci">int MQTTAsync_isConnected(MQTTAsync handle)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a4fe09cc9c976b1cf424e13765d6cd8c9"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a4fe09cc9c976b1cf424e13765d6cd8c9">MQTTAsync_waitForCompletion</a></div><div class="ttdeci">int MQTTAsync_waitForCompletion(MQTTAsync handle, MQTTAsync_token token, unsigned long timeout)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a52a1d9ab6e5d5064a3de42d0eec88f57"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a52a1d9ab6e5d5064a3de42d0eec88f57">MQTTAsync_disconnected</a></div><div class="ttdeci">void MQTTAsync_disconnected(void *context, MQTTProperties *properties, enum MQTTReasonCodes reasonCode)</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:448</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5462c4618d0a229116db5fbadacf95d2"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create</a></div><div class="ttdeci">int MQTTAsync_create(MQTTAsync *handle, const char *serverURI, const char *clientId, int persistence_type, void *persistence_context)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5687171e67e98f9ea590c9e3b64cde18"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5687171e67e98f9ea590c9e3b64cde18">MQTTAsync_sendMessage</a></div><div class="ttdeci">int MQTTAsync_sendMessage(MQTTAsync handle, const char *destinationName, const MQTTAsync_message *msg, MQTTAsync_responseOptions *response)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5b87bb24923a91e4947a6d598f278aa3"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5b87bb24923a91e4947a6d598f278aa3">MQTTAsync_getVersionInfo</a></div><div class="ttdeci">MQTTAsync_nameValue * MQTTAsync_getVersionInfo(void)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5de816f986b318947709a34e0787eda5"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5">MQTTASYNC_TRACE_LEVELS</a></div><div class="ttdeci">MQTTASYNC_TRACE_LEVELS</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1717</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5de816f986b318947709a34e0787eda5a0b91d2213ebb6655e41a7f6ce1a42295"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a0b91d2213ebb6655e41a7f6ce1a42295">MQTTASYNC_TRACE_FATAL</a></div><div class="ttdeci">@ MQTTASYNC_TRACE_FATAL</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1724</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5de816f986b318947709a34e0787eda5a133c380b84d75477ff31a2ad732133ce"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a133c380b84d75477ff31a2ad732133ce">MQTTASYNC_TRACE_MEDIUM</a></div><div class="ttdeci">@ MQTTASYNC_TRACE_MEDIUM</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1719</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5de816f986b318947709a34e0787eda5a3084770185f384398cefe4aaba533d40"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a3084770185f384398cefe4aaba533d40">MQTTASYNC_TRACE_SEVERE</a></div><div class="ttdeci">@ MQTTASYNC_TRACE_SEVERE</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1723</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5de816f986b318947709a34e0787eda5a6a719b2b7fc4dfc41494370ff96fec3e"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a6a719b2b7fc4dfc41494370ff96fec3e">MQTTASYNC_TRACE_PROTOCOL</a></div><div class="ttdeci">@ MQTTASYNC_TRACE_PROTOCOL</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1721</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5de816f986b318947709a34e0787eda5a7a45c26816b1cac1fde02d79a9f4337b"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a7a45c26816b1cac1fde02d79a9f4337b">MQTTASYNC_TRACE_MINIMUM</a></div><div class="ttdeci">@ MQTTASYNC_TRACE_MINIMUM</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1720</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5de816f986b318947709a34e0787eda5ac428f74ca453dacb7b8271ca741266e8"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5ac428f74ca453dacb7b8271ca741266e8">MQTTASYNC_TRACE_ERROR</a></div><div class="ttdeci">@ MQTTASYNC_TRACE_ERROR</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1722</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5de816f986b318947709a34e0787eda5af684f42971cced68693ce993703548c1"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5af684f42971cced68693ce993703548c1">MQTTASYNC_TRACE_MAXIMUM</a></div><div class="ttdeci">@ MQTTASYNC_TRACE_MAXIMUM</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1718</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5e44304a2c011a7d61b72c779ad83979"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5e44304a2c011a7d61b72c779ad83979">MQTTAsync_updateConnectOptions</a></div><div class="ttdeci">int MQTTAsync_updateConnectOptions(void *context, MQTTAsync_connectData *data)</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:501</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a6060c25c2641e878803aef76fefb31ee"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync_onFailure</a></div><div class="ttdeci">void MQTTAsync_onFailure(void *context, MQTTAsync_failureData *response)</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:691</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a63c66a311ab16239a4175ff671871bf2"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a63c66a311ab16239a4175ff671871bf2">MQTTAsync_send</a></div><div class="ttdeci">int MQTTAsync_send(MQTTAsync handle, const char *destinationName, int payloadlen, const void *payload, int qos, int retained, MQTTAsync_responseOptions *response)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a65aba1caeae9b5af5d5b6c5598a75b02"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a65aba1caeae9b5af5d5b6c5598a75b02">MQTTAsync_traceCallback</a></div><div class="ttdeci">void MQTTAsync_traceCallback(enum MQTTASYNC_TRACE_LEVELS level, char *message)</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1745</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a69fd433ce1b9b6a1b3b453c4793a9311"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a69fd433ce1b9b6a1b3b453c4793a9311">MQTTAsync_unsubscribeMany</a></div><div class="ttdeci">int MQTTAsync_unsubscribeMany(MQTTAsync handle, int count, char *const *topic, MQTTAsync_responseOptions *response)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a78cbe1b851fea48001112f7ba9e4ea62"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a78cbe1b851fea48001112f7ba9e4ea62">MQTTAsync_createWithOptions</a></div><div class="ttdeci">int MQTTAsync_createWithOptions(MQTTAsync *handle, const char *serverURI, const char *clientId, int persistence_type, void *persistence_context, MQTTAsync_createOptions *options)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a7b0c18a0e29e2ce73f3ea109bc32617b"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess</a></div><div class="ttdeci">void MQTTAsync_onSuccess(void *context, MQTTAsync_successData *response)</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:660</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a7ca6d2a1813f2bbd0bc3af2771e46ba4"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a></div><div class="ttdeci">int MQTTAsync_token</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:268</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a7e739a09dbee6a0bda493222747a145f"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a7e739a09dbee6a0bda493222747a145f">MQTTAsync_strerror</a></div><div class="ttdeci">const char * MQTTAsync_strerror(int code)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a892cf122e6e8d8f6cd38c4c8efe8fb67"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync_onSuccess5</a></div><div class="ttdeci">void MQTTAsync_onSuccess5(void *context, MQTTAsync_successData5 *response)</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:676</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a8aa49e9e5fd6b6e1ca34cbaee9a28ee4"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a8aa49e9e5fd6b6e1ca34cbaee9a28ee4">MQTTAsync_malloc</a></div><div class="ttdeci">void * MQTTAsync_malloc(size_t size)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a8c5023e04d5c3e9805d5dae76df21f4c"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync_onFailure5</a></div><div class="ttdeci">void MQTTAsync_onFailure5(void *context, MQTTAsync_failureData5 *response)</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:706</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a94ec624ee22cc01d2ca58a9e646a2665"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a94ec624ee22cc01d2ca58a9e646a2665">MQTTAsync_setDeliveryCompleteCallback</a></div><div class="ttdeci">int MQTTAsync_setDeliveryCompleteCallback(MQTTAsync handle, void *context, MQTTAsync_deliveryComplete *dc)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a9b45db63052fe29ab1fad22d2a00c91c"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a9b45db63052fe29ab1fad22d2a00c91c">MQTTAsync_freeMessage</a></div><div class="ttdeci">void MQTTAsync_freeMessage(MQTTAsync_message **msg)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_aa078aec3eba83481f63db3c3939a5da9"><div class="ttname"><a href="_m_q_t_t_async_8h.html#aa078aec3eba83481f63db3c3939a5da9">MQTTAsync_setUpdateConnectOptions</a></div><div class="ttdeci">int MQTTAsync_setUpdateConnectOptions(MQTTAsync handle, void *context, MQTTAsync_updateConnectOptions *co)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ab10296618e266b3c02fd117d6616b15d"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync_deliveryComplete</a></div><div class="ttdeci">void MQTTAsync_deliveryComplete(void *context, MQTTAsync_token token)</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:397</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ab207095cab6f9a48b52cdb593b8456f4"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ab207095cab6f9a48b52cdb593b8456f4">MQTTAsync_isComplete</a></div><div class="ttdeci">int MQTTAsync_isComplete(MQTTAsync handle, MQTTAsync_token token)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ab4d16e3c57502be6a7d1b1d3bcc382f3"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ab4d16e3c57502be6a7d1b1d3bcc382f3">MQTTAsync_setAfterPersistenceRead</a></div><div class="ttdeci">int MQTTAsync_setAfterPersistenceRead(MQTTAsync handle, void *context, MQTTPersistence_afterRead *co)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_abc92f60743fc471643b473abbc987be0"><div class="ttname"><a href="_m_q_t_t_async_8h.html#abc92f60743fc471643b473abbc987be0">MQTTAsync_getPendingTokens</a></div><div class="ttdeci">int MQTTAsync_getPendingTokens(MQTTAsync handle, MQTTAsync_token **tokens)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_abd3ea01869b89ff23f9522640479c395"><div class="ttname"><a href="_m_q_t_t_async_8h.html#abd3ea01869b89ff23f9522640479c395">MQTTAsync_reconnect</a></div><div class="ttdeci">int MQTTAsync_reconnect(MQTTAsync handle)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ac78620b33434a187255bd1a3faec1578"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ac78620b33434a187255bd1a3faec1578">MQTTAsync_subscribeMany</a></div><div class="ttdeci">int MQTTAsync_subscribeMany(MQTTAsync handle, int count, char *const *topic, const int *qos, MQTTAsync_responseOptions *response)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ac7fbab13a0b2e5dd4ee11efbbb9f6a3a"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ac7fbab13a0b2e5dd4ee11efbbb9f6a3a">MQTTAsync_setTraceLevel</a></div><div class="ttdeci">void MQTTAsync_setTraceLevel(enum MQTTASYNC_TRACE_LEVELS level)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ad5562f9dc71fbd93d25ad20b328cb887"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync_destroy</a></div><div class="ttdeci">void MQTTAsync_destroy(MQTTAsync *handle)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ada4dd26d23c8849c51e4ab8200339040"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ada4dd26d23c8849c51e4ab8200339040">MQTTAsync_setDisconnected</a></div><div class="ttdeci">int MQTTAsync_setDisconnected(MQTTAsync handle, void *context, MQTTAsync_disconnected *co)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_adc69afa4725f8321bdaa5a05aec5cfd5"><div class="ttname"><a href="_m_q_t_t_async_8h.html#adc69afa4725f8321bdaa5a05aec5cfd5">MQTTAsync_disconnect</a></div><div class="ttdeci">int MQTTAsync_disconnect(MQTTAsync handle, const MQTTAsync_disconnectOptions *options)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ae10bd009934b3bb4a9f4abae7424a611"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ae10bd009934b3bb4a9f4abae7424a611">MQTTAsync_subscribe</a></div><div class="ttdeci">int MQTTAsync_subscribe(MQTTAsync handle, const char *topic, int qos, MQTTAsync_responseOptions *response)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ae1568d96d6418004cc79466c06f3d791"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ae1568d96d6418004cc79466c06f3d791">MQTTAsync_responseOptions</a></div><div class="ttdeci">struct MQTTAsync_responseOptions MQTTAsync_responseOptions</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ae9ae8d61023e7029ef5a19f5219c3599"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">MQTTAsync_setCallbacks</a></div><div class="ttdeci">int MQTTAsync_setCallbacks(MQTTAsync handle, void *context, MQTTAsync_connectionLost *cl, MQTTAsync_messageArrived *ma, MQTTAsync_deliveryComplete *dc)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_aee15bbd9224efd9dcce9b4ae491b2e2e"><div class="ttname"><a href="_m_q_t_t_async_8h.html#aee15bbd9224efd9dcce9b4ae491b2e2e">MQTTAsync_setConnectionLostCallback</a></div><div class="ttdeci">int MQTTAsync_setConnectionLostCallback(MQTTAsync handle, void *context, MQTTAsync_connectionLost *cl)</div></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html">MQTTClientPersistence.h</a></div><div class="ttdoc">This structure represents a persistent data store, used to store outbound and inbound messages,...</div></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html_ab865640a1cc53b68622004c5a2d29fae"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html#ab865640a1cc53b68622004c5a2d29fae">MQTTPersistence_beforeWrite</a></div><div class="ttdeci">int MQTTPersistence_beforeWrite(void *context, int bufcount, char *buffers[], int buflens[])</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:264</div></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html_af5a966a574c6ad7a35f1ebb7edd5c1c4"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html#af5a966a574c6ad7a35f1ebb7edd5c1c4">MQTTPersistence_afterRead</a></div><div class="ttdeci">int MQTTPersistence_afterRead(void *context, char **buffer, int *buflen)</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:275</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html"><div class="ttname"><a href="_m_q_t_t_properties_8h.html">MQTTProperties.h</a></div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html">MQTTReasonCodes.h</a></div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a></div><div class="ttdeci">MQTTReasonCodes</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:23</div></div>
<div class="ttc" id="a_m_q_t_t_subscribe_opts_8h_html"><div class="ttname"><a href="_m_q_t_t_subscribe_opts_8h.html">MQTTSubscribeOpts.h</a></div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html">MQTTAsync_SSLOptions</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1073</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a032835d4c4a1c1e19b53c330a673a6e0"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a032835d4c4a1c1e19b53c330a673a6e0">MQTTAsync_SSLOptions::trustStore</a></div><div class="ttdeci">const char * trustStore</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1087</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_SSLOptions::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1084</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a0826fcae7c2816e04772c61542c6846b"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a0826fcae7c2816e04772c61542c6846b">MQTTAsync_SSLOptions::disableDefaultTrustStore</a></div><div class="ttdeci">int disableDefaultTrustStore</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1165</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a189f11195f4d5a70024adffdb050885f"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a189f11195f4d5a70024adffdb050885f">MQTTAsync_SSLOptions::ssl_error_context</a></div><div class="ttdeci">void * ssl_error_context</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1145</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a26f5d839c92f9772c2a5d05486277a42"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a26f5d839c92f9772c2a5d05486277a42">MQTTAsync_SSLOptions::protos_len</a></div><div class="ttdeci">unsigned int protos_len</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1180</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a3078b3c824cc9753a57898072445c34d"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a3078b3c824cc9753a57898072445c34d">MQTTAsync_SSLOptions::CApath</a></div><div class="ttdeci">const char * CApath</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1133</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a32b476382955289ce427112b59f21c3e"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a32b476382955289ce427112b59f21c3e">MQTTAsync_SSLOptions::keyStore</a></div><div class="ttdeci">const char * keyStore</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1092</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a3543ea1481b68d73cdde833280bb9c45"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a3543ea1481b68d73cdde833280bb9c45">MQTTAsync_SSLOptions::sslVersion</a></div><div class="ttdeci">int sslVersion</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1119</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a4f8661600fb8bacf031150f8dcd293a5"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a4f8661600fb8bacf031150f8dcd293a5">MQTTAsync_SSLOptions::protos</a></div><div class="ttdeci">const unsigned char * protos</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1174</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a75f6c13b7634e15f96dd9f17db6cf0be"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a75f6c13b7634e15f96dd9f17db6cf0be">MQTTAsync_SSLOptions::enableServerCertAuth</a></div><div class="ttdeci">int enableServerCertAuth</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1113</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a7dd436cbb916fba200595c3519f09ec4"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a7dd436cbb916fba200595c3519f09ec4">MQTTAsync_SSLOptions::privateKey</a></div><div class="ttdeci">const char * privateKey</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1097</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a94900629685d5ed08f66fd2931f573ce"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a94900629685d5ed08f66fd2931f573ce">MQTTAsync_SSLOptions::verify</a></div><div class="ttdeci">int verify</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1126</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_aa683926d52134077f27d6dc67bda13ab"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#aa683926d52134077f27d6dc67bda13ab">MQTTAsync_SSLOptions::enabledCipherSuites</a></div><div class="ttdeci">const char * enabledCipherSuites</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1110</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_ab7f597518dd5b9db5a515081f8e0bd1f"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#ab7f597518dd5b9db5a515081f8e0bd1f">MQTTAsync_SSLOptions::ssl_psk_context</a></div><div class="ttdeci">void * ssl_psk_context</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1158</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_abb427571ba37b51f6985f1a6906ca031"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#abb427571ba37b51f6985f1a6906ca031">MQTTAsync_SSLOptions::privateKeyPassword</a></div><div class="ttdeci">const char * privateKeyPassword</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1100</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_data_html"><div class="ttname"><a href="struct_m_q_t_t_async__connect_data.html">MQTTAsync_connectData</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:470</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_data_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__connect_data.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_connectData::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:474</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_data_html_a0d49d74db4c035719c3867723cf7e779"><div class="ttname"><a href="struct_m_q_t_t_async__connect_data.html#a0d49d74db4c035719c3867723cf7e779">MQTTAsync_connectData::data</a></div><div class="ttdeci">const void * data</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:489</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_data_html_aba2dfcdfda80edcb531a5a7115d3e043"><div class="ttname"><a href="struct_m_q_t_t_async__connect_data.html#aba2dfcdfda80edcb531a5a7115d3e043">MQTTAsync_connectData::username</a></div><div class="ttdeci">const char * username</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:481</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_data_html_afed088663f8704004425cdae2120b9b3"><div class="ttname"><a href="struct_m_q_t_t_async__connect_data.html#afed088663f8704004425cdae2120b9b3">MQTTAsync_connectData::len</a></div><div class="ttdeci">int len</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:488</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html">MQTTAsync_connectOptions</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1203</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a035ba380dd97a284db04f4eaae5e113b"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a035ba380dd97a284db04f4eaae5e113b">MQTTAsync_connectOptions::maxRetryInterval</a></div><div class="ttdeci">int maxRetryInterval</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1341</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a036c36a2a4d3a3ffae9ab4dd8b3e7f7b"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">MQTTAsync_connectOptions::cleansession</a></div><div class="ttdeci">int cleansession</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1249</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_connectOptions::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1216</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a09ce26d7cff24e14a6844eaae7b15290"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a09ce26d7cff24e14a6844eaae7b15290">MQTTAsync_connectOptions::onFailure</a></div><div class="ttdeci">MQTTAsync_onFailure * onFailure</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1300</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a0d49d74db4c035719c3867723cf7e779"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a0d49d74db4c035719c3867723cf7e779">MQTTAsync_connectOptions::data</a></div><div class="ttdeci">const void * data</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1347</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a12d546fd0ccf4e1091b18e1b735c7240"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTAsync_connectOptions::MQTTVersion</a></div><div class="ttdeci">int MQTTVersion</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1329</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a166ac1b967f09326b0187f66be3e69af"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a166ac1b967f09326b0187f66be3e69af">MQTTAsync_connectOptions::minRetryInterval</a></div><div class="ttdeci">int minRetryInterval</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1337</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a1c23c490f06428725345de68a4ff0a3e"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a1c23c490f06428725345de68a4ff0a3e">MQTTAsync_connectOptions::onSuccess5</a></div><div class="ttdeci">MQTTAsync_onSuccess5 * onSuccess5</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1366</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a388b78d8a75658928238f700f207ad92"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a388b78d8a75658928238f700f207ad92">MQTTAsync_connectOptions::httpsProxy</a></div><div class="ttdeci">const char * httpsProxy</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1386</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a38c6aa24b36d981c49405db425c24db0"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a38c6aa24b36d981c49405db425c24db0">MQTTAsync_connectOptions::connectTimeout</a></div><div class="ttdeci">int connectTimeout</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1275</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a4dad726f2b6f79ca5847689c5f2f2ec2"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a4dad726f2b6f79ca5847689c5f2f2ec2">MQTTAsync_connectOptions::onFailure5</a></div><div class="ttdeci">MQTTAsync_onFailure5 * onFailure5</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1372</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a5c9d6c557453232a1b25cbbec5a31e8c"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a5c9d6c557453232a1b25cbbec5a31e8c">MQTTAsync_connectOptions::maxInflight</a></div><div class="ttdeci">int maxInflight</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1253</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a7902ce4d11b96d8b19582bdd1f82b630"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a7902ce4d11b96d8b19582bdd1f82b630">MQTTAsync_connectOptions::automaticReconnect</a></div><div class="ttdeci">int automaticReconnect</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1333</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a7a9c5105542460d6fd9323facca66648"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a7a9c5105542460d6fd9323facca66648">MQTTAsync_connectOptions::will</a></div><div class="ttdeci">MQTTAsync_willOptions * will</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1259</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a86fd59846f3ba2082fd99906c6b496a6"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a86fd59846f3ba2082fd99906c6b496a6">MQTTAsync_connectOptions::ssl</a></div><div class="ttdeci">MQTTAsync_SSLOptions * ssl</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1288</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a9f8b7ffb4a698eb151a3b090548b82e8"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a9f8b7ffb4a698eb151a3b090548b82e8">MQTTAsync_connectOptions::connectProperties</a></div><div class="ttdeci">MQTTProperties * connectProperties</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1356</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_aa4a2ebcb494493f648ae1e6975672575"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#aa4a2ebcb494493f648ae1e6975672575">MQTTAsync_connectOptions::password</a></div><div class="ttdeci">const char * password</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1271</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_aa82629005937abd92e97084a428cd61f"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#aa82629005937abd92e97084a428cd61f">MQTTAsync_connectOptions::serverURIcount</a></div><div class="ttdeci">int serverURIcount</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1310</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_aba22d81c407fb2ba590dba476240d3e9"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#aba22d81c407fb2ba590dba476240d3e9">MQTTAsync_connectOptions::serverURIs</a></div><div class="ttdeci">char *const  * serverURIs</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1322</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_aba2dfcdfda80edcb531a5a7115d3e043"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#aba2dfcdfda80edcb531a5a7115d3e043">MQTTAsync_connectOptions::username</a></div><div class="ttdeci">const char * username</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1265</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_ac13fb68f736854fcab131b34756bfceb"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#ac13fb68f736854fcab131b34756bfceb">MQTTAsync_connectOptions::onSuccess</a></div><div class="ttdeci">MQTTAsync_onSuccess * onSuccess</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1294</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_ac31f13e964ffb7e3696caef47ecc0641"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#ac31f13e964ffb7e3696caef47ecc0641">MQTTAsync_connectOptions::willProperties</a></div><div class="ttdeci">MQTTProperties * willProperties</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1360</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_ac4098248961a1ee89f40353eeebab58b"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#ac4098248961a1ee89f40353eeebab58b">MQTTAsync_connectOptions::httpHeaders</a></div><div class="ttdeci">const MQTTAsync_nameValue * httpHeaders</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1376</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_ac73f57846c42bcaa9a47e6721a957748"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#ac73f57846c42bcaa9a47e6721a957748">MQTTAsync_connectOptions::retryInterval</a></div><div class="ttdeci">int retryInterval</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1283</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_ac8dd0930672a9c7d71fc645aa1f0521d"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#ac8dd0930672a9c7d71fc645aa1f0521d">MQTTAsync_connectOptions::keepAliveInterval</a></div><div class="ttdeci">int keepAliveInterval</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1227</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_acdcb75a5d5981da027bce83849140f7b"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#acdcb75a5d5981da027bce83849140f7b">MQTTAsync_connectOptions::cleanstart</a></div><div class="ttdeci">int cleanstart</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1352</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_add124780ab2de397a96780576c2f112c"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#add124780ab2de397a96780576c2f112c">MQTTAsync_connectOptions::httpProxy</a></div><div class="ttdeci">const char * httpProxy</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1382</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_ae376f130b17d169ee51be68077a89ed0"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#ae376f130b17d169ee51be68077a89ed0">MQTTAsync_connectOptions::context</a></div><div class="ttdeci">void * context</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1306</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_afed088663f8704004425cdae2120b9b3"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#afed088663f8704004425cdae2120b9b3">MQTTAsync_connectOptions::len</a></div><div class="ttdeci">int len</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1346</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__create_options_html"><div class="ttname"><a href="struct_m_q_t_t_async__create_options.html">MQTTAsync_createOptions</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:965</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__create_options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__create_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_createOptions::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:973</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__create_options_html_a078cd68d8f896ce7eac0cc83d4486a2c"><div class="ttname"><a href="struct_m_q_t_t_async__create_options.html#a078cd68d8f896ce7eac0cc83d4486a2c">MQTTAsync_createOptions::sendWhileDisconnected</a></div><div class="ttdeci">int sendWhileDisconnected</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:975</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__create_options_html_a0c3ea2641e188542c787e71e2c521a0b"><div class="ttname"><a href="struct_m_q_t_t_async__create_options.html#a0c3ea2641e188542c787e71e2c521a0b">MQTTAsync_createOptions::persistQoS0</a></div><div class="ttdeci">int persistQoS0</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1001</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__create_options_html_a12d546fd0ccf4e1091b18e1b735c7240"><div class="ttname"><a href="struct_m_q_t_t_async__create_options.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTAsync_createOptions::MQTTVersion</a></div><div class="ttdeci">int MQTTVersion</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:985</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__create_options_html_a231b8890c3bc2ea07f7c599896f30691"><div class="ttname"><a href="struct_m_q_t_t_async__create_options.html#a231b8890c3bc2ea07f7c599896f30691">MQTTAsync_createOptions::restoreMessages</a></div><div class="ttdeci">int restoreMessages</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:997</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__create_options_html_a3b74acf6f315bb5fe36266bc9647ee97"><div class="ttname"><a href="struct_m_q_t_t_async__create_options.html#a3b74acf6f315bb5fe36266bc9647ee97">MQTTAsync_createOptions::maxBufferedMessages</a></div><div class="ttdeci">int maxBufferedMessages</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:979</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__create_options_html_a76de37b3cff885e83db204a347fe0a2d"><div class="ttname"><a href="struct_m_q_t_t_async__create_options.html#a76de37b3cff885e83db204a347fe0a2d">MQTTAsync_createOptions::deleteOldestMessages</a></div><div class="ttdeci">int deleteOldestMessages</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:993</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__create_options_html_abe7fdbe18bfd3577a75d3b386d69406c"><div class="ttname"><a href="struct_m_q_t_t_async__create_options.html#abe7fdbe18bfd3577a75d3b386d69406c">MQTTAsync_createOptions::allowDisconnectedSendAtAnyTime</a></div><div class="ttdeci">int allowDisconnectedSendAtAnyTime</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:989</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html">MQTTAsync_disconnectOptions</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1434</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_disconnectOptions::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1438</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_a09ce26d7cff24e14a6844eaae7b15290"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#a09ce26d7cff24e14a6844eaae7b15290">MQTTAsync_disconnectOptions::onFailure</a></div><div class="ttdeci">MQTTAsync_onFailure * onFailure</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1455</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_a1594008402f7307e4de8fa6131656dde"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#a1594008402f7307e4de8fa6131656dde">MQTTAsync_disconnectOptions::properties</a></div><div class="ttdeci">MQTTProperties properties</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1465</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_a1c23c490f06428725345de68a4ff0a3e"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#a1c23c490f06428725345de68a4ff0a3e">MQTTAsync_disconnectOptions::onSuccess5</a></div><div class="ttdeci">MQTTAsync_onSuccess5 * onSuccess5</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1475</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_a493b57f443cc38b3d3df9c1e584d9d82"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#a493b57f443cc38b3d3df9c1e584d9d82">MQTTAsync_disconnectOptions::timeout</a></div><div class="ttdeci">int timeout</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1443</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_a4dad726f2b6f79ca5847689c5f2f2ec2"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#a4dad726f2b6f79ca5847689c5f2f2ec2">MQTTAsync_disconnectOptions::onFailure5</a></div><div class="ttdeci">MQTTAsync_onFailure5 * onFailure5</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1481</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_a580d8a8ecb285f5a86c2a3865438f8ee"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#a580d8a8ecb285f5a86c2a3865438f8ee">MQTTAsync_disconnectOptions::reasonCode</a></div><div class="ttdeci">enum MQTTReasonCodes reasonCode</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1469</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_ac13fb68f736854fcab131b34756bfceb"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#ac13fb68f736854fcab131b34756bfceb">MQTTAsync_disconnectOptions::onSuccess</a></div><div class="ttdeci">MQTTAsync_onSuccess * onSuccess</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1449</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_ae376f130b17d169ee51be68077a89ed0"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#ae376f130b17d169ee51be68077a89ed0">MQTTAsync_disconnectOptions::context</a></div><div class="ttdeci">void * context</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1461</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data5_html"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data5.html">MQTTAsync_failureData5</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:553</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data5_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data5.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_failureData5::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:557</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data5_html_a1594008402f7307e4de8fa6131656dde"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data5.html#a1594008402f7307e4de8fa6131656dde">MQTTAsync_failureData5::properties</a></div><div class="ttdeci">MQTTProperties properties</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:563</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data5_html_a254bf0858da09c96a48daf64404eb4f8"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data5.html#a254bf0858da09c96a48daf64404eb4f8">MQTTAsync_failureData5::message</a></div><div class="ttdeci">const char * message</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:567</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data5_html_a38dfee9f038f473c95af46fcef5dd3e9"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data5.html#a38dfee9f038f473c95af46fcef5dd3e9">MQTTAsync_failureData5::packet_type</a></div><div class="ttdeci">int packet_type</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:569</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data5_html_a45a5b7c00a796a23f01673cef1dbe0a9"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data5.html#a45a5b7c00a796a23f01673cef1dbe0a9">MQTTAsync_failureData5::code</a></div><div class="ttdeci">int code</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:565</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data5_html_a580d8a8ecb285f5a86c2a3865438f8ee"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data5.html#a580d8a8ecb285f5a86c2a3865438f8ee">MQTTAsync_failureData5::reasonCode</a></div><div class="ttdeci">enum MQTTReasonCodes reasonCode</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:561</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data5_html_af8f771e67d284379111151b003c0d810"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data5.html#af8f771e67d284379111151b003c0d810">MQTTAsync_failureData5::token</a></div><div class="ttdeci">MQTTAsync_token token</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:559</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data_html"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data.html">MQTTAsync_failureData</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:541</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data_html_a254bf0858da09c96a48daf64404eb4f8"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data.html#a254bf0858da09c96a48daf64404eb4f8">MQTTAsync_failureData::message</a></div><div class="ttdeci">const char * message</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:547</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data_html_a45a5b7c00a796a23f01673cef1dbe0a9"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data.html#a45a5b7c00a796a23f01673cef1dbe0a9">MQTTAsync_failureData::code</a></div><div class="ttdeci">int code</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:545</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data_html_af8f771e67d284379111151b003c0d810"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data.html#af8f771e67d284379111151b003c0d810">MQTTAsync_failureData::token</a></div><div class="ttdeci">MQTTAsync_token token</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:543</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__init__options_html"><div class="ttname"><a href="struct_m_q_t_t_async__init__options.html">MQTTAsync_init_options</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:237</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__init__options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__init__options.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_init_options::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:241</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__init__options_html_a5929146596391e2838ef95feb89776da"><div class="ttname"><a href="struct_m_q_t_t_async__init__options.html#a5929146596391e2838ef95feb89776da">MQTTAsync_init_options::do_openssl_init</a></div><div class="ttdeci">int do_openssl_init</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:243</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html"><div class="ttname"><a href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:277</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_message::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:282</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_a1594008402f7307e4de8fa6131656dde"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#a1594008402f7307e4de8fa6131656dde">MQTTAsync_message::properties</a></div><div class="ttdeci">MQTTProperties properties</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:336</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_a35738099155a0e4f54050da474bab2e7"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#a35738099155a0e4f54050da474bab2e7">MQTTAsync_message::qos</a></div><div class="ttdeci">int qos</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:300</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_a6174c42da8c55c86e7255be2848dc4ac"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#a6174c42da8c55c86e7255be2848dc4ac">MQTTAsync_message::msgid</a></div><div class="ttdeci">int msgid</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:332</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_a6a4904c112507a43e7dc8495b62cc0fc"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#a6a4904c112507a43e7dc8495b62cc0fc">MQTTAsync_message::retained</a></div><div class="ttdeci">int retained</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:319</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_a9eff55064941fb604452abb0050ea99d"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#a9eff55064941fb604452abb0050ea99d">MQTTAsync_message::payload</a></div><div class="ttdeci">void * payload</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:286</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_aa3cb44feb3ae6d11b3a4cad2d94cb33a"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#aa3cb44feb3ae6d11b3a4cad2d94cb33a">MQTTAsync_message::payloadlen</a></div><div class="ttdeci">int payloadlen</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:284</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_adc4cf3f551bb367858644559d69cfdf5"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#adc4cf3f551bb367858644559d69cfdf5">MQTTAsync_message::dup</a></div><div class="ttdeci">int dup</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:326</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__name_value_html"><div class="ttname"><a href="struct_m_q_t_t_async__name_value.html">MQTTAsync_nameValue</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1187</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__name_value_html_a8556878012feffc9e0beb86cd78f424d"><div class="ttname"><a href="struct_m_q_t_t_async__name_value.html#a8556878012feffc9e0beb86cd78f424d">MQTTAsync_nameValue::value</a></div><div class="ttdeci">const char * value</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1189</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__name_value_html_a8f8f80d37794cde9472343e4487ba3eb"><div class="ttname"><a href="struct_m_q_t_t_async__name_value.html#a8f8f80d37794cde9472343e4487ba3eb">MQTTAsync_nameValue::name</a></div><div class="ttdeci">const char * name</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1188</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:714</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_responseOptions::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:719</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_a09ce26d7cff24e14a6844eaae7b15290"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#a09ce26d7cff24e14a6844eaae7b15290">MQTTAsync_responseOptions::onFailure</a></div><div class="ttdeci">MQTTAsync_onFailure * onFailure</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:731</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_a1594008402f7307e4de8fa6131656dde"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#a1594008402f7307e4de8fa6131656dde">MQTTAsync_responseOptions::properties</a></div><div class="ttdeci">MQTTProperties properties</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:760</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_a16a3cd2a8c69669e9ed6e420ccd9c517"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#a16a3cd2a8c69669e9ed6e420ccd9c517">MQTTAsync_responseOptions::subscribeOptions</a></div><div class="ttdeci">MQTTSubscribe_options subscribeOptions</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:764</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_a1a4b9bb2780472ec7bb65d0df1bf5d26"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#a1a4b9bb2780472ec7bb65d0df1bf5d26">MQTTAsync_responseOptions::subscribeOptionsCount</a></div><div class="ttdeci">int subscribeOptionsCount</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:769</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_a1c23c490f06428725345de68a4ff0a3e"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#a1c23c490f06428725345de68a4ff0a3e">MQTTAsync_responseOptions::onSuccess5</a></div><div class="ttdeci">MQTTAsync_onSuccess5 * onSuccess5</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:750</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_a4dad726f2b6f79ca5847689c5f2f2ec2"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#a4dad726f2b6f79ca5847689c5f2f2ec2">MQTTAsync_responseOptions::onFailure5</a></div><div class="ttdeci">MQTTAsync_onFailure5 * onFailure5</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:756</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_a98f71c5d03dc5ee86fd9dc0119ccb961"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#a98f71c5d03dc5ee86fd9dc0119ccb961">MQTTAsync_responseOptions::subscribeOptionsList</a></div><div class="ttdeci">MQTTSubscribe_options * subscribeOptionsList</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:773</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_aa5326df180cb23c59afbcab711a06479"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#aa5326df180cb23c59afbcab711a06479">MQTTAsync_responseOptions::struct_id</a></div><div class="ttdeci">char struct_id[4]</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:716</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_ac13fb68f736854fcab131b34756bfceb"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#ac13fb68f736854fcab131b34756bfceb">MQTTAsync_responseOptions::onSuccess</a></div><div class="ttdeci">MQTTAsync_onSuccess * onSuccess</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:725</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_ae376f130b17d169ee51be68077a89ed0"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#ae376f130b17d169ee51be68077a89ed0">MQTTAsync_responseOptions::context</a></div><div class="ttdeci">void * context</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:737</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_af8f771e67d284379111151b003c0d810"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#af8f771e67d284379111151b003c0d810">MQTTAsync_responseOptions::token</a></div><div class="ttdeci">MQTTAsync_token token</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:744</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html">MQTTAsync_successData5</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:607</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_successData5::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:609</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_a12d546fd0ccf4e1091b18e1b735c7240"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTAsync_successData5::MQTTVersion</a></div><div class="ttdeci">int MQTTVersion</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:633</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_a1594008402f7307e4de8fa6131656dde"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#a1594008402f7307e4de8fa6131656dde">MQTTAsync_successData5::properties</a></div><div class="ttdeci">MQTTProperties properties</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:613</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_a2199c9d905dbfa279895cf8123c10f4f"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#a2199c9d905dbfa279895cf8123c10f4f">MQTTAsync_successData5::reasonCodes</a></div><div class="ttdeci">enum MQTTReasonCodes * reasonCodes</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:621</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_a44baf2cb9a0bbcec3ed2eace43f832d1"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#a44baf2cb9a0bbcec3ed2eace43f832d1">MQTTAsync_successData5::sessionPresent</a></div><div class="ttdeci">int sessionPresent</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:634</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_a580d8a8ecb285f5a86c2a3865438f8ee"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#a580d8a8ecb285f5a86c2a3865438f8ee">MQTTAsync_successData5::reasonCode</a></div><div class="ttdeci">enum MQTTReasonCodes reasonCode</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:612</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_a6ed8403758cecd2f762af6ba5e0ae525"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#a6ed8403758cecd2f762af6ba5e0ae525">MQTTAsync_successData5::message</a></div><div class="ttdeci">MQTTAsync_message message</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:626</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_a95309fdf27015b12bc4adf56306e557b"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#a95309fdf27015b12bc4adf56306e557b">MQTTAsync_successData5::serverURI</a></div><div class="ttdeci">char * serverURI</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:632</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_ac97316626bd4faa6b71277c221275f4b"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#ac97316626bd4faa6b71277c221275f4b">MQTTAsync_successData5::reasonCodeCount</a></div><div class="ttdeci">int reasonCodeCount</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:620</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_ae25f4a1d2a3fa952d052a965376d8fef"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#ae25f4a1d2a3fa952d052a965376d8fef">MQTTAsync_successData5::destinationName</a></div><div class="ttdeci">char * destinationName</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:627</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_af8f771e67d284379111151b003c0d810"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#af8f771e67d284379111151b003c0d810">MQTTAsync_successData5::token</a></div><div class="ttdeci">MQTTAsync_token token</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:611</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html">MQTTAsync_successData</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:576</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html_a12d546fd0ccf4e1091b18e1b735c7240"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTAsync_successData::MQTTVersion</a></div><div class="ttdeci">int MQTTVersion</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:598</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html_a35738099155a0e4f54050da474bab2e7"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html#a35738099155a0e4f54050da474bab2e7">MQTTAsync_successData::qos</a></div><div class="ttdeci">int qos</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:584</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html_a44baf2cb9a0bbcec3ed2eace43f832d1"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html#a44baf2cb9a0bbcec3ed2eace43f832d1">MQTTAsync_successData::sessionPresent</a></div><div class="ttdeci">int sessionPresent</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:599</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html_a6ed8403758cecd2f762af6ba5e0ae525"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html#a6ed8403758cecd2f762af6ba5e0ae525">MQTTAsync_successData::message</a></div><div class="ttdeci">MQTTAsync_message message</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:591</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html_a82786d9ba5cae39873f378a48b36c23b"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html#a82786d9ba5cae39873f378a48b36c23b">MQTTAsync_successData::qosList</a></div><div class="ttdeci">int * qosList</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:587</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html_a95309fdf27015b12bc4adf56306e557b"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html#a95309fdf27015b12bc4adf56306e557b">MQTTAsync_successData::serverURI</a></div><div class="ttdeci">char * serverURI</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:597</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html_ae25f4a1d2a3fa952d052a965376d8fef"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html#ae25f4a1d2a3fa952d052a965376d8fef">MQTTAsync_successData::destinationName</a></div><div class="ttdeci">char * destinationName</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:592</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html_af8f771e67d284379111151b003c0d810"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html#af8f771e67d284379111151b003c0d810">MQTTAsync_successData::token</a></div><div class="ttdeci">MQTTAsync_token token</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:578</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__will_options_html"><div class="ttname"><a href="struct_m_q_t_t_async__will_options.html">MQTTAsync_willOptions</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1025</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__will_options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__will_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_willOptions::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1031</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__will_options_html_a0d49d74db4c035719c3867723cf7e779"><div class="ttname"><a href="struct_m_q_t_t_async__will_options.html#a0d49d74db4c035719c3867723cf7e779">MQTTAsync_willOptions::data</a></div><div class="ttdeci">const void * data</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1049</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__will_options_html_a0e20a7b350881d05108d6342884198a5"><div class="ttname"><a href="struct_m_q_t_t_async__will_options.html#a0e20a7b350881d05108d6342884198a5">MQTTAsync_willOptions::topicName</a></div><div class="ttdeci">const char * topicName</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1033</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__will_options_html_a254bf0858da09c96a48daf64404eb4f8"><div class="ttname"><a href="struct_m_q_t_t_async__will_options.html#a254bf0858da09c96a48daf64404eb4f8">MQTTAsync_willOptions::message</a></div><div class="ttdeci">const char * message</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1035</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__will_options_html_a35738099155a0e4f54050da474bab2e7"><div class="ttname"><a href="struct_m_q_t_t_async__will_options.html#a35738099155a0e4f54050da474bab2e7">MQTTAsync_willOptions::qos</a></div><div class="ttdeci">int qos</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1044</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__will_options_html_a6a4904c112507a43e7dc8495b62cc0fc"><div class="ttname"><a href="struct_m_q_t_t_async__will_options.html#a6a4904c112507a43e7dc8495b62cc0fc">MQTTAsync_willOptions::retained</a></div><div class="ttdeci">int retained</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1039</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__will_options_html_afed088663f8704004425cdae2120b9b3"><div class="ttname"><a href="struct_m_q_t_t_async__will_options.html#afed088663f8704004425cdae2120b9b3">MQTTAsync_willOptions::len</a></div><div class="ttdeci">int len</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1048</div></div>
<div class="ttc" id="astruct_m_q_t_t_properties_html"><div class="ttname"><a href="struct_m_q_t_t_properties.html">MQTTProperties</a></div><div class="ttdef"><b>Definition</b> MQTTProperties.h:116</div></div>
<div class="ttc" id="astruct_m_q_t_t_subscribe__options_html"><div class="ttname"><a href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a></div><div class="ttdef"><b>Definition</b> MQTTSubscribeOpts.h:22</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:07 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
