<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: MQTTProperty Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">MQTTProperty Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="_m_q_t_t_properties_8h_source.html">MQTTProperties.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a2ff04e8cc70fbaa9bcb9a4fb3d510882" id="r_a2ff04e8cc70fbaa9bcb9a4fb3d510882"><td class="memItemLeft" align="right" valign="top">enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2ff04e8cc70fbaa9bcb9a4fb3d510882">identifier</a></td></tr>
<tr class="separator:a2ff04e8cc70fbaa9bcb9a4fb3d510882"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af2307539b97777bec0475619af5648f1" id="r_af2307539b97777bec0475619af5648f1"><td class="memItemLeft" >union {&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1581cde4f73c9a797ae1e7afcc1bb3de" id="r_a1581cde4f73c9a797ae1e7afcc1bb3de"><td class="memItemLeft" >&#160;&#160;&#160;unsigned char&#160;&#160;&#160;<a class="el" href="#a1581cde4f73c9a797ae1e7afcc1bb3de">byte</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a1581cde4f73c9a797ae1e7afcc1bb3de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0289ec2e0df8789139386b0ddf5c71c3" id="r_a0289ec2e0df8789139386b0ddf5c71c3"><td class="memItemLeft" >&#160;&#160;&#160;unsigned short&#160;&#160;&#160;<a class="el" href="#a0289ec2e0df8789139386b0ddf5c71c3">integer2</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a0289ec2e0df8789139386b0ddf5c71c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a813425ef31abb5ef0091e3043e8a366b" id="r_a813425ef31abb5ef0091e3043e8a366b"><td class="memItemLeft" >&#160;&#160;&#160;unsigned int&#160;&#160;&#160;<a class="el" href="#a813425ef31abb5ef0091e3043e8a366b">integer4</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a813425ef31abb5ef0091e3043e8a366b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a49b55c8f2bc66616cdd6a2979c3178c4" id="r_a49b55c8f2bc66616cdd6a2979c3178c4"><td class="memItemLeft" >&#160;&#160;&#160;struct {&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa43ebcb9f97210421431a671384ef159" id="r_aa43ebcb9f97210421431a671384ef159"><td class="memItemLeft" >&#160;&#160;&#160;&#160;&#160;&#160;<a class="el" href="struct_m_q_t_t_len_string.html">MQTTLenString</a>&#160;&#160;&#160;<a class="el" href="#aa43ebcb9f97210421431a671384ef159">data</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:aa43ebcb9f97210421431a671384ef159"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09e85ff5ad73824d6c2edc1ce4283a17" id="r_a09e85ff5ad73824d6c2edc1ce4283a17"><td class="memItemLeft" >&#160;&#160;&#160;&#160;&#160;&#160;<a class="el" href="struct_m_q_t_t_len_string.html">MQTTLenString</a>&#160;&#160;&#160;<a class="el" href="#a09e85ff5ad73824d6c2edc1ce4283a17">value</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a09e85ff5ad73824d6c2edc1ce4283a17"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a49b55c8f2bc66616cdd6a2979c3178c4" id="r_a49b55c8f2bc66616cdd6a2979c3178c4"><td class="memItemLeft" valign="top">&#160;&#160;&#160;}&#160;</td><td class="memItemRight" valign="bottom">&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a49b55c8f2bc66616cdd6a2979c3178c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af2307539b97777bec0475619af5648f1" id="r_af2307539b97777bec0475619af5648f1"><td class="memItemLeft" valign="top">}&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af2307539b97777bec0475619af5648f1">value</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:af2307539b97777bec0475619af5648f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Structure to hold an MQTT version 5 property of any type </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="a2ff04e8cc70fbaa9bcb9a4fb3d510882" name="a2ff04e8cc70fbaa9bcb9a4fb3d510882"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2ff04e8cc70fbaa9bcb9a4fb3d510882">&#9670;&#160;</a></span>identifier</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> identifier</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The MQTT V5 property id. A multi-byte integer. </p>

</div>
</div>
<a id="a1581cde4f73c9a797ae1e7afcc1bb3de" name="a1581cde4f73c9a797ae1e7afcc1bb3de"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1581cde4f73c9a797ae1e7afcc1bb3de">&#9670;&#160;</a></span>byte</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">unsigned char byte</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>holds the value of a byte property type </p>

</div>
</div>
<a id="a0289ec2e0df8789139386b0ddf5c71c3" name="a0289ec2e0df8789139386b0ddf5c71c3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0289ec2e0df8789139386b0ddf5c71c3">&#9670;&#160;</a></span>integer2</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">unsigned short integer2</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>holds the value of a 2 byte integer property type </p>

</div>
</div>
<a id="a813425ef31abb5ef0091e3043e8a366b" name="a813425ef31abb5ef0091e3043e8a366b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a813425ef31abb5ef0091e3043e8a366b">&#9670;&#160;</a></span>integer4</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">unsigned int integer4</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>holds the value of a 4 byte integer property type </p>

</div>
</div>
<a id="aa43ebcb9f97210421431a671384ef159" name="aa43ebcb9f97210421431a671384ef159"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa43ebcb9f97210421431a671384ef159">&#9670;&#160;</a></span>data</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_len_string.html">MQTTLenString</a> data</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The value of a string property, or the name of a user property. </p>

</div>
</div>
<a id="a09e85ff5ad73824d6c2edc1ce4283a17" name="a09e85ff5ad73824d6c2edc1ce4283a17"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a09e85ff5ad73824d6c2edc1ce4283a17">&#9670;&#160;</a></span>value <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_len_string.html">MQTTLenString</a> value</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The value of a user property. </p>

</div>
</div>
<a id="af2307539b97777bec0475619af5648f1" name="af2307539b97777bec0475619af5648f1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af2307539b97777bec0475619af5648f1">&#9670;&#160;</a></span>[union] <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union  { ... }  value</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The value of the property, as a union of the different possible types. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_m_q_t_t_properties_8h_source.html">MQTTProperties.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:08 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
