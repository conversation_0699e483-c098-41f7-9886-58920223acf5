<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all functions with links to the files they belong to:</div>

<h3><a id="index_m" name="index_m"></a>- m -</h3><ul>
<li>MQTTAsync_connect()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a0388b226a414b09fa733f6d65004ec32">MQTTAsync.h</a></li>
<li>MQTTAsync_create()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync.h</a></li>
<li>MQTTAsync_createWithOptions()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a78cbe1b851fea48001112f7ba9e4ea62">MQTTAsync.h</a></li>
<li>MQTTAsync_destroy()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync.h</a></li>
<li>MQTTAsync_disconnect()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#adc69afa4725f8321bdaa5a05aec5cfd5">MQTTAsync.h</a></li>
<li>MQTTAsync_free()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a2b836f58612a2c4627e40ae848da190d">MQTTAsync.h</a></li>
<li>MQTTAsync_freeMessage()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a9b45db63052fe29ab1fad22d2a00c91c">MQTTAsync.h</a></li>
<li>MQTTAsync_getPendingTokens()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#abc92f60743fc471643b473abbc987be0">MQTTAsync.h</a></li>
<li>MQTTAsync_getVersionInfo()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a5b87bb24923a91e4947a6d598f278aa3">MQTTAsync.h</a></li>
<li>MQTTAsync_global_init()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a1705e75a48999cb45bf85c15608478f5">MQTTAsync.h</a></li>
<li>MQTTAsync_isComplete()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#ab207095cab6f9a48b52cdb593b8456f4">MQTTAsync.h</a></li>
<li>MQTTAsync_isConnected()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a46c332245c379629ae11f457fc179457">MQTTAsync.h</a></li>
<li>MQTTAsync_malloc()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a8aa49e9e5fd6b6e1ca34cbaee9a28ee4">MQTTAsync.h</a></li>
<li>MQTTAsync_reconnect()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#abd3ea01869b89ff23f9522640479c395">MQTTAsync.h</a></li>
<li>MQTTAsync_send()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a63c66a311ab16239a4175ff671871bf2">MQTTAsync.h</a></li>
<li>MQTTAsync_sendMessage()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a5687171e67e98f9ea590c9e3b64cde18">MQTTAsync.h</a></li>
<li>MQTTAsync_setAfterPersistenceRead()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#ab4d16e3c57502be6a7d1b1d3bcc382f3">MQTTAsync.h</a></li>
<li>MQTTAsync_setBeforePersistenceWrite()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a1002b09c62a096578c9b3e0135eb98c1">MQTTAsync.h</a></li>
<li>MQTTAsync_setCallbacks()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">MQTTAsync.h</a></li>
<li>MQTTAsync_setConnected()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a18cc19740d9b00c629dc53a4420ecf1f">MQTTAsync.h</a></li>
<li>MQTTAsync_setConnectionLostCallback()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#aee15bbd9224efd9dcce9b4ae491b2e2e">MQTTAsync.h</a></li>
<li>MQTTAsync_setDeliveryCompleteCallback()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a94ec624ee22cc01d2ca58a9e646a2665">MQTTAsync.h</a></li>
<li>MQTTAsync_setDisconnected()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#ada4dd26d23c8849c51e4ab8200339040">MQTTAsync.h</a></li>
<li>MQTTAsync_setMessageArrivedCallback()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a44abc360051b918a39b0596a137775ae">MQTTAsync.h</a></li>
<li>MQTTAsync_setTraceCallback()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a0b350581324a4ff0eaee71e7a6721388">MQTTAsync.h</a></li>
<li>MQTTAsync_setTraceLevel()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#ac7fbab13a0b2e5dd4ee11efbbb9f6a3a">MQTTAsync.h</a></li>
<li>MQTTAsync_setUpdateConnectOptions()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#aa078aec3eba83481f63db3c3939a5da9">MQTTAsync.h</a></li>
<li>MQTTAsync_strerror()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a7e739a09dbee6a0bda493222747a145f">MQTTAsync.h</a></li>
<li>MQTTAsync_subscribe()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#ae10bd009934b3bb4a9f4abae7424a611">MQTTAsync.h</a></li>
<li>MQTTAsync_subscribeMany()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#ac78620b33434a187255bd1a3faec1578">MQTTAsync.h</a></li>
<li>MQTTAsync_unsubscribe()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a08d18ece91c1b011011354570d8ac1ab">MQTTAsync.h</a></li>
<li>MQTTAsync_unsubscribeMany()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a69fd433ce1b9b6a1b3b453c4793a9311">MQTTAsync.h</a></li>
<li>MQTTAsync_waitForCompletion()&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a4fe09cc9c976b1cf424e13765d6cd8c9">MQTTAsync.h</a></li>
<li>MQTTProperties_add()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a88f1d21556c2d23330d71357cd226a15">MQTTProperties.h</a></li>
<li>MQTTProperties_copy()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a69b3e474ee2f828e5b827d615fe0fe72">MQTTProperties.h</a></li>
<li>MQTTProperties_free()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#ab68247ed365ee51170a9309c828b1823">MQTTProperties.h</a></li>
<li>MQTTProperties_getNumericValue()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#ad8643f0e68deb29e16ef88fc225e03c2">MQTTProperties.h</a></li>
<li>MQTTProperties_getNumericValueAt()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a1d4f3fd86fc47241fefe623556d99ea9">MQTTProperties.h</a></li>
<li>MQTTProperties_getProperty()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#ac5c49930de80af1e0df0f0584f142078">MQTTProperties.h</a></li>
<li>MQTTProperties_getPropertyAt()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a345b64fc33afd05148aa018c24c42c80">MQTTProperties.h</a></li>
<li>MQTTProperties_hasProperty()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#abde30a2a44f41c649bd84f4d1467b72c">MQTTProperties.h</a></li>
<li>MQTTProperties_len()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a2850f38d4ff89af52999fdc42cdff6fa">MQTTProperties.h</a></li>
<li>MQTTProperties_propertyCount()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#ac7ea96a57ad09e9d0fc3203d008f52aa">MQTTProperties.h</a></li>
<li>MQTTProperties_read()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#afcb874dfcc9f0eaa0b063e2fad740871">MQTTProperties.h</a></li>
<li>MQTTProperties_write()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#ade0027a4e571bd288fe40271ff7aa497">MQTTProperties.h</a></li>
<li>MQTTProperty_getType()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a7d30ad0520bc9b9366e700d4b493b173">MQTTProperties.h</a></li>
<li>MQTTPropertyName()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a69d277e6a0f27a05279eca2736e09840">MQTTProperties.h</a></li>
<li>MQTTReasonCode_toString()&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#a385737b840eb180f35b9a714ea295ceb">MQTTReasonCodes.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:08 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
