<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: Publication example</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div><div class="header">
  <div class="headertitle"><div class="title">Publication example</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><div class="fragment"><div class="line"><span class="preprocessor">#include &lt;stdio.h&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;stdlib.h&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;string.h&gt;</span></div>
<div class="line"><span class="preprocessor">#include &quot;<a class="code" href="_m_q_t_t_async_8h.html">MQTTAsync.h</a>&quot;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#if !defined(_WIN32)</span></div>
<div class="line"><span class="preprocessor">#include &lt;unistd.h&gt;</span></div>
<div class="line"><span class="preprocessor">#else</span></div>
<div class="line"><span class="preprocessor">#include &lt;windows.h&gt;</span></div>
<div class="line"><span class="preprocessor">#endif</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#if defined(_WRS_KERNEL)</span></div>
<div class="line"><span class="preprocessor">#include &lt;OsWrapper.h&gt;</span></div>
<div class="line"><span class="preprocessor">#endif</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#define ADDRESS     &quot;tcp://mqtt.eclipseprojects.io:1883&quot;</span></div>
<div class="line"><span class="preprocessor">#define CLIENTID    &quot;ExampleClientPub&quot;</span></div>
<div class="line"><span class="preprocessor">#define TOPIC       &quot;MQTT Examples&quot;</span></div>
<div class="line"><span class="preprocessor">#define PAYLOAD     &quot;Hello World!&quot;</span></div>
<div class="line"><span class="preprocessor">#define QOS         1</span></div>
<div class="line"><span class="preprocessor">#define TIMEOUT     10000L</span></div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> finished = 0;</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> connlost(<span class="keywordtype">void</span> *context, <span class="keywordtype">char</span> *cause)</div>
<div class="line">{</div>
<div class="line">        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> client = (<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>)context;</div>
<div class="line">        <a class="code hl_struct" href="struct_m_q_t_t_async__connect_options.html">MQTTAsync_connectOptions</a> conn_opts = <a class="code hl_define" href="_m_q_t_t_async_8h.html#ae18b51f22784a43803eb809d6a0c2492">MQTTAsync_connectOptions_initializer</a>;</div>
<div class="line">        <span class="keywordtype">int</span> rc;</div>
<div class="line"> </div>
<div class="line">        printf(<span class="stringliteral">&quot;\nConnection lost\n&quot;</span>);</div>
<div class="line">        printf(<span class="stringliteral">&quot;     cause: %s\n&quot;</span>, cause);</div>
<div class="line"> </div>
<div class="line">        printf(<span class="stringliteral">&quot;Reconnecting\n&quot;</span>);</div>
<div class="line">        conn_opts.<a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#ac8dd0930672a9c7d71fc645aa1f0521d">keepAliveInterval</a> = 20;</div>
<div class="line">        conn_opts.<a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">cleansession</a> = 1;</div>
<div class="line">        <span class="keywordflow">if</span> ((rc = <a class="code hl_function" href="_m_q_t_t_async_8h.html#a0388b226a414b09fa733f6d65004ec32">MQTTAsync_connect</a>(client, &amp;conn_opts)) != <a class="code hl_define" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a>)</div>
<div class="line">        {</div>
<div class="line">                printf(<span class="stringliteral">&quot;Failed to start connect, return code %d\n&quot;</span>, rc);</div>
<div class="line">                finished = 1;</div>
<div class="line">        }</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> onDisconnectFailure(<span class="keywordtype">void</span>* context, <a class="code hl_struct" href="struct_m_q_t_t_async__failure_data.html">MQTTAsync_failureData</a>* response)</div>
<div class="line">{</div>
<div class="line">        printf(<span class="stringliteral">&quot;Disconnect failed\n&quot;</span>);</div>
<div class="line">        finished = 1;</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> onDisconnect(<span class="keywordtype">void</span>* context, <a class="code hl_struct" href="struct_m_q_t_t_async__success_data.html">MQTTAsync_successData</a>* response)</div>
<div class="line">{</div>
<div class="line">        printf(<span class="stringliteral">&quot;Successful disconnection\n&quot;</span>);</div>
<div class="line">        finished = 1;</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> onSendFailure(<span class="keywordtype">void</span>* context, <a class="code hl_struct" href="struct_m_q_t_t_async__failure_data.html">MQTTAsync_failureData</a>* response)</div>
<div class="line">{</div>
<div class="line">        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> client = (<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>)context;</div>
<div class="line">        <a class="code hl_struct" href="struct_m_q_t_t_async__disconnect_options.html">MQTTAsync_disconnectOptions</a> opts = <a class="code hl_define" href="_m_q_t_t_async_8h.html#a2fd5d6df31928ae468f3f2e522b9c707">MQTTAsync_disconnectOptions_initializer</a>;</div>
<div class="line">        <span class="keywordtype">int</span> rc;</div>
<div class="line"> </div>
<div class="line">        printf(<span class="stringliteral">&quot;Message send failed token %d error code %d\n&quot;</span>, response-&gt;<a class="code hl_variable" href="struct_m_q_t_t_async__failure_data.html#af8f771e67d284379111151b003c0d810">token</a>, response-&gt;<a class="code hl_variable" href="struct_m_q_t_t_async__failure_data.html#a45a5b7c00a796a23f01673cef1dbe0a9">code</a>);</div>
<div class="line">        opts.<a class="code hl_variable" href="struct_m_q_t_t_async__disconnect_options.html#ac13fb68f736854fcab131b34756bfceb">onSuccess</a> = onDisconnect;</div>
<div class="line">        opts.<a class="code hl_variable" href="struct_m_q_t_t_async__disconnect_options.html#a09ce26d7cff24e14a6844eaae7b15290">onFailure</a> = onDisconnectFailure;</div>
<div class="line">        opts.<a class="code hl_variable" href="struct_m_q_t_t_async__disconnect_options.html#ae376f130b17d169ee51be68077a89ed0">context</a> = client;</div>
<div class="line">        <span class="keywordflow">if</span> ((rc = <a class="code hl_function" href="_m_q_t_t_async_8h.html#adc69afa4725f8321bdaa5a05aec5cfd5">MQTTAsync_disconnect</a>(client, &amp;opts)) != <a class="code hl_define" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a>)</div>
<div class="line">        {</div>
<div class="line">                printf(<span class="stringliteral">&quot;Failed to start disconnect, return code %d\n&quot;</span>, rc);</div>
<div class="line">                exit(EXIT_FAILURE);</div>
<div class="line">        }</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> onSend(<span class="keywordtype">void</span>* context, <a class="code hl_struct" href="struct_m_q_t_t_async__success_data.html">MQTTAsync_successData</a>* response)</div>
<div class="line">{</div>
<div class="line">        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> client = (<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>)context;</div>
<div class="line">        <a class="code hl_struct" href="struct_m_q_t_t_async__disconnect_options.html">MQTTAsync_disconnectOptions</a> opts = <a class="code hl_define" href="_m_q_t_t_async_8h.html#a2fd5d6df31928ae468f3f2e522b9c707">MQTTAsync_disconnectOptions_initializer</a>;</div>
<div class="line">        <span class="keywordtype">int</span> rc;</div>
<div class="line"> </div>
<div class="line">        printf(<span class="stringliteral">&quot;Message with token value %d delivery confirmed\n&quot;</span>, response-&gt;<a class="code hl_variable" href="struct_m_q_t_t_async__success_data.html#af8f771e67d284379111151b003c0d810">token</a>);</div>
<div class="line">        opts.<a class="code hl_variable" href="struct_m_q_t_t_async__disconnect_options.html#ac13fb68f736854fcab131b34756bfceb">onSuccess</a> = onDisconnect;</div>
<div class="line">        opts.<a class="code hl_variable" href="struct_m_q_t_t_async__disconnect_options.html#a09ce26d7cff24e14a6844eaae7b15290">onFailure</a> = onDisconnectFailure;</div>
<div class="line">        opts.<a class="code hl_variable" href="struct_m_q_t_t_async__disconnect_options.html#ae376f130b17d169ee51be68077a89ed0">context</a> = client;</div>
<div class="line">        <span class="keywordflow">if</span> ((rc = <a class="code hl_function" href="_m_q_t_t_async_8h.html#adc69afa4725f8321bdaa5a05aec5cfd5">MQTTAsync_disconnect</a>(client, &amp;opts)) != <a class="code hl_define" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a>)</div>
<div class="line">        {</div>
<div class="line">                printf(<span class="stringliteral">&quot;Failed to start disconnect, return code %d\n&quot;</span>, rc);</div>
<div class="line">                exit(EXIT_FAILURE);</div>
<div class="line">        }</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> onConnectFailure(<span class="keywordtype">void</span>* context, <a class="code hl_struct" href="struct_m_q_t_t_async__failure_data.html">MQTTAsync_failureData</a>* response)</div>
<div class="line">{</div>
<div class="line">        printf(<span class="stringliteral">&quot;Connect failed, rc %d\n&quot;</span>, response ? response-&gt;<a class="code hl_variable" href="struct_m_q_t_t_async__failure_data.html#a45a5b7c00a796a23f01673cef1dbe0a9">code</a> : 0);</div>
<div class="line">        finished = 1;</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> onConnect(<span class="keywordtype">void</span>* context, <a class="code hl_struct" href="struct_m_q_t_t_async__success_data.html">MQTTAsync_successData</a>* response)</div>
<div class="line">{</div>
<div class="line">        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> client = (<a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>)context;</div>
<div class="line">        <a class="code hl_struct" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> opts = <a class="code hl_define" href="_m_q_t_t_async_8h.html#a3f8b408243b5c2369bc9758f2edf0878">MQTTAsync_responseOptions_initializer</a>;</div>
<div class="line">        <a class="code hl_struct" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a> pubmsg = <a class="code hl_define" href="_m_q_t_t_async_8h.html#a6a85061dadab532f28e96e5ab3c600e9">MQTTAsync_message_initializer</a>;</div>
<div class="line">        <span class="keywordtype">int</span> rc;</div>
<div class="line"> </div>
<div class="line">        printf(<span class="stringliteral">&quot;Successful connection\n&quot;</span>);</div>
<div class="line">        opts.<a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#ac13fb68f736854fcab131b34756bfceb">onSuccess</a> = onSend;</div>
<div class="line">        opts.<a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#a09ce26d7cff24e14a6844eaae7b15290">onFailure</a> = onSendFailure;</div>
<div class="line">        opts.<a class="code hl_variable" href="struct_m_q_t_t_async__response_options.html#ae376f130b17d169ee51be68077a89ed0">context</a> = client;</div>
<div class="line">        pubmsg.<a class="code hl_variable" href="struct_m_q_t_t_async__message.html#a9eff55064941fb604452abb0050ea99d">payload</a> = PAYLOAD;</div>
<div class="line">        pubmsg.<a class="code hl_variable" href="struct_m_q_t_t_async__message.html#aa3cb44feb3ae6d11b3a4cad2d94cb33a">payloadlen</a> = (int)strlen(PAYLOAD);</div>
<div class="line">        pubmsg.<a class="code hl_variable" href="struct_m_q_t_t_async__message.html#a35738099155a0e4f54050da474bab2e7">qos</a> = QOS;</div>
<div class="line">        pubmsg.<a class="code hl_variable" href="struct_m_q_t_t_async__message.html#a6a4904c112507a43e7dc8495b62cc0fc">retained</a> = 0;</div>
<div class="line">        <span class="keywordflow">if</span> ((rc = <a class="code hl_function" href="_m_q_t_t_async_8h.html#a5687171e67e98f9ea590c9e3b64cde18">MQTTAsync_sendMessage</a>(client, TOPIC, &amp;pubmsg, &amp;opts)) != <a class="code hl_define" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a>)</div>
<div class="line">        {</div>
<div class="line">                printf(<span class="stringliteral">&quot;Failed to start sendMessage, return code %d\n&quot;</span>, rc);</div>
<div class="line">                exit(EXIT_FAILURE);</div>
<div class="line">        }</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> messageArrived(<span class="keywordtype">void</span>* context, <span class="keywordtype">char</span>* topicName, <span class="keywordtype">int</span> topicLen, <a class="code hl_struct" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a>* m)</div>
<div class="line">{</div>
<div class="line">        <span class="comment">// not expecting any messages</span></div>
<div class="line">        <span class="keywordflow">return</span> 1;</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main(<span class="keywordtype">int</span> argc, <span class="keywordtype">char</span>* argv[])</div>
<div class="line">{</div>
<div class="line">        <a class="code hl_typedef" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> client;</div>
<div class="line">        <a class="code hl_struct" href="struct_m_q_t_t_async__connect_options.html">MQTTAsync_connectOptions</a> conn_opts = <a class="code hl_define" href="_m_q_t_t_async_8h.html#ae18b51f22784a43803eb809d6a0c2492">MQTTAsync_connectOptions_initializer</a>;</div>
<div class="line">        <span class="keywordtype">int</span> rc;</div>
<div class="line"> </div>
<div class="line">        <span class="keywordflow">if</span> ((rc = <a class="code hl_function" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create</a>(&amp;client, ADDRESS, CLIENTID, <a class="code hl_define" href="_m_q_t_t_client_persistence_8h.html#ae01e089313a65ac4661ed216b6ac00fa">MQTTCLIENT_PERSISTENCE_NONE</a>, NULL)) != <a class="code hl_define" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a>)</div>
<div class="line">        {</div>
<div class="line">                printf(<span class="stringliteral">&quot;Failed to create client object, return code %d\n&quot;</span>, rc);</div>
<div class="line">                exit(EXIT_FAILURE);</div>
<div class="line">        }</div>
<div class="line"> </div>
<div class="line">        <span class="keywordflow">if</span> ((rc = <a class="code hl_function" href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">MQTTAsync_setCallbacks</a>(client, NULL, connlost, messageArrived, NULL)) != <a class="code hl_define" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a>)</div>
<div class="line">        {</div>
<div class="line">                printf(<span class="stringliteral">&quot;Failed to set callback, return code %d\n&quot;</span>, rc);</div>
<div class="line">                exit(EXIT_FAILURE);</div>
<div class="line">        }</div>
<div class="line"> </div>
<div class="line">        conn_opts.<a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#ac8dd0930672a9c7d71fc645aa1f0521d">keepAliveInterval</a> = 20;</div>
<div class="line">        conn_opts.<a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">cleansession</a> = 1;</div>
<div class="line">        conn_opts.<a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#ac13fb68f736854fcab131b34756bfceb">onSuccess</a> = onConnect;</div>
<div class="line">        conn_opts.<a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#a09ce26d7cff24e14a6844eaae7b15290">onFailure</a> = onConnectFailure;</div>
<div class="line">        conn_opts.<a class="code hl_variable" href="struct_m_q_t_t_async__connect_options.html#ae376f130b17d169ee51be68077a89ed0">context</a> = client;</div>
<div class="line">        <span class="keywordflow">if</span> ((rc = <a class="code hl_function" href="_m_q_t_t_async_8h.html#a0388b226a414b09fa733f6d65004ec32">MQTTAsync_connect</a>(client, &amp;conn_opts)) != <a class="code hl_define" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a>)</div>
<div class="line">        {</div>
<div class="line">                printf(<span class="stringliteral">&quot;Failed to start connect, return code %d\n&quot;</span>, rc);</div>
<div class="line">                exit(EXIT_FAILURE);</div>
<div class="line">        }</div>
<div class="line"> </div>
<div class="line">        printf(<span class="stringliteral">&quot;Waiting for publication of %s\n&quot;</span></div>
<div class="line">         <span class="stringliteral">&quot;on topic %s for client with ClientID: %s\n&quot;</span>,</div>
<div class="line">         PAYLOAD, TOPIC, CLIENTID);</div>
<div class="line">        <span class="keywordflow">while</span> (!finished)</div>
<div class="line"><span class="preprocessor">                #if defined(_WIN32)</span></div>
<div class="line">                        Sleep(100);</div>
<div class="line"><span class="preprocessor">                #else</span></div>
<div class="line">                        usleep(10000L);</div>
<div class="line"><span class="preprocessor">                #endif</span></div>
<div class="line"> </div>
<div class="line">        <a class="code hl_function" href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync_destroy</a>(&amp;client);</div>
<div class="line">        <span class="keywordflow">return</span> rc;</div>
<div class="line">}</div>
<div class="ttc" id="a_m_q_t_t_async_8h_html"><div class="ttname"><a href="_m_q_t_t_async_8h.html">MQTTAsync.h</a></div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a0388b226a414b09fa733f6d65004ec32"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a0388b226a414b09fa733f6d65004ec32">MQTTAsync_connect</a></div><div class="ttdeci">int MQTTAsync_connect(MQTTAsync handle, const MQTTAsync_connectOptions *options)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a0db1d736cdc0c864fe41abb3afd605bd"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a></div><div class="ttdeci">void * MQTTAsync</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:258</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a2fd5d6df31928ae468f3f2e522b9c707"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a2fd5d6df31928ae468f3f2e522b9c707">MQTTAsync_disconnectOptions_initializer</a></div><div class="ttdeci">#define MQTTAsync_disconnectOptions_initializer</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1484</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a3f8b408243b5c2369bc9758f2edf0878"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a3f8b408243b5c2369bc9758f2edf0878">MQTTAsync_responseOptions_initializer</a></div><div class="ttdeci">#define MQTTAsync_responseOptions_initializer</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:776</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5462c4618d0a229116db5fbadacf95d2"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create</a></div><div class="ttdeci">int MQTTAsync_create(MQTTAsync *handle, const char *serverURI, const char *clientId, int persistence_type, void *persistence_context)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5687171e67e98f9ea590c9e3b64cde18"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5687171e67e98f9ea590c9e3b64cde18">MQTTAsync_sendMessage</a></div><div class="ttdeci">int MQTTAsync_sendMessage(MQTTAsync handle, const char *destinationName, const MQTTAsync_message *msg, MQTTAsync_responseOptions *response)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a6a85061dadab532f28e96e5ab3c600e9"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a6a85061dadab532f28e96e5ab3c600e9">MQTTAsync_message_initializer</a></div><div class="ttdeci">#define MQTTAsync_message_initializer</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:339</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ad5562f9dc71fbd93d25ad20b328cb887"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync_destroy</a></div><div class="ttdeci">void MQTTAsync_destroy(MQTTAsync *handle)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_adc69afa4725f8321bdaa5a05aec5cfd5"><div class="ttname"><a href="_m_q_t_t_async_8h.html#adc69afa4725f8321bdaa5a05aec5cfd5">MQTTAsync_disconnect</a></div><div class="ttdeci">int MQTTAsync_disconnect(MQTTAsync handle, const MQTTAsync_disconnectOptions *options)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ae18b51f22784a43803eb809d6a0c2492"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ae18b51f22784a43803eb809d6a0c2492">MQTTAsync_connectOptions_initializer</a></div><div class="ttdeci">#define MQTTAsync_connectOptions_initializer</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1390</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ae9ae8d61023e7029ef5a19f5219c3599"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">MQTTAsync_setCallbacks</a></div><div class="ttdeci">int MQTTAsync_setCallbacks(MQTTAsync handle, void *context, MQTTAsync_connectionLost *cl, MQTTAsync_messageArrived *ma, MQTTAsync_deliveryComplete *dc)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_afe0cffcce8efe25186f79c51ac44e16f"><div class="ttname"><a href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a></div><div class="ttdeci">#define MQTTASYNC_SUCCESS</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:118</div></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html_ae01e089313a65ac4661ed216b6ac00fa"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html#ae01e089313a65ac4661ed216b6ac00fa">MQTTCLIENT_PERSISTENCE_NONE</a></div><div class="ttdeci">#define MQTTCLIENT_PERSISTENCE_NONE</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:74</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html">MQTTAsync_connectOptions</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1203</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a036c36a2a4d3a3ffae9ab4dd8b3e7f7b"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">MQTTAsync_connectOptions::cleansession</a></div><div class="ttdeci">int cleansession</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1249</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a09ce26d7cff24e14a6844eaae7b15290"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a09ce26d7cff24e14a6844eaae7b15290">MQTTAsync_connectOptions::onFailure</a></div><div class="ttdeci">MQTTAsync_onFailure * onFailure</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1300</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_ac13fb68f736854fcab131b34756bfceb"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#ac13fb68f736854fcab131b34756bfceb">MQTTAsync_connectOptions::onSuccess</a></div><div class="ttdeci">MQTTAsync_onSuccess * onSuccess</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1294</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_ac8dd0930672a9c7d71fc645aa1f0521d"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#ac8dd0930672a9c7d71fc645aa1f0521d">MQTTAsync_connectOptions::keepAliveInterval</a></div><div class="ttdeci">int keepAliveInterval</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1227</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_ae376f130b17d169ee51be68077a89ed0"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#ae376f130b17d169ee51be68077a89ed0">MQTTAsync_connectOptions::context</a></div><div class="ttdeci">void * context</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1306</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html">MQTTAsync_disconnectOptions</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1434</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_a09ce26d7cff24e14a6844eaae7b15290"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#a09ce26d7cff24e14a6844eaae7b15290">MQTTAsync_disconnectOptions::onFailure</a></div><div class="ttdeci">MQTTAsync_onFailure * onFailure</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1455</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_ac13fb68f736854fcab131b34756bfceb"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#ac13fb68f736854fcab131b34756bfceb">MQTTAsync_disconnectOptions::onSuccess</a></div><div class="ttdeci">MQTTAsync_onSuccess * onSuccess</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1449</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_ae376f130b17d169ee51be68077a89ed0"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#ae376f130b17d169ee51be68077a89ed0">MQTTAsync_disconnectOptions::context</a></div><div class="ttdeci">void * context</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:1461</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data_html"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data.html">MQTTAsync_failureData</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:541</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data_html_a45a5b7c00a796a23f01673cef1dbe0a9"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data.html#a45a5b7c00a796a23f01673cef1dbe0a9">MQTTAsync_failureData::code</a></div><div class="ttdeci">int code</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:545</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data_html_af8f771e67d284379111151b003c0d810"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data.html#af8f771e67d284379111151b003c0d810">MQTTAsync_failureData::token</a></div><div class="ttdeci">MQTTAsync_token token</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:543</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html"><div class="ttname"><a href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:277</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_a35738099155a0e4f54050da474bab2e7"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#a35738099155a0e4f54050da474bab2e7">MQTTAsync_message::qos</a></div><div class="ttdeci">int qos</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:300</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_a6a4904c112507a43e7dc8495b62cc0fc"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#a6a4904c112507a43e7dc8495b62cc0fc">MQTTAsync_message::retained</a></div><div class="ttdeci">int retained</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:319</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_a9eff55064941fb604452abb0050ea99d"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#a9eff55064941fb604452abb0050ea99d">MQTTAsync_message::payload</a></div><div class="ttdeci">void * payload</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:286</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_aa3cb44feb3ae6d11b3a4cad2d94cb33a"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#aa3cb44feb3ae6d11b3a4cad2d94cb33a">MQTTAsync_message::payloadlen</a></div><div class="ttdeci">int payloadlen</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:284</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:714</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_a09ce26d7cff24e14a6844eaae7b15290"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#a09ce26d7cff24e14a6844eaae7b15290">MQTTAsync_responseOptions::onFailure</a></div><div class="ttdeci">MQTTAsync_onFailure * onFailure</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:731</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_ac13fb68f736854fcab131b34756bfceb"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#ac13fb68f736854fcab131b34756bfceb">MQTTAsync_responseOptions::onSuccess</a></div><div class="ttdeci">MQTTAsync_onSuccess * onSuccess</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:725</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_ae376f130b17d169ee51be68077a89ed0"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#ae376f130b17d169ee51be68077a89ed0">MQTTAsync_responseOptions::context</a></div><div class="ttdeci">void * context</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:737</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html">MQTTAsync_successData</a></div><div class="ttdef"><b>Definition</b> MQTTAsync.h:576</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html_af8f771e67d284379111151b003c0d810"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html#af8f771e67d284379111151b003c0d810">MQTTAsync_successData::token</a></div><div class="ttdeci">MQTTAsync_token token</div><div class="ttdef"><b>Definition</b> MQTTAsync.h:578</div></div>
</div><!-- fragment --> </div></div><!-- contents -->
</div><!-- PageDoc -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:07 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
