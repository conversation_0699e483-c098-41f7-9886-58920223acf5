<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: MQTTAsync_responseOptions Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">MQTTAsync_responseOptions Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="_m_q_t_t_async_8h_source.html">MQTTAsync.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:aa5326df180cb23c59afbcab711a06479" id="r_aa5326df180cb23c59afbcab711a06479"><td class="memItemLeft" align="right" valign="top">char&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa5326df180cb23c59afbcab711a06479">struct_id</a> [4]</td></tr>
<tr class="separator:aa5326df180cb23c59afbcab711a06479"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0761a5e5be0383882e42924de8e51f82" id="r_a0761a5e5be0383882e42924de8e51f82"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0761a5e5be0383882e42924de8e51f82">struct_version</a></td></tr>
<tr class="separator:a0761a5e5be0383882e42924de8e51f82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac13fb68f736854fcab131b34756bfceb" id="r_ac13fb68f736854fcab131b34756bfceb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac13fb68f736854fcab131b34756bfceb">onSuccess</a></td></tr>
<tr class="separator:ac13fb68f736854fcab131b34756bfceb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09ce26d7cff24e14a6844eaae7b15290" id="r_a09ce26d7cff24e14a6844eaae7b15290"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync_onFailure</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a09ce26d7cff24e14a6844eaae7b15290">onFailure</a></td></tr>
<tr class="separator:a09ce26d7cff24e14a6844eaae7b15290"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae376f130b17d169ee51be68077a89ed0" id="r_ae376f130b17d169ee51be68077a89ed0"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae376f130b17d169ee51be68077a89ed0">context</a></td></tr>
<tr class="separator:ae376f130b17d169ee51be68077a89ed0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af8f771e67d284379111151b003c0d810" id="r_af8f771e67d284379111151b003c0d810"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af8f771e67d284379111151b003c0d810">token</a></td></tr>
<tr class="separator:af8f771e67d284379111151b003c0d810"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1c23c490f06428725345de68a4ff0a3e" id="r_a1c23c490f06428725345de68a4ff0a3e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync_onSuccess5</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1c23c490f06428725345de68a4ff0a3e">onSuccess5</a></td></tr>
<tr class="separator:a1c23c490f06428725345de68a4ff0a3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4dad726f2b6f79ca5847689c5f2f2ec2" id="r_a4dad726f2b6f79ca5847689c5f2f2ec2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync_onFailure5</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4dad726f2b6f79ca5847689c5f2f2ec2">onFailure5</a></td></tr>
<tr class="separator:a4dad726f2b6f79ca5847689c5f2f2ec2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1594008402f7307e4de8fa6131656dde" id="r_a1594008402f7307e4de8fa6131656dde"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1594008402f7307e4de8fa6131656dde">properties</a></td></tr>
<tr class="separator:a1594008402f7307e4de8fa6131656dde"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a16a3cd2a8c69669e9ed6e420ccd9c517" id="r_a16a3cd2a8c69669e9ed6e420ccd9c517"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a16a3cd2a8c69669e9ed6e420ccd9c517">subscribeOptions</a></td></tr>
<tr class="separator:a16a3cd2a8c69669e9ed6e420ccd9c517"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a4b9bb2780472ec7bb65d0df1bf5d26" id="r_a1a4b9bb2780472ec7bb65d0df1bf5d26"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1a4b9bb2780472ec7bb65d0df1bf5d26">subscribeOptionsCount</a></td></tr>
<tr class="separator:a1a4b9bb2780472ec7bb65d0df1bf5d26"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a98f71c5d03dc5ee86fd9dc0119ccb961" id="r_a98f71c5d03dc5ee86fd9dc0119ccb961"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a98f71c5d03dc5ee86fd9dc0119ccb961">subscribeOptionsList</a></td></tr>
<tr class="separator:a98f71c5d03dc5ee86fd9dc0119ccb961"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Structure to define call options. For MQTT 5.0 there is input data as well as that describing the response method. So there is now also a synonym <a class="el" href="_m_q_t_t_async_8h.html#ab6bfa6beae93c259220e1a131ba1cf9c">MQTTAsync_callOptions</a> to better reflect the use. This responseOptions name is kept for backward compatibility. </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="aa5326df180cb23c59afbcab711a06479" name="aa5326df180cb23c59afbcab711a06479"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa5326df180cb23c59afbcab711a06479">&#9670;&#160;</a></span>struct_id</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char struct_id[4]</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The eyecatcher for this structure. Must be MQTR </p>

</div>
</div>
<a id="a0761a5e5be0383882e42924de8e51f82" name="a0761a5e5be0383882e42924de8e51f82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0761a5e5be0383882e42924de8e51f82">&#9670;&#160;</a></span>struct_version</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int struct_version</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The version number of this structure. Must be 0 or 1 if 0, no MQTTV5 options </p>

</div>
</div>
<a id="ac13fb68f736854fcab131b34756bfceb" name="ac13fb68f736854fcab131b34756bfceb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac13fb68f736854fcab131b34756bfceb">&#9670;&#160;</a></span>onSuccess</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess</a>* onSuccess</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A pointer to a callback function to be called if the API call successfully completes. Can be set to NULL, in which case no indication of successful completion will be received. </p>

</div>
</div>
<a id="a09ce26d7cff24e14a6844eaae7b15290" name="a09ce26d7cff24e14a6844eaae7b15290"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a09ce26d7cff24e14a6844eaae7b15290">&#9670;&#160;</a></span>onFailure</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync_onFailure</a>* onFailure</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A pointer to a callback function to be called if the API call fails. Can be set to NULL, in which case no indication of unsuccessful completion will be received. </p>

</div>
</div>
<a id="ae376f130b17d169ee51be68077a89ed0" name="ae376f130b17d169ee51be68077a89ed0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae376f130b17d169ee51be68077a89ed0">&#9670;&#160;</a></span>context</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* context</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A pointer to any application-specific context. The the <em>context</em> pointer is passed to success or failure callback functions to provide access to the context information in the callback. </p>

</div>
</div>
<a id="af8f771e67d284379111151b003c0d810" name="af8f771e67d284379111151b003c0d810"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af8f771e67d284379111151b003c0d810">&#9670;&#160;</a></span>token</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> token</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A token is returned from the call. It can be used to track the state of this request, both in the callbacks and in future calls such as <a class="el" href="_m_q_t_t_async_8h.html#a4fe09cc9c976b1cf424e13765d6cd8c9">MQTTAsync_waitForCompletion</a>. This is output only - any change by the application will be ignored. </p>

</div>
</div>
<a id="a1c23c490f06428725345de68a4ff0a3e" name="a1c23c490f06428725345de68a4ff0a3e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1c23c490f06428725345de68a4ff0a3e">&#9670;&#160;</a></span>onSuccess5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync_onSuccess5</a>* onSuccess5</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A pointer to a callback function to be called if the API call successfully completes. Can be set to NULL, in which case no indication of successful completion will be received. </p>

</div>
</div>
<a id="a4dad726f2b6f79ca5847689c5f2f2ec2" name="a4dad726f2b6f79ca5847689c5f2f2ec2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4dad726f2b6f79ca5847689c5f2f2ec2">&#9670;&#160;</a></span>onFailure5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync_onFailure5</a>* onFailure5</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A pointer to a callback function to be called if the API call successfully completes. Can be set to NULL, in which case no indication of successful completion will be received. </p>

</div>
</div>
<a id="a1594008402f7307e4de8fa6131656dde" name="a1594008402f7307e4de8fa6131656dde"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1594008402f7307e4de8fa6131656dde">&#9670;&#160;</a></span>properties</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> properties</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>MQTT V5 input properties </p>

</div>
</div>
<a id="a16a3cd2a8c69669e9ed6e420ccd9c517" name="a16a3cd2a8c69669e9ed6e420ccd9c517"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a16a3cd2a8c69669e9ed6e420ccd9c517">&#9670;&#160;</a></span>subscribeOptions</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a> subscribeOptions</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a1a4b9bb2780472ec7bb65d0df1bf5d26" name="a1a4b9bb2780472ec7bb65d0df1bf5d26"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1a4b9bb2780472ec7bb65d0df1bf5d26">&#9670;&#160;</a></span>subscribeOptionsCount</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int subscribeOptionsCount</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a98f71c5d03dc5ee86fd9dc0119ccb961" name="a98f71c5d03dc5ee86fd9dc0119ccb961"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a98f71c5d03dc5ee86fd9dc0119ccb961">&#9670;&#160;</a></span>subscribeOptionsList</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a>* subscribeOptionsList</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_m_q_t_t_async_8h_source.html">MQTTAsync.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:08 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
