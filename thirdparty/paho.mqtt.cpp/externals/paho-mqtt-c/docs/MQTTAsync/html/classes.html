<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: Data Structure Index</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">Data Structure Index</div></div>
</div><!--header-->
<div class="contents">
<div class="qindex"><a class="qindex" href="#letter_M">M</a></div>
<div class="classindex">
<dl class="classindex even">
<dt class="alphachar"><a id="letter_M" name="letter_M">M</a></dt>
<dd><a class="el" href="struct_m_q_t_t_async__connect_data.html">MQTTAsync_connectData</a></dd><dd><a class="el" href="struct_m_q_t_t_async__connect_options.html">MQTTAsync_connectOptions</a></dd><dd><a class="el" href="struct_m_q_t_t_async__create_options.html">MQTTAsync_createOptions</a></dd><dd><a class="el" href="struct_m_q_t_t_async__disconnect_options.html">MQTTAsync_disconnectOptions</a></dd><dd><a class="el" href="struct_m_q_t_t_async__failure_data.html">MQTTAsync_failureData</a></dd><dd><a class="el" href="struct_m_q_t_t_async__failure_data5.html">MQTTAsync_failureData5</a></dd><dd><a class="el" href="struct_m_q_t_t_async__init__options.html">MQTTAsync_init_options</a></dd><dd><a class="el" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a></dd><dd><a class="el" href="struct_m_q_t_t_async__name_value.html">MQTTAsync_nameValue</a></dd><dd><a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a></dd><dd><a class="el" href="struct_m_q_t_t_async___s_s_l_options.html">MQTTAsync_SSLOptions</a></dd><dd><a class="el" href="struct_m_q_t_t_async__success_data.html">MQTTAsync_successData</a></dd><dd><a class="el" href="struct_m_q_t_t_async__success_data5.html">MQTTAsync_successData5</a></dd><dd><a class="el" href="struct_m_q_t_t_async__will_options.html">MQTTAsync_willOptions</a></dd><dd><a class="el" href="struct_m_q_t_t_client__persistence.html">MQTTClient_persistence</a></dd><dd><a class="el" href="struct_m_q_t_t_len_string.html">MQTTLenString</a></dd><dd><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a></dd><dd><a class="el" href="struct_m_q_t_t_property.html">MQTTProperty</a></dd><dd><a class="el" href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a></dd></dl>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:08 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
