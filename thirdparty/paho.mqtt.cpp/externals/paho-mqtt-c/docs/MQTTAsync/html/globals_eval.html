<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all enum values with links to the files they belong to:</div>

<h3><a id="index_m" name="index_m"></a>- m -</h3><ul>
<li>MQTTASYNC_TRACE_ERROR&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5ac428f74ca453dacb7b8271ca741266e8">MQTTAsync.h</a></li>
<li>MQTTASYNC_TRACE_FATAL&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a0b91d2213ebb6655e41a7f6ce1a42295">MQTTAsync.h</a></li>
<li>MQTTASYNC_TRACE_MAXIMUM&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5af684f42971cced68693ce993703548c1">MQTTAsync.h</a></li>
<li>MQTTASYNC_TRACE_MEDIUM&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a133c380b84d75477ff31a2ad732133ce">MQTTAsync.h</a></li>
<li>MQTTASYNC_TRACE_MINIMUM&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a7a45c26816b1cac1fde02d79a9f4337b">MQTTAsync.h</a></li>
<li>MQTTASYNC_TRACE_PROTOCOL&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a6a719b2b7fc4dfc41494370ff96fec3e">MQTTAsync.h</a></li>
<li>MQTTASYNC_TRACE_SEVERE&#160;:&#160;<a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a3084770185f384398cefe4aaba533d40">MQTTAsync.h</a></li>
<li>MQTTPROPERTY_CODE_ASSIGNED_CLIENT_IDENTIFER&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a768d84858fd18d5d5a7dee394929c672">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_ASSIGNED_CLIENT_IDENTIFIER&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab5153f683f5a25f6e3a9e3aeb37f1fd6">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_AUTHENTICATION_DATA&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4abdf9feec165aceefbe7aa46764f6ab6e">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_AUTHENTICATION_METHOD&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a7c53f1e414b577d787b5d51af3204100">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_CONTENT_TYPE&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a4027d9e0fb53a62ae35963e700b56198">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_CORRELATION_DATA&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a887d3dd3f0ce31255324f5a1ba8b72c5">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_MAXIMUM_PACKET_SIZE&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a6834ea9878f028d5fbdeccaaeae492e5">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_MAXIMUM_QOS&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a506faeb89c407cf78853c777d750fa59">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_MESSAGE_EXPIRY_INTERVAL&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a284c0e62d47ee8d358b16a8075632b4a">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_PAYLOAD_FORMAT_INDICATOR&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ae5d077520427d03b44096f631411575d">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_REASON_STRING&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a3dce8f679474e901ce4aec076e9e59e1">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_RECEIVE_MAXIMUM&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab2688fe8d7d263c27c00d41776cb8f9f">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_REQUEST_PROBLEM_INFORMATION&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a3954daf1d5772b5d56eefa1ab6a28aa1">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_REQUEST_RESPONSE_INFORMATION&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a420b882a337dc1fd5f336ac6cd0529bf">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_RESPONSE_INFORMATION&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a2584b050f016af496c7f0b46692dbc00">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_RESPONSE_TOPIC&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a7fa9996eef721d318504fbb0a8d4bac5">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_RETAIN_AVAILABLE&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a448b3a40afaa5f7195701e7dc8bed30c">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_SERVER_KEEP_ALIVE&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab106f320e7537b79644f25d3efcd68c7">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_SERVER_REFERENCE&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a0168e8a59f7994c02b7a7fd2fc3735c4">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_SESSION_EXPIRY_INTERVAL&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a22e4caa63f63ca3f9b1c1330711ee766">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_SHARED_SUBSCRIPTION_AVAILABLE&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ae04a7356f9e11654f15a3b21f2aae636">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIER&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a70ead9c93f06396a4d9469b65bff0c96">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIERS_AVAILABLE&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a8b366cfd8bd3f388bafb67f3ebf83505">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_TOPIC_ALIAS&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ad4dfb37d341ea190afc144668e5e3bee">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_TOPIC_ALIAS_MAXIMUM&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a0a0b0b0715ecc9ccf471c75aa4c21c23">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_USER_PROPERTY&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a596ff540370235d3eca693ce30dd4af8">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_WILDCARD_SUBSCRIPTION_AVAILABLE&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ad05993f90baaee0ba7094ccef4d378b9">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_WILL_DELAY_INTERVAL&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a53fd81bc554f152a2772d282be7ce5ef">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_TYPE_BINARY_DATA&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a6643aed682b9b07f98159856776fe7b4">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_TYPE_BYTE&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ac36f96ce58c98a8ebbe0783df030726a">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_TYPE_FOUR_BYTE_INTEGER&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958aa49c558733bd735ae872fd87ad0d7e15">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_TYPE_TWO_BYTE_INTEGER&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ae301a9e68326cc2d8bfefeca401e78e6">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_TYPE_UTF_8_ENCODED_STRING&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ad45c866a5bef6c5048a7af21405734d1">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_TYPE_UTF_8_STRING_PAIR&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a28ab5fe5b159f3b3a8884b0f61527214">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_TYPE_VARIABLE_BYTE_INTEGER&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a27bbcb5bc4f584f96612c0cec329c6a7">MQTTProperties.h</a></li>
<li>MQTTREASONCODE_ADMINISTRATIVE_ACTION&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ae1e3b428072be26d2cbf6f88361f76cc">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_BAD_AUTHENTICATION_METHOD&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af62e569703d7a7f0acffaa59522b9dc3">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_BAD_USER_NAME_OR_PASSWORD&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279abfc617112d5856722108912c5c6633ff">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_BANNED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ab4cf7578f0078293fa66a4cd5e5d4aa4">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_CLIENT_IDENTIFIER_NOT_VALID&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ab58bb236e7dbd000a56c590c01bc73fd">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_CONNECTION_RATE_EXCEEDED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a879c56ed34fa2dd6492e7a34a9747bc1">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_CONTINUE_AUTHENTICATION&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a0c0726c0e87eaddd636708497c69d055">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_DISCONNECT_WITH_WILL_MESSAGE&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a55f533a6cc98417d08dac8cc69da0ed3">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_GRANTED_QOS_0&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a3fd0d12c0e44b4df9f716aef89b61aff">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_GRANTED_QOS_1&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a07578b30b2d72af2eeea6be268475876">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_GRANTED_QOS_2&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a74ac34a39a849c9c369b18545a4b1f93">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_IMPLEMENTATION_SPECIFIC_ERROR&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a41629fa453cdf14ef6a5370a16d5a19c">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_KEEP_ALIVE_TIMEOUT&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af21a6c320e34993d7aa169330ab23409">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_MALFORMED_PACKET&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a2cbee3502c00d304bf1091195457fcf5">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_MAXIMUM_CONNECT_TIME&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6f07c3b42690afc7b117321dc4e2657f">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_MESSAGE_RATE_TOO_HIGH&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af76d0e32fb44fa94e407b1af5dc7aa4e">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_NO_MATCHING_SUBSCRIBERS&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1720d8b04af4c0d92e27b378d735e899">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_NO_SUBSCRIPTION_FOUND&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a55208c34a26f67e112d53c54be37acb9">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_NORMAL_DISCONNECTION&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a3590f41d984646bc58c82734c1516c92">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_NOT_AUTHORIZED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a91a14fc763349cf4a7047d24f13d0803">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_PACKET_IDENTIFIER_IN_USE&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279adaee01dbc97a0773b5032a29c797613a">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_PACKET_IDENTIFIER_NOT_FOUND&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a4908a8293054f8ff8d6c47fe0cf31932">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_PACKET_TOO_LARGE&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a11a587e15c468bf1c6ba9df7e8fd78aa">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_PAYLOAD_FORMAT_INVALID&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a2d629400116e1723c5e2e597bbfe29ca">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_PROTOCOL_ERROR&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ae0dad403f352e31449764e2ac94c7756">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_QOS_NOT_SUPPORTED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a83865a2440b512e5602152521e3810bb">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_QUOTA_EXCEEDED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a954fcabf6e88925b2a57bcd84032d9f9">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_RE_AUTHENTICATE&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6cc1b342856c1d96d54c368148b536f7">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_RECEIVE_MAXIMUM_EXCEEDED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a45afaacbefd2d816fddf9fe9804b61d1">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_RETAIN_NOT_SUPPORTED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279aa4378012148d98599398bc4a3480c38f">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_SERVER_BUSY&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af507e75147b0b34f36955c9f62389a74">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_SERVER_MOVED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a783254c7acf8de52ee345bc176f9d6c0">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_SERVER_SHUTTING_DOWN&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a085e1572ffce61838807b7429b691113">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_SERVER_UNAVAILABLE&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a0cfd4de78870b3fb0499b916d06d40bb">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_SESSION_TAKEN_OVER&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ad15ffa6884f97976e237afafcbccea21">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_SHARED_SUBSCRIPTIONS_NOT_SUPPORTED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1c694648e36a40162939a2785450b6bd">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_SUBSCRIPTION_IDENTIFIERS_NOT_SUPPORTED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a7bcd0f9b21c398a217667aebb4107842">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_SUCCESS&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_TOPIC_ALIAS_INVALID&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a8e0fcdd051e154e319058600b58652ec">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_TOPIC_FILTER_INVALID&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a00319b171f469824dd6938cbd0212b5b">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_TOPIC_NAME_INVALID&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6268968177868576f6b9239aa9afd8ac">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_UNSPECIFIED_ERROR&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1881ee597bfef9157f0034a1377328e3">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_UNSUPPORTED_PROTOCOL_VERSION&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a021ceca20e6d35279075a2b93ece973d">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_USE_ANOTHER_SERVER&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279aabaee4062c4e4941b9eed59f09e9440c">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_WILDCARD_SUBSCRIPTIONS_NOT_SUPPORTED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a81b5708f676f52594b680f085e444e1f">MQTTReasonCodes.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:08 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
