<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: MQTTAsync_message Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">MQTTAsync_message Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="_m_q_t_t_async_8h_source.html">MQTTAsync.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:aa5326df180cb23c59afbcab711a06479" id="r_aa5326df180cb23c59afbcab711a06479"><td class="memItemLeft" align="right" valign="top">char&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa5326df180cb23c59afbcab711a06479">struct_id</a> [4]</td></tr>
<tr class="separator:aa5326df180cb23c59afbcab711a06479"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0761a5e5be0383882e42924de8e51f82" id="r_a0761a5e5be0383882e42924de8e51f82"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0761a5e5be0383882e42924de8e51f82">struct_version</a></td></tr>
<tr class="separator:a0761a5e5be0383882e42924de8e51f82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa3cb44feb3ae6d11b3a4cad2d94cb33a" id="r_aa3cb44feb3ae6d11b3a4cad2d94cb33a"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa3cb44feb3ae6d11b3a4cad2d94cb33a">payloadlen</a></td></tr>
<tr class="separator:aa3cb44feb3ae6d11b3a4cad2d94cb33a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9eff55064941fb604452abb0050ea99d" id="r_a9eff55064941fb604452abb0050ea99d"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9eff55064941fb604452abb0050ea99d">payload</a></td></tr>
<tr class="separator:a9eff55064941fb604452abb0050ea99d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a35738099155a0e4f54050da474bab2e7" id="r_a35738099155a0e4f54050da474bab2e7"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a35738099155a0e4f54050da474bab2e7">qos</a></td></tr>
<tr class="separator:a35738099155a0e4f54050da474bab2e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6a4904c112507a43e7dc8495b62cc0fc" id="r_a6a4904c112507a43e7dc8495b62cc0fc"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6a4904c112507a43e7dc8495b62cc0fc">retained</a></td></tr>
<tr class="separator:a6a4904c112507a43e7dc8495b62cc0fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc4cf3f551bb367858644559d69cfdf5" id="r_adc4cf3f551bb367858644559d69cfdf5"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#adc4cf3f551bb367858644559d69cfdf5">dup</a></td></tr>
<tr class="separator:adc4cf3f551bb367858644559d69cfdf5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6174c42da8c55c86e7255be2848dc4ac" id="r_a6174c42da8c55c86e7255be2848dc4ac"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6174c42da8c55c86e7255be2848dc4ac">msgid</a></td></tr>
<tr class="separator:a6174c42da8c55c86e7255be2848dc4ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1594008402f7307e4de8fa6131656dde" id="r_a1594008402f7307e4de8fa6131656dde"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1594008402f7307e4de8fa6131656dde">properties</a></td></tr>
<tr class="separator:a1594008402f7307e4de8fa6131656dde"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>A structure representing the payload and attributes of an MQTT message. The message topic is not part of this structure (see MQTTAsync_publishMessage(), MQTTAsync_publish(), MQTTAsync_receive(), <a class="el" href="_m_q_t_t_async_8h.html#a9b45db63052fe29ab1fad22d2a00c91c">MQTTAsync_freeMessage()</a> and <a class="el" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync_messageArrived()</a>). </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="aa5326df180cb23c59afbcab711a06479" name="aa5326df180cb23c59afbcab711a06479"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa5326df180cb23c59afbcab711a06479">&#9670;&#160;</a></span>struct_id</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char struct_id[4]</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The eyecatcher for this structure. must be MQTM. </p>

</div>
</div>
<a id="a0761a5e5be0383882e42924de8e51f82" name="a0761a5e5be0383882e42924de8e51f82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0761a5e5be0383882e42924de8e51f82">&#9670;&#160;</a></span>struct_version</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int struct_version</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The version number of this structure. Must be 0 or 1. 0 indicates no message properties </p>

</div>
</div>
<a id="aa3cb44feb3ae6d11b3a4cad2d94cb33a" name="aa3cb44feb3ae6d11b3a4cad2d94cb33a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa3cb44feb3ae6d11b3a4cad2d94cb33a">&#9670;&#160;</a></span>payloadlen</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int payloadlen</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The length of the MQTT message payload in bytes. </p>

</div>
</div>
<a id="a9eff55064941fb604452abb0050ea99d" name="a9eff55064941fb604452abb0050ea99d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9eff55064941fb604452abb0050ea99d">&#9670;&#160;</a></span>payload</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* payload</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A pointer to the payload of the MQTT message. </p>

</div>
</div>
<a id="a35738099155a0e4f54050da474bab2e7" name="a35738099155a0e4f54050da474bab2e7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a35738099155a0e4f54050da474bab2e7">&#9670;&#160;</a></span>qos</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int qos</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The quality of service (QoS) assigned to the message. There are three levels of QoS: </p><dl>
<dt><b>QoS0</b> </dt>
<dd>Fire and forget - the message may not be delivered </dd>
<dt><b>QoS1</b> </dt>
<dd>At least once - the message will be delivered, but may be delivered more than once in some circumstances. </dd>
<dt><b>QoS2</b> </dt>
<dd>Once and one only - the message will be delivered exactly once. </dd>
</dl>

</div>
</div>
<a id="a6a4904c112507a43e7dc8495b62cc0fc" name="a6a4904c112507a43e7dc8495b62cc0fc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6a4904c112507a43e7dc8495b62cc0fc">&#9670;&#160;</a></span>retained</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int retained</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The retained flag serves two purposes depending on whether the message it is associated with is being published or received.</p>
<p><b>retained = true</b><br  />
 For messages being published, a true setting indicates that the MQTT server should retain a copy of the message. The message will then be transmitted to new subscribers to a topic that matches the message topic. For subscribers registering a new subscription, the flag being true indicates that the received message is not a new one, but one that has been retained by the MQTT server.</p>
<p><b>retained = false</b> <br  />
 For publishers, this indicates that this message should not be retained by the MQTT server. For subscribers, a false setting indicates this is a normal message, received as a result of it being published to the server. </p>

</div>
</div>
<a id="adc4cf3f551bb367858644559d69cfdf5" name="adc4cf3f551bb367858644559d69cfdf5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adc4cf3f551bb367858644559d69cfdf5">&#9670;&#160;</a></span>dup</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int dup</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The dup flag indicates whether or not this message is a duplicate. It is only meaningful when receiving QoS1 messages. When true, the client application should take appropriate action to deal with the duplicate message. This is an output parameter only. </p>

</div>
</div>
<a id="a6174c42da8c55c86e7255be2848dc4ac" name="a6174c42da8c55c86e7255be2848dc4ac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6174c42da8c55c86e7255be2848dc4ac">&#9670;&#160;</a></span>msgid</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int msgid</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The message identifier is reserved for internal use by the MQTT client and server. It is an output parameter only - writing to it will serve no purpose. It contains the MQTT message id of an incoming publish message. </p>

</div>
</div>
<a id="a1594008402f7307e4de8fa6131656dde" name="a1594008402f7307e4de8fa6131656dde"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1594008402f7307e4de8fa6131656dde">&#9670;&#160;</a></span>properties</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> properties</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The MQTT V5 properties associated with the message. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_m_q_t_t_async_8h_source.html">MQTTAsync.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:08 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
