<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: MQTTClient_persistence Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">MQTTClient_persistence Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p>A structure containing the function pointers to a persistence implementation and the context or state that will be shared across all the persistence functions.  
 <a href="#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_m_q_t_t_client_persistence_8h_source.html">MQTTClientPersistence.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:ae376f130b17d169ee51be68077a89ed0" id="r_ae376f130b17d169ee51be68077a89ed0"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae376f130b17d169ee51be68077a89ed0">context</a></td></tr>
<tr class="separator:ae376f130b17d169ee51be68077a89ed0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1bae211b32415e6b349d5ae71599f9f4" id="r_a1bae211b32415e6b349d5ae71599f9f4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_client_persistence_8h.html#a4c7d332bb16907058ae3b375488b6008">Persistence_open</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1bae211b32415e6b349d5ae71599f9f4">popen</a></td></tr>
<tr class="separator:a1bae211b32415e6b349d5ae71599f9f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7e50506912d2ec0e014cc25ec28fb402" id="r_a7e50506912d2ec0e014cc25ec28fb402"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_client_persistence_8h.html#a3582de2c87e89f617e8e553b2a0e279a">Persistence_close</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7e50506912d2ec0e014cc25ec28fb402">pclose</a></td></tr>
<tr class="separator:a7e50506912d2ec0e014cc25ec28fb402"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4114d9b9971cee18d7e4b9dd5736a608" id="r_a4114d9b9971cee18d7e4b9dd5736a608"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_client_persistence_8h.html#a44679cab77cfbd6e2a4639cdd27ac80c">Persistence_put</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4114d9b9971cee18d7e4b9dd5736a608">pput</a></td></tr>
<tr class="separator:a4114d9b9971cee18d7e4b9dd5736a608"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a49155000b82a28ac3b3cb878f3a092d4" id="r_a49155000b82a28ac3b3cb878f3a092d4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_client_persistence_8h.html#adc3aff3c570fa5509e9d6814a85ab867">Persistence_get</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a49155000b82a28ac3b3cb878f3a092d4">pget</a></td></tr>
<tr class="separator:a49155000b82a28ac3b3cb878f3a092d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a53150e443ca721b8623689371c2fbdb9" id="r_a53150e443ca721b8623689371c2fbdb9"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_client_persistence_8h.html#a73350bf7208658bf5434a59f7bdbae90">Persistence_remove</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a53150e443ca721b8623689371c2fbdb9">premove</a></td></tr>
<tr class="separator:a53150e443ca721b8623689371c2fbdb9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a407e86a809e4b0b098a8c158f53b9606" id="r_a407e86a809e4b0b098a8c158f53b9606"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_client_persistence_8h.html#a2601cc91eeabdbf9578f8dd45e4997a8">Persistence_keys</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a407e86a809e4b0b098a8c158f53b9606">pkeys</a></td></tr>
<tr class="separator:a407e86a809e4b0b098a8c158f53b9606"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc192dc88113c7d933b29d3561badbf5" id="r_abc192dc88113c7d933b29d3561badbf5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_client_persistence_8h.html#acee7097c1a0ab44b98c870f533687887">Persistence_clear</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abc192dc88113c7d933b29d3561badbf5">pclear</a></td></tr>
<tr class="separator:abc192dc88113c7d933b29d3561badbf5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac103711576267f791325f2b70b6dc49d" id="r_ac103711576267f791325f2b70b6dc49d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_client_persistence_8h.html#a753a0f9a9c51284d63a907af19c7bbba">Persistence_containskey</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac103711576267f791325f2b70b6dc49d">pcontainskey</a></td></tr>
<tr class="separator:ac103711576267f791325f2b70b6dc49d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>A structure containing the function pointers to a persistence implementation and the context or state that will be shared across all the persistence functions. </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="ae376f130b17d169ee51be68077a89ed0" name="ae376f130b17d169ee51be68077a89ed0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae376f130b17d169ee51be68077a89ed0">&#9670;&#160;</a></span>context</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* context</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A pointer to any data required to initialize the persistent store. </p>

</div>
</div>
<a id="a1bae211b32415e6b349d5ae71599f9f4" name="a1bae211b32415e6b349d5ae71599f9f4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1bae211b32415e6b349d5ae71599f9f4">&#9670;&#160;</a></span>popen</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_client_persistence_8h.html#a4c7d332bb16907058ae3b375488b6008">Persistence_open</a> popen</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A function pointer to an implementation of <a class="el" href="_m_q_t_t_client_persistence_8h.html#a4c7d332bb16907058ae3b375488b6008" title="Initialize the persistent store.">Persistence_open()</a>. </p>

</div>
</div>
<a id="a7e50506912d2ec0e014cc25ec28fb402" name="a7e50506912d2ec0e014cc25ec28fb402"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7e50506912d2ec0e014cc25ec28fb402">&#9670;&#160;</a></span>pclose</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_client_persistence_8h.html#a3582de2c87e89f617e8e553b2a0e279a">Persistence_close</a> pclose</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A function pointer to an implementation of <a class="el" href="_m_q_t_t_client_persistence_8h.html#a3582de2c87e89f617e8e553b2a0e279a" title="Close the persistent store referred to by the handle.">Persistence_close()</a>. </p>

</div>
</div>
<a id="a4114d9b9971cee18d7e4b9dd5736a608" name="a4114d9b9971cee18d7e4b9dd5736a608"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4114d9b9971cee18d7e4b9dd5736a608">&#9670;&#160;</a></span>pput</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_client_persistence_8h.html#a44679cab77cfbd6e2a4639cdd27ac80c">Persistence_put</a> pput</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A function pointer to an implementation of <a class="el" href="_m_q_t_t_client_persistence_8h.html#a44679cab77cfbd6e2a4639cdd27ac80c" title="Put the specified data into the persistent store.">Persistence_put()</a>. </p>

</div>
</div>
<a id="a49155000b82a28ac3b3cb878f3a092d4" name="a49155000b82a28ac3b3cb878f3a092d4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a49155000b82a28ac3b3cb878f3a092d4">&#9670;&#160;</a></span>pget</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_client_persistence_8h.html#adc3aff3c570fa5509e9d6814a85ab867">Persistence_get</a> pget</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A function pointer to an implementation of <a class="el" href="_m_q_t_t_client_persistence_8h.html#adc3aff3c570fa5509e9d6814a85ab867" title="Retrieve the specified data from the persistent store.">Persistence_get()</a>. </p>

</div>
</div>
<a id="a53150e443ca721b8623689371c2fbdb9" name="a53150e443ca721b8623689371c2fbdb9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a53150e443ca721b8623689371c2fbdb9">&#9670;&#160;</a></span>premove</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_client_persistence_8h.html#a73350bf7208658bf5434a59f7bdbae90">Persistence_remove</a> premove</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A function pointer to an implementation of <a class="el" href="_m_q_t_t_client_persistence_8h.html#a73350bf7208658bf5434a59f7bdbae90" title="Remove the data for the specified key from the store.">Persistence_remove()</a>. </p>

</div>
</div>
<a id="a407e86a809e4b0b098a8c158f53b9606" name="a407e86a809e4b0b098a8c158f53b9606"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a407e86a809e4b0b098a8c158f53b9606">&#9670;&#160;</a></span>pkeys</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_client_persistence_8h.html#a2601cc91eeabdbf9578f8dd45e4997a8">Persistence_keys</a> pkeys</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A function pointer to an implementation of <a class="el" href="_m_q_t_t_client_persistence_8h.html#a2601cc91eeabdbf9578f8dd45e4997a8" title="Returns the keys in this persistent data store.">Persistence_keys()</a>. </p>

</div>
</div>
<a id="abc192dc88113c7d933b29d3561badbf5" name="abc192dc88113c7d933b29d3561badbf5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abc192dc88113c7d933b29d3561badbf5">&#9670;&#160;</a></span>pclear</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_client_persistence_8h.html#acee7097c1a0ab44b98c870f533687887">Persistence_clear</a> pclear</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A function pointer to an implementation of <a class="el" href="_m_q_t_t_client_persistence_8h.html#acee7097c1a0ab44b98c870f533687887" title="Clears the persistence store, so that it no longer contains any persisted data.">Persistence_clear()</a>. </p>

</div>
</div>
<a id="ac103711576267f791325f2b70b6dc49d" name="ac103711576267f791325f2b70b6dc49d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac103711576267f791325f2b70b6dc49d">&#9670;&#160;</a></span>pcontainskey</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_client_persistence_8h.html#a753a0f9a9c51284d63a907af19c7bbba">Persistence_containskey</a> pcontainskey</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A function pointer to an implementation of <a class="el" href="_m_q_t_t_client_persistence_8h.html#a753a0f9a9c51284d63a907af19c7bbba" title="Returns whether any data has been persisted using the specified key.">Persistence_containskey()</a>. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_m_q_t_t_client_persistence_8h_source.html">MQTTClientPersistence.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:08 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
