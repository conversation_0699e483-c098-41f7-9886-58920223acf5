<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: MQTTAsync_disconnectOptions Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">MQTTAsync_disconnectOptions Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="_m_q_t_t_async_8h_source.html">MQTTAsync.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:aa5326df180cb23c59afbcab711a06479" id="r_aa5326df180cb23c59afbcab711a06479"><td class="memItemLeft" align="right" valign="top">char&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa5326df180cb23c59afbcab711a06479">struct_id</a> [4]</td></tr>
<tr class="separator:aa5326df180cb23c59afbcab711a06479"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0761a5e5be0383882e42924de8e51f82" id="r_a0761a5e5be0383882e42924de8e51f82"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0761a5e5be0383882e42924de8e51f82">struct_version</a></td></tr>
<tr class="separator:a0761a5e5be0383882e42924de8e51f82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a493b57f443cc38b3d3df9c1e584d9d82" id="r_a493b57f443cc38b3d3df9c1e584d9d82"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a493b57f443cc38b3d3df9c1e584d9d82">timeout</a></td></tr>
<tr class="separator:a493b57f443cc38b3d3df9c1e584d9d82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac13fb68f736854fcab131b34756bfceb" id="r_ac13fb68f736854fcab131b34756bfceb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac13fb68f736854fcab131b34756bfceb">onSuccess</a></td></tr>
<tr class="separator:ac13fb68f736854fcab131b34756bfceb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09ce26d7cff24e14a6844eaae7b15290" id="r_a09ce26d7cff24e14a6844eaae7b15290"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync_onFailure</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a09ce26d7cff24e14a6844eaae7b15290">onFailure</a></td></tr>
<tr class="separator:a09ce26d7cff24e14a6844eaae7b15290"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae376f130b17d169ee51be68077a89ed0" id="r_ae376f130b17d169ee51be68077a89ed0"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae376f130b17d169ee51be68077a89ed0">context</a></td></tr>
<tr class="separator:ae376f130b17d169ee51be68077a89ed0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1594008402f7307e4de8fa6131656dde" id="r_a1594008402f7307e4de8fa6131656dde"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1594008402f7307e4de8fa6131656dde">properties</a></td></tr>
<tr class="separator:a1594008402f7307e4de8fa6131656dde"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a580d8a8ecb285f5a86c2a3865438f8ee" id="r_a580d8a8ecb285f5a86c2a3865438f8ee"><td class="memItemLeft" align="right" valign="top">enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a580d8a8ecb285f5a86c2a3865438f8ee">reasonCode</a></td></tr>
<tr class="separator:a580d8a8ecb285f5a86c2a3865438f8ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1c23c490f06428725345de68a4ff0a3e" id="r_a1c23c490f06428725345de68a4ff0a3e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync_onSuccess5</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1c23c490f06428725345de68a4ff0a3e">onSuccess5</a></td></tr>
<tr class="separator:a1c23c490f06428725345de68a4ff0a3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4dad726f2b6f79ca5847689c5f2f2ec2" id="r_a4dad726f2b6f79ca5847689c5f2f2ec2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync_onFailure5</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4dad726f2b6f79ca5847689c5f2f2ec2">onFailure5</a></td></tr>
<tr class="separator:a4dad726f2b6f79ca5847689c5f2f2ec2"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Options for the <a class="el" href="_m_q_t_t_async_8h.html#adc69afa4725f8321bdaa5a05aec5cfd5">MQTTAsync_disconnect</a> call </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="aa5326df180cb23c59afbcab711a06479" name="aa5326df180cb23c59afbcab711a06479"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa5326df180cb23c59afbcab711a06479">&#9670;&#160;</a></span>struct_id</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char struct_id[4]</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The eyecatcher for this structure. Must be MQTD. </p>

</div>
</div>
<a id="a0761a5e5be0383882e42924de8e51f82" name="a0761a5e5be0383882e42924de8e51f82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0761a5e5be0383882e42924de8e51f82">&#9670;&#160;</a></span>struct_version</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int struct_version</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The version number of this structure. Must be 0 or 1. 0 signifies no V5 properties </p>

</div>
</div>
<a id="a493b57f443cc38b3d3df9c1e584d9d82" name="a493b57f443cc38b3d3df9c1e584d9d82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a493b57f443cc38b3d3df9c1e584d9d82">&#9670;&#160;</a></span>timeout</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int timeout</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The client delays disconnection for up to this time (in milliseconds) in order to allow in-flight message transfers to complete. </p>

</div>
</div>
<a id="ac13fb68f736854fcab131b34756bfceb" name="ac13fb68f736854fcab131b34756bfceb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac13fb68f736854fcab131b34756bfceb">&#9670;&#160;</a></span>onSuccess</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess</a>* onSuccess</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A pointer to a callback function to be called if the disconnect successfully completes. Can be set to NULL, in which case no indication of successful completion will be received. </p>

</div>
</div>
<a id="a09ce26d7cff24e14a6844eaae7b15290" name="a09ce26d7cff24e14a6844eaae7b15290"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a09ce26d7cff24e14a6844eaae7b15290">&#9670;&#160;</a></span>onFailure</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync_onFailure</a>* onFailure</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A pointer to a callback function to be called if the disconnect fails. Can be set to NULL, in which case no indication of unsuccessful completion will be received. </p>

</div>
</div>
<a id="ae376f130b17d169ee51be68077a89ed0" name="ae376f130b17d169ee51be68077a89ed0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae376f130b17d169ee51be68077a89ed0">&#9670;&#160;</a></span>context</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* context</td>
        </tr>
      </table>
</div><div class="memdoc">
<pre class="fragment">A pointer to any application-specific context. The
</pre><p> the <em>context</em> pointer is passed to success or failure callback functions to provide access to the context information in the callback. </p>

</div>
</div>
<a id="a1594008402f7307e4de8fa6131656dde" name="a1594008402f7307e4de8fa6131656dde"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1594008402f7307e4de8fa6131656dde">&#9670;&#160;</a></span>properties</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> properties</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>MQTT V5 input properties </p>

</div>
</div>
<a id="a580d8a8ecb285f5a86c2a3865438f8ee" name="a580d8a8ecb285f5a86c2a3865438f8ee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a580d8a8ecb285f5a86c2a3865438f8ee">&#9670;&#160;</a></span>reasonCode</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reasonCode</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Reason code for MQTTV5 disconnect </p>

</div>
</div>
<a id="a1c23c490f06428725345de68a4ff0a3e" name="a1c23c490f06428725345de68a4ff0a3e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1c23c490f06428725345de68a4ff0a3e">&#9670;&#160;</a></span>onSuccess5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync_onSuccess5</a>* onSuccess5</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A pointer to a callback function to be called if the disconnect successfully completes. Can be set to NULL, in which case no indication of successful completion will be received. </p>

</div>
</div>
<a id="a4dad726f2b6f79ca5847689c5f2f2ec2" name="a4dad726f2b6f79ca5847689c5f2f2ec2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4dad726f2b6f79ca5847689c5f2f2ec2">&#9670;&#160;</a></span>onFailure5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync_onFailure5</a>* onFailure5</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A pointer to a callback function to be called if the disconnect fails. Can be set to NULL, in which case no indication of unsuccessful completion will be received. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_m_q_t_t_async_8h_source.html">MQTTAsync.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:07 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
