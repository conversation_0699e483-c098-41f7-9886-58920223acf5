<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: Asynchronous MQTT client library for C (MQTTAsync)</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div><div class="header">
  <div class="headertitle"><div class="title">Asynchronous MQTT client library for C (MQTTAsync) </div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>An Asynchronous MQTT client library for C.  </p>
<p>&copy; Copyright 2009, 2025 IBM Corp., Ian Craggs and others.</p>
<p>Version 1.3.14</p>
<p>An MQTT client application connects to MQTT-capable servers. A typical client is responsible for collecting information from a telemetry device and publishing the information to the server. It can also subscribe to topics, receive messages, and use this information to control the telemetry device.</p>
<p>MQTT clients implement the published MQTT v3 protocol. You can write your own API to the MQTT protocol using the programming language and platform of your choice. This can be time-consuming and error-prone.</p>
<p>To simplify writing MQTT client applications, this library encapsulates the MQTT v3 protocol for you. Using this library enables a fully functional MQTT client application to be written in a few lines of code. The information presented here documents the API provided by the Asynchronous MQTT Client library for C.</p>
<p><b>Using the client</b><br  />
 Applications that use the client library typically use a similar structure: </p><ul>
<li>
Create a client object </li>
<li>
Set the options to connect to an MQTT server </li>
<li>
Set up callback functions </li>
<li>
Connect the client to an MQTT server </li>
<li>
Subscribe to any topics the client needs to receive </li>
<li>
Repeat until finished: <ul>
<li>
Publish any messages the client needs to </li>
<li>
Handle any incoming messages </li>
</ul>
</li>
<li>
Disconnect the client </li>
<li>
Free any memory being used by the client </li>
</ul>
<p>Some simple examples are shown here: </p><ul>
<li>
<a class="el" href="publish.html">Publication example</a> </li>
<li>
<a class="el" href="subscribe.html">Subscription example</a> </li>
</ul>
<p>Additional information about important concepts is provided here: </p><ul>
<li>
<a class="el" href="async.html">Threading</a> </li>
<li>
<a class="el" href="wildcard.html">Subscription wildcards</a> </li>
<li>
<a class="el" href="qos.html">Quality of service</a> </li>
<li>
<a class="el" href="tracing.html">Tracing</a> </li>
<li>
<a class="el" href="auto_reconnect.html">Automatic Reconnect</a> </li>
<li>
<a class="el" href="offline_publish.html">Publish While Disconnected</a> </li>
<li>
<a class="el" href="_h_t_t_p_proxies.html">HTTP Proxies</a> </li>
</ul>
</div></div><!-- PageDoc -->
<a href="doxygen_crawl.html"></a>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:08 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
