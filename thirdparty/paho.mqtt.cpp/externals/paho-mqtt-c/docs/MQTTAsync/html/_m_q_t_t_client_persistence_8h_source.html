<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: MQTTClientPersistence.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">MQTTClientPersistence.h</div></div>
</div><!--header-->
<div class="contents">
<a href="_m_q_t_t_client_persistence_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="comment">/*******************************************************************************</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span><span class="comment"> * Copyright (c) 2009, 2020 IBM Corp.</span></div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span><span class="comment"> *</span></div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="comment"> * All rights reserved. This program and the accompanying materials</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span><span class="comment"> * are made available under the terms of the Eclipse Public License v2.0</span></div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span><span class="comment"> * and Eclipse Distribution License v1.0 which accompany this distribution. </span></div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno">    7</span><span class="comment"> *</span></div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span><span class="comment"> * The Eclipse Public License is available at </span></div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno">    9</span><span class="comment"> *    https://www.eclipse.org/legal/epl-2.0/</span></div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno">   10</span><span class="comment"> * and the Eclipse Distribution License is available at </span></div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno">   11</span><span class="comment"> *   http://www.eclipse.org/org/documents/edl-v10.php.</span></div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno">   12</span><span class="comment"> *</span></div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span><span class="comment"> * Contributors:</span></div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno">   14</span><span class="comment"> *    Ian Craggs - initial API and implementation and/or initial documentation</span></div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span><span class="comment"> *******************************************************************************/</span></div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno">   16</span> </div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno">   56</span><span class="comment">/*</span></div>
<div class="line"><a id="l00058" name="l00058"></a><span class="lineno">   58</span>*/</div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno">   59</span><span class="preprocessor">#if !defined(MQTTCLIENTPERSISTENCE_H)</span></div>
<div class="line"><a id="l00060" name="l00060"></a><span class="lineno">   60</span><span class="preprocessor">#define MQTTCLIENTPERSISTENCE_H</span></div>
<div class="line"><a id="l00061" name="l00061"></a><span class="lineno">   61</span><span class="comment">/*</span></div>
<div class="line"><a id="l00063" name="l00063"></a><span class="lineno">   63</span>*/</div>
<div class="line"><a id="l00064" name="l00064"></a><span class="lineno">   64</span> </div>
<div class="line"><a id="l00069" name="l00069"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_persistence_8h.html#aaa948291718a9c06369b854b0f64bc32">   69</a></span><span class="preprocessor">#define MQTTCLIENT_PERSISTENCE_DEFAULT 0</span></div>
<div class="line"><a id="l00074" name="l00074"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_persistence_8h.html#ae01e089313a65ac4661ed216b6ac00fa">   74</a></span><span class="preprocessor">#define MQTTCLIENT_PERSISTENCE_NONE 1</span></div>
<div class="line"><a id="l00079" name="l00079"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_persistence_8h.html#a5dc68b8616e4041e037bad94ce07681b">   79</a></span><span class="preprocessor">#define MQTTCLIENT_PERSISTENCE_USER 2</span></div>
<div class="line"><a id="l00080" name="l00080"></a><span class="lineno">   80</span> </div>
<div class="line"><a id="l00085" name="l00085"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_persistence_8h.html#ab716e21e53c84a5ad62aa962a2a8f7db">   85</a></span><span class="preprocessor">#define MQTTCLIENT_PERSISTENCE_ERROR -2</span></div>
<div class="line"><a id="l00086" name="l00086"></a><span class="lineno">   86</span> </div>
<div class="line"><a id="l00113" name="l00113"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_persistence_8h.html#a4c7d332bb16907058ae3b375488b6008">  113</a></span><span class="keyword">typedef</span> int (*<a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#a4c7d332bb16907058ae3b375488b6008">Persistence_open</a>)(<span class="keywordtype">void</span>** handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* clientID, <span class="keyword">const</span> <span class="keywordtype">char</span>* serverURI, <span class="keywordtype">void</span>* context);</div>
<div class="line"><a id="l00114" name="l00114"></a><span class="lineno">  114</span> </div>
<div class="line"><a id="l00123" name="l00123"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_persistence_8h.html#a3582de2c87e89f617e8e553b2a0e279a">  123</a></span><span class="keyword">typedef</span> int (*<a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#a3582de2c87e89f617e8e553b2a0e279a">Persistence_close</a>)(<span class="keywordtype">void</span>* handle); </div>
<div class="line"><a id="l00124" name="l00124"></a><span class="lineno">  124</span> </div>
<div class="line"><a id="l00140" name="l00140"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_persistence_8h.html#a44679cab77cfbd6e2a4639cdd27ac80c">  140</a></span><span class="keyword">typedef</span> int (*<a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#a44679cab77cfbd6e2a4639cdd27ac80c">Persistence_put</a>)(<span class="keywordtype">void</span>* handle, <span class="keywordtype">char</span>* key, <span class="keywordtype">int</span> bufcount, <span class="keywordtype">char</span>* buffers[], <span class="keywordtype">int</span> buflens[]);</div>
<div class="line"><a id="l00141" name="l00141"></a><span class="lineno">  141</span> </div>
<div class="line"><a id="l00156" name="l00156"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_persistence_8h.html#adc3aff3c570fa5509e9d6814a85ab867">  156</a></span><span class="keyword">typedef</span> int (*<a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#adc3aff3c570fa5509e9d6814a85ab867">Persistence_get</a>)(<span class="keywordtype">void</span>* handle, <span class="keywordtype">char</span>* key, <span class="keywordtype">char</span>** buffer, <span class="keywordtype">int</span>* buflen);</div>
<div class="line"><a id="l00157" name="l00157"></a><span class="lineno">  157</span> </div>
<div class="line"><a id="l00169" name="l00169"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_persistence_8h.html#a73350bf7208658bf5434a59f7bdbae90">  169</a></span><span class="keyword">typedef</span> int (*<a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#a73350bf7208658bf5434a59f7bdbae90">Persistence_remove</a>)(<span class="keywordtype">void</span>* handle, <span class="keywordtype">char</span>* key);</div>
<div class="line"><a id="l00170" name="l00170"></a><span class="lineno">  170</span> </div>
<div class="line"><a id="l00186" name="l00186"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_persistence_8h.html#a2601cc91eeabdbf9578f8dd45e4997a8">  186</a></span><span class="keyword">typedef</span> int (*<a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#a2601cc91eeabdbf9578f8dd45e4997a8">Persistence_keys</a>)(<span class="keywordtype">void</span>* handle, <span class="keywordtype">char</span>*** keys, <span class="keywordtype">int</span>* nkeys);</div>
<div class="line"><a id="l00187" name="l00187"></a><span class="lineno">  187</span> </div>
<div class="line"><a id="l00197" name="l00197"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_persistence_8h.html#acee7097c1a0ab44b98c870f533687887">  197</a></span><span class="keyword">typedef</span> int (*<a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#acee7097c1a0ab44b98c870f533687887">Persistence_clear</a>)(<span class="keywordtype">void</span>* handle);</div>
<div class="line"><a id="l00198" name="l00198"></a><span class="lineno">  198</span> </div>
<div class="line"><a id="l00208" name="l00208"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_persistence_8h.html#a753a0f9a9c51284d63a907af19c7bbba">  208</a></span><span class="keyword">typedef</span> int (*<a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#a753a0f9a9c51284d63a907af19c7bbba">Persistence_containskey</a>)(<span class="keywordtype">void</span>* handle, <span class="keywordtype">char</span>* key);</div>
<div class="line"><a id="l00209" name="l00209"></a><span class="lineno">  209</span> </div>
<div class="foldopen" id="foldopen00215" data-start="{" data-end="};">
<div class="line"><a id="l00215" name="l00215"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__persistence.html">  215</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span>{</div>
<div class="line"><a id="l00219" name="l00219"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__persistence.html#ae376f130b17d169ee51be68077a89ed0">  219</a></span>        <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_client__persistence.html#ae376f130b17d169ee51be68077a89ed0">context</a>;</div>
<div class="line"><a id="l00223" name="l00223"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__persistence.html#a1bae211b32415e6b349d5ae71599f9f4">  223</a></span>        <a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#a4c7d332bb16907058ae3b375488b6008">Persistence_open</a> <a class="code hl_variable" href="struct_m_q_t_t_client__persistence.html#a1bae211b32415e6b349d5ae71599f9f4">popen</a>;</div>
<div class="line"><a id="l00227" name="l00227"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__persistence.html#a7e50506912d2ec0e014cc25ec28fb402">  227</a></span>        <a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#a3582de2c87e89f617e8e553b2a0e279a">Persistence_close</a> <a class="code hl_variable" href="struct_m_q_t_t_client__persistence.html#a7e50506912d2ec0e014cc25ec28fb402">pclose</a>;</div>
<div class="line"><a id="l00231" name="l00231"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__persistence.html#a4114d9b9971cee18d7e4b9dd5736a608">  231</a></span>        <a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#a44679cab77cfbd6e2a4639cdd27ac80c">Persistence_put</a> <a class="code hl_variable" href="struct_m_q_t_t_client__persistence.html#a4114d9b9971cee18d7e4b9dd5736a608">pput</a>;</div>
<div class="line"><a id="l00235" name="l00235"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__persistence.html#a49155000b82a28ac3b3cb878f3a092d4">  235</a></span>        <a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#adc3aff3c570fa5509e9d6814a85ab867">Persistence_get</a> <a class="code hl_variable" href="struct_m_q_t_t_client__persistence.html#a49155000b82a28ac3b3cb878f3a092d4">pget</a>;</div>
<div class="line"><a id="l00239" name="l00239"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__persistence.html#a53150e443ca721b8623689371c2fbdb9">  239</a></span>        <a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#a73350bf7208658bf5434a59f7bdbae90">Persistence_remove</a> <a class="code hl_variable" href="struct_m_q_t_t_client__persistence.html#a53150e443ca721b8623689371c2fbdb9">premove</a>;</div>
<div class="line"><a id="l00243" name="l00243"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__persistence.html#a407e86a809e4b0b098a8c158f53b9606">  243</a></span>        <a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#a2601cc91eeabdbf9578f8dd45e4997a8">Persistence_keys</a> <a class="code hl_variable" href="struct_m_q_t_t_client__persistence.html#a407e86a809e4b0b098a8c158f53b9606">pkeys</a>;</div>
<div class="line"><a id="l00247" name="l00247"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__persistence.html#abc192dc88113c7d933b29d3561badbf5">  247</a></span>        <a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#acee7097c1a0ab44b98c870f533687887">Persistence_clear</a> <a class="code hl_variable" href="struct_m_q_t_t_client__persistence.html#abc192dc88113c7d933b29d3561badbf5">pclear</a>;</div>
<div class="line"><a id="l00251" name="l00251"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__persistence.html#ac103711576267f791325f2b70b6dc49d">  251</a></span>        <a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#a753a0f9a9c51284d63a907af19c7bbba">Persistence_containskey</a> <a class="code hl_variable" href="struct_m_q_t_t_client__persistence.html#ac103711576267f791325f2b70b6dc49d">pcontainskey</a>;</div>
<div class="line"><a id="l00252" name="l00252"></a><span class="lineno">  252</span>} <a class="code hl_struct" href="struct_m_q_t_t_client__persistence.html">MQTTClient_persistence</a>;</div>
</div>
<div class="line"><a id="l00253" name="l00253"></a><span class="lineno">  253</span> </div>
<div class="line"><a id="l00254" name="l00254"></a><span class="lineno">  254</span> </div>
<div class="line"><a id="l00264" name="l00264"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_persistence_8h.html#ab865640a1cc53b68622004c5a2d29fae">  264</a></span><span class="keyword">typedef</span> <span class="keywordtype">int</span> <a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#ab865640a1cc53b68622004c5a2d29fae">MQTTPersistence_beforeWrite</a>(<span class="keywordtype">void</span>* context, <span class="keywordtype">int</span> bufcount, <span class="keywordtype">char</span>* buffers[], <span class="keywordtype">int</span> buflens[]);</div>
<div class="line"><a id="l00265" name="l00265"></a><span class="lineno">  265</span> </div>
<div class="line"><a id="l00266" name="l00266"></a><span class="lineno">  266</span> </div>
<div class="line"><a id="l00275" name="l00275"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_persistence_8h.html#af5a966a574c6ad7a35f1ebb7edd5c1c4">  275</a></span><span class="keyword">typedef</span> <span class="keywordtype">int</span> <a class="code hl_typedef" href="_m_q_t_t_client_persistence_8h.html#af5a966a574c6ad7a35f1ebb7edd5c1c4">MQTTPersistence_afterRead</a>(<span class="keywordtype">void</span>* context, <span class="keywordtype">char</span>** buffer, <span class="keywordtype">int</span>* buflen);</div>
<div class="line"><a id="l00276" name="l00276"></a><span class="lineno">  276</span> </div>
<div class="line"><a id="l00277" name="l00277"></a><span class="lineno">  277</span><span class="preprocessor">#endif</span></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html_a2601cc91eeabdbf9578f8dd45e4997a8"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html#a2601cc91eeabdbf9578f8dd45e4997a8">Persistence_keys</a></div><div class="ttdeci">int(* Persistence_keys)(void *handle, char ***keys, int *nkeys)</div><div class="ttdoc">Returns the keys in this persistent data store.</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:186</div></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html_a3582de2c87e89f617e8e553b2a0e279a"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html#a3582de2c87e89f617e8e553b2a0e279a">Persistence_close</a></div><div class="ttdeci">int(* Persistence_close)(void *handle)</div><div class="ttdoc">Close the persistent store referred to by the handle.</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:123</div></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html_a44679cab77cfbd6e2a4639cdd27ac80c"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html#a44679cab77cfbd6e2a4639cdd27ac80c">Persistence_put</a></div><div class="ttdeci">int(* Persistence_put)(void *handle, char *key, int bufcount, char *buffers[], int buflens[])</div><div class="ttdoc">Put the specified data into the persistent store.</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:140</div></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html_a4c7d332bb16907058ae3b375488b6008"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html#a4c7d332bb16907058ae3b375488b6008">Persistence_open</a></div><div class="ttdeci">int(* Persistence_open)(void **handle, const char *clientID, const char *serverURI, void *context)</div><div class="ttdoc">Initialize the persistent store.</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:113</div></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html_a73350bf7208658bf5434a59f7bdbae90"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html#a73350bf7208658bf5434a59f7bdbae90">Persistence_remove</a></div><div class="ttdeci">int(* Persistence_remove)(void *handle, char *key)</div><div class="ttdoc">Remove the data for the specified key from the store.</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:169</div></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html_a753a0f9a9c51284d63a907af19c7bbba"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html#a753a0f9a9c51284d63a907af19c7bbba">Persistence_containskey</a></div><div class="ttdeci">int(* Persistence_containskey)(void *handle, char *key)</div><div class="ttdoc">Returns whether any data has been persisted using the specified key.</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:208</div></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html_ab865640a1cc53b68622004c5a2d29fae"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html#ab865640a1cc53b68622004c5a2d29fae">MQTTPersistence_beforeWrite</a></div><div class="ttdeci">int MQTTPersistence_beforeWrite(void *context, int bufcount, char *buffers[], int buflens[])</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:264</div></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html_acee7097c1a0ab44b98c870f533687887"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html#acee7097c1a0ab44b98c870f533687887">Persistence_clear</a></div><div class="ttdeci">int(* Persistence_clear)(void *handle)</div><div class="ttdoc">Clears the persistence store, so that it no longer contains any persisted data.</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:197</div></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html_adc3aff3c570fa5509e9d6814a85ab867"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html#adc3aff3c570fa5509e9d6814a85ab867">Persistence_get</a></div><div class="ttdeci">int(* Persistence_get)(void *handle, char *key, char **buffer, int *buflen)</div><div class="ttdoc">Retrieve the specified data from the persistent store.</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:156</div></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html_af5a966a574c6ad7a35f1ebb7edd5c1c4"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html#af5a966a574c6ad7a35f1ebb7edd5c1c4">MQTTPersistence_afterRead</a></div><div class="ttdeci">int MQTTPersistence_afterRead(void *context, char **buffer, int *buflen)</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:275</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__persistence_html"><div class="ttname"><a href="struct_m_q_t_t_client__persistence.html">MQTTClient_persistence</a></div><div class="ttdoc">A structure containing the function pointers to a persistence implementation and the context or state...</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:215</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__persistence_html_a1bae211b32415e6b349d5ae71599f9f4"><div class="ttname"><a href="struct_m_q_t_t_client__persistence.html#a1bae211b32415e6b349d5ae71599f9f4">MQTTClient_persistence::popen</a></div><div class="ttdeci">Persistence_open popen</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:223</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__persistence_html_a407e86a809e4b0b098a8c158f53b9606"><div class="ttname"><a href="struct_m_q_t_t_client__persistence.html#a407e86a809e4b0b098a8c158f53b9606">MQTTClient_persistence::pkeys</a></div><div class="ttdeci">Persistence_keys pkeys</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:243</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__persistence_html_a4114d9b9971cee18d7e4b9dd5736a608"><div class="ttname"><a href="struct_m_q_t_t_client__persistence.html#a4114d9b9971cee18d7e4b9dd5736a608">MQTTClient_persistence::pput</a></div><div class="ttdeci">Persistence_put pput</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:231</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__persistence_html_a49155000b82a28ac3b3cb878f3a092d4"><div class="ttname"><a href="struct_m_q_t_t_client__persistence.html#a49155000b82a28ac3b3cb878f3a092d4">MQTTClient_persistence::pget</a></div><div class="ttdeci">Persistence_get pget</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:235</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__persistence_html_a53150e443ca721b8623689371c2fbdb9"><div class="ttname"><a href="struct_m_q_t_t_client__persistence.html#a53150e443ca721b8623689371c2fbdb9">MQTTClient_persistence::premove</a></div><div class="ttdeci">Persistence_remove premove</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:239</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__persistence_html_a7e50506912d2ec0e014cc25ec28fb402"><div class="ttname"><a href="struct_m_q_t_t_client__persistence.html#a7e50506912d2ec0e014cc25ec28fb402">MQTTClient_persistence::pclose</a></div><div class="ttdeci">Persistence_close pclose</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:227</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__persistence_html_abc192dc88113c7d933b29d3561badbf5"><div class="ttname"><a href="struct_m_q_t_t_client__persistence.html#abc192dc88113c7d933b29d3561badbf5">MQTTClient_persistence::pclear</a></div><div class="ttdeci">Persistence_clear pclear</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:247</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__persistence_html_ac103711576267f791325f2b70b6dc49d"><div class="ttname"><a href="struct_m_q_t_t_client__persistence.html#ac103711576267f791325f2b70b6dc49d">MQTTClient_persistence::pcontainskey</a></div><div class="ttdeci">Persistence_containskey pcontainskey</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:251</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__persistence_html_ae376f130b17d169ee51be68077a89ed0"><div class="ttname"><a href="struct_m_q_t_t_client__persistence.html#ae376f130b17d169ee51be68077a89ed0">MQTTClient_persistence::context</a></div><div class="ttdeci">void * context</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:219</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:07 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
