<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: MQTTAsync_createOptions Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">MQTTAsync_createOptions Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="_m_q_t_t_async_8h_source.html">MQTTAsync.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:aa5326df180cb23c59afbcab711a06479" id="r_aa5326df180cb23c59afbcab711a06479"><td class="memItemLeft" align="right" valign="top">char&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa5326df180cb23c59afbcab711a06479">struct_id</a> [4]</td></tr>
<tr class="separator:aa5326df180cb23c59afbcab711a06479"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0761a5e5be0383882e42924de8e51f82" id="r_a0761a5e5be0383882e42924de8e51f82"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0761a5e5be0383882e42924de8e51f82">struct_version</a></td></tr>
<tr class="separator:a0761a5e5be0383882e42924de8e51f82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a078cd68d8f896ce7eac0cc83d4486a2c" id="r_a078cd68d8f896ce7eac0cc83d4486a2c"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a078cd68d8f896ce7eac0cc83d4486a2c">sendWhileDisconnected</a></td></tr>
<tr class="separator:a078cd68d8f896ce7eac0cc83d4486a2c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b74acf6f315bb5fe36266bc9647ee97" id="r_a3b74acf6f315bb5fe36266bc9647ee97"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3b74acf6f315bb5fe36266bc9647ee97">maxBufferedMessages</a></td></tr>
<tr class="separator:a3b74acf6f315bb5fe36266bc9647ee97"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a12d546fd0ccf4e1091b18e1b735c7240" id="r_a12d546fd0ccf4e1091b18e1b735c7240"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a12d546fd0ccf4e1091b18e1b735c7240">MQTTVersion</a></td></tr>
<tr class="separator:a12d546fd0ccf4e1091b18e1b735c7240"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe7fdbe18bfd3577a75d3b386d69406c" id="r_abe7fdbe18bfd3577a75d3b386d69406c"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abe7fdbe18bfd3577a75d3b386d69406c">allowDisconnectedSendAtAnyTime</a></td></tr>
<tr class="separator:abe7fdbe18bfd3577a75d3b386d69406c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a76de37b3cff885e83db204a347fe0a2d" id="r_a76de37b3cff885e83db204a347fe0a2d"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a76de37b3cff885e83db204a347fe0a2d">deleteOldestMessages</a></td></tr>
<tr class="separator:a76de37b3cff885e83db204a347fe0a2d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a231b8890c3bc2ea07f7c599896f30691" id="r_a231b8890c3bc2ea07f7c599896f30691"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a231b8890c3bc2ea07f7c599896f30691">restoreMessages</a></td></tr>
<tr class="separator:a231b8890c3bc2ea07f7c599896f30691"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0c3ea2641e188542c787e71e2c521a0b" id="r_a0c3ea2641e188542c787e71e2c521a0b"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0c3ea2641e188542c787e71e2c521a0b">persistQoS0</a></td></tr>
<tr class="separator:a0c3ea2641e188542c787e71e2c521a0b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Options for the <a class="el" href="_m_q_t_t_async_8h.html#a78cbe1b851fea48001112f7ba9e4ea62">MQTTAsync_createWithOptions</a> call </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="aa5326df180cb23c59afbcab711a06479" name="aa5326df180cb23c59afbcab711a06479"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa5326df180cb23c59afbcab711a06479">&#9670;&#160;</a></span>struct_id</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char struct_id[4]</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The eyecatcher for this structure. must be MQCO. </p>

</div>
</div>
<a id="a0761a5e5be0383882e42924de8e51f82" name="a0761a5e5be0383882e42924de8e51f82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0761a5e5be0383882e42924de8e51f82">&#9670;&#160;</a></span>struct_version</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int struct_version</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The version number of this structure. Must be 0, 1, 2 or 3 0 means no MQTTVersion 1 means no allowDisconnectedSendAtAnyTime, deleteOldestMessages, restoreMessages 2 means no persistQoS0 </p>

</div>
</div>
<a id="a078cd68d8f896ce7eac0cc83d4486a2c" name="a078cd68d8f896ce7eac0cc83d4486a2c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a078cd68d8f896ce7eac0cc83d4486a2c">&#9670;&#160;</a></span>sendWhileDisconnected</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int sendWhileDisconnected</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Whether to allow messages to be sent when the client library is not connected. </p>

</div>
</div>
<a id="a3b74acf6f315bb5fe36266bc9647ee97" name="a3b74acf6f315bb5fe36266bc9647ee97"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3b74acf6f315bb5fe36266bc9647ee97">&#9670;&#160;</a></span>maxBufferedMessages</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int maxBufferedMessages</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The maximum number of messages allowed to be buffered. This is intended to be used to limit the number of messages queued while the client is not connected. It also applies when the client is connected, however, so has to be greater than 0. </p>

</div>
</div>
<a id="a12d546fd0ccf4e1091b18e1b735c7240" name="a12d546fd0ccf4e1091b18e1b735c7240"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a12d546fd0ccf4e1091b18e1b735c7240">&#9670;&#160;</a></span>MQTTVersion</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTVersion</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Whether the MQTT version is 3.1, 3.1.1, or 5. To use V5, this must be set. MQTT V5 has to be chosen here, because during the create call the message persistence is initialized, and we want to know whether the format of any persisted messages is appropriate for the MQTT version we are going to connect with. Selecting 3.1 or 3.1.1 and attempting to read 5.0 persisted messages will result in an error on create. <br  />
 </p>

</div>
</div>
<a id="abe7fdbe18bfd3577a75d3b386d69406c" name="abe7fdbe18bfd3577a75d3b386d69406c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abe7fdbe18bfd3577a75d3b386d69406c">&#9670;&#160;</a></span>allowDisconnectedSendAtAnyTime</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int allowDisconnectedSendAtAnyTime</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Allow sending of messages while disconnected before a first successful connect. </p>

</div>
</div>
<a id="a76de37b3cff885e83db204a347fe0a2d" name="a76de37b3cff885e83db204a347fe0a2d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a76de37b3cff885e83db204a347fe0a2d">&#9670;&#160;</a></span>deleteOldestMessages</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int deleteOldestMessages</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a231b8890c3bc2ea07f7c599896f30691" name="a231b8890c3bc2ea07f7c599896f30691"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a231b8890c3bc2ea07f7c599896f30691">&#9670;&#160;</a></span>restoreMessages</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int restoreMessages</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0c3ea2641e188542c787e71e2c521a0b" name="a0c3ea2641e188542c787e71e2c521a0b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0c3ea2641e188542c787e71e2c521a0b">&#9670;&#160;</a></span>persistQoS0</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int persistQoS0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_m_q_t_t_async_8h_source.html">MQTTAsync.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:07 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
