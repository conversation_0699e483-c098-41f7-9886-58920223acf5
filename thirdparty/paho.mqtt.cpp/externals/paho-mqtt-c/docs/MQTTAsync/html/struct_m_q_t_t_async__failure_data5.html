<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: MQTTAsync_failureData5 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">MQTTAsync_failureData5 Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="_m_q_t_t_async_8h_source.html">MQTTAsync.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:aa5326df180cb23c59afbcab711a06479" id="r_aa5326df180cb23c59afbcab711a06479"><td class="memItemLeft" align="right" valign="top">char&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa5326df180cb23c59afbcab711a06479">struct_id</a> [4]</td></tr>
<tr class="separator:aa5326df180cb23c59afbcab711a06479"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0761a5e5be0383882e42924de8e51f82" id="r_a0761a5e5be0383882e42924de8e51f82"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0761a5e5be0383882e42924de8e51f82">struct_version</a></td></tr>
<tr class="separator:a0761a5e5be0383882e42924de8e51f82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af8f771e67d284379111151b003c0d810" id="r_af8f771e67d284379111151b003c0d810"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af8f771e67d284379111151b003c0d810">token</a></td></tr>
<tr class="separator:af8f771e67d284379111151b003c0d810"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a580d8a8ecb285f5a86c2a3865438f8ee" id="r_a580d8a8ecb285f5a86c2a3865438f8ee"><td class="memItemLeft" align="right" valign="top">enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a580d8a8ecb285f5a86c2a3865438f8ee">reasonCode</a></td></tr>
<tr class="separator:a580d8a8ecb285f5a86c2a3865438f8ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1594008402f7307e4de8fa6131656dde" id="r_a1594008402f7307e4de8fa6131656dde"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1594008402f7307e4de8fa6131656dde">properties</a></td></tr>
<tr class="separator:a1594008402f7307e4de8fa6131656dde"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a45a5b7c00a796a23f01673cef1dbe0a9" id="r_a45a5b7c00a796a23f01673cef1dbe0a9"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a45a5b7c00a796a23f01673cef1dbe0a9">code</a></td></tr>
<tr class="separator:a45a5b7c00a796a23f01673cef1dbe0a9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a254bf0858da09c96a48daf64404eb4f8" id="r_a254bf0858da09c96a48daf64404eb4f8"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a254bf0858da09c96a48daf64404eb4f8">message</a></td></tr>
<tr class="separator:a254bf0858da09c96a48daf64404eb4f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a38dfee9f038f473c95af46fcef5dd3e9" id="r_a38dfee9f038f473c95af46fcef5dd3e9"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a38dfee9f038f473c95af46fcef5dd3e9">packet_type</a></td></tr>
<tr class="separator:a38dfee9f038f473c95af46fcef5dd3e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The data returned on completion of an unsuccessful API call in the response callback onFailure. </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="aa5326df180cb23c59afbcab711a06479" name="aa5326df180cb23c59afbcab711a06479"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa5326df180cb23c59afbcab711a06479">&#9670;&#160;</a></span>struct_id</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char struct_id[4]</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The eyecatcher for this structure. Will be MQFD. </p>

</div>
</div>
<a id="a0761a5e5be0383882e42924de8e51f82" name="a0761a5e5be0383882e42924de8e51f82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0761a5e5be0383882e42924de8e51f82">&#9670;&#160;</a></span>struct_version</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int struct_version</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The version number of this structure. Will be 0 </p>

</div>
</div>
<a id="af8f771e67d284379111151b003c0d810" name="af8f771e67d284379111151b003c0d810"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af8f771e67d284379111151b003c0d810">&#9670;&#160;</a></span>token</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> token</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A token identifying the failed request. </p>

</div>
</div>
<a id="a580d8a8ecb285f5a86c2a3865438f8ee" name="a580d8a8ecb285f5a86c2a3865438f8ee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a580d8a8ecb285f5a86c2a3865438f8ee">&#9670;&#160;</a></span>reasonCode</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reasonCode</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The MQTT reason code returned. </p>

</div>
</div>
<a id="a1594008402f7307e4de8fa6131656dde" name="a1594008402f7307e4de8fa6131656dde"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1594008402f7307e4de8fa6131656dde">&#9670;&#160;</a></span>properties</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> properties</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The MQTT properties on the ack, if any. </p>

</div>
</div>
<a id="a45a5b7c00a796a23f01673cef1dbe0a9" name="a45a5b7c00a796a23f01673cef1dbe0a9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a45a5b7c00a796a23f01673cef1dbe0a9">&#9670;&#160;</a></span>code</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int code</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A numeric code identifying the MQTT client library error. </p>

</div>
</div>
<a id="a254bf0858da09c96a48daf64404eb4f8" name="a254bf0858da09c96a48daf64404eb4f8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a254bf0858da09c96a48daf64404eb4f8">&#9670;&#160;</a></span>message</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* message</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Optional further text explaining the error. Can be NULL. </p>

</div>
</div>
<a id="a38dfee9f038f473c95af46fcef5dd3e9" name="a38dfee9f038f473c95af46fcef5dd3e9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a38dfee9f038f473c95af46fcef5dd3e9">&#9670;&#160;</a></span>packet_type</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int packet_type</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Packet type on which the failure occurred - used for publish QoS 1/2 exchanges </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_m_q_t_t_async_8h_source.html">MQTTAsync.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:08 for Paho Asynchronous MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
