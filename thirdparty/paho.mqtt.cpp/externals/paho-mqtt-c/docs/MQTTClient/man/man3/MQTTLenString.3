.TH "MQTTLenString" 3 "Tue Jan 7 2025 13:21:06" "Paho MQTT C Client Library" \" -*- nroff -*-
.ad l
.nh
.SH NAME
MQTTLenString
.SH SYNOPSIS
.br
.PP
.PP
\fR#include <MQTTProperties\&.h>\fP
.SS "Data Fields"

.in +1c
.ti -1c
.RI "int \fBlen\fP"
.br
.ti -1c
.RI "char * \fBdata\fP"
.br
.in -1c
.SH "Detailed Description"
.PP 
The data for a length delimited string 
.SH "Field Documentation"
.PP 
.SS "int len"
the length of the string 
.SS "char* data"
pointer to the string data 

.SH "Author"
.PP 
Generated automatically by Doxygen for Paho MQTT C Client Library from the source code\&.
