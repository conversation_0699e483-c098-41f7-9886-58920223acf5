.TH "MQTTClient_nameValue" 3 "Tu<PERSON> Jan 7 2025 13:21:06" "Paho MQTT C Client Library" \" -*- nroff -*-
.ad l
.nh
.SH NAME
MQTTClient_nameValue
.SH SYNOPSIS
.br
.PP
.PP
\fR#include <MQTTClient\&.h>\fP
.SS "Data Fields"

.in +1c
.ti -1c
.RI "const char * \fBname\fP"
.br
.ti -1c
.RI "const char * \fBvalue\fP"
.br
.in -1c
.SH "Detailed Description"
.PP 
MQTTClient_libraryInfo is used to store details relating to the currently used library such as the version in use, the time it was built and relevant openSSL options\&. There is one static instance of this struct in MQTTClient\&.c 
.SH "Field Documentation"
.PP 
.SS "const char* name"

.SS "const char* value"


.SH "Author"
.PP 
Generated automatically by Doxygen for Paho MQTT C Client Library from the source code\&.
