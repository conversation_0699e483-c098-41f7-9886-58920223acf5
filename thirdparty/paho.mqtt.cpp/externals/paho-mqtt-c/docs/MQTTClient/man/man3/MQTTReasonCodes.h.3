.TH "MQTTReasonCodes.h" 3 "Tue Jan 7 2025 13:21:06" "Paho MQTT C Client Library" \" -*- nroff -*-
.ad l
.nh
.SH NAME
MQTTReasonCodes.h
.SH SYNOPSIS
.br
.PP
\fR#include 'MQTTExportDeclarations\&.h'\fP
.br

.SS "Enumerations"

.in +1c
.ti -1c
.RI "enum \fBMQTTReasonCodes\fP { \fBMQTTREASONCODE_SUCCESS\fP = 0, \fBMQTTREASONCODE_NORMAL_DISCONNECTION\fP = 0, \fBMQTTREASONCODE_GRANTED_QOS_0\fP = 0, \fBMQTTREASONCODE_GRANTED_QOS_1\fP = 1, \fBMQTTREASONCODE_GRANTED_QOS_2\fP = 2, \fBMQTTREASONCODE_DISCONNECT_WITH_WILL_MESSAGE\fP = 4, \fBMQTTREASONCODE_NO_MATCHING_SUBSCRIBERS\fP = 16, \fBMQTTREASONCODE_NO_SUBSCRIPTION_FOUND\fP = 17, \fBMQTTREASONCODE_CONTINUE_AUTHENTICATION\fP = 24, \fBMQTTREASONCODE_RE_AUTHENTICATE\fP = 25, \fBMQTTREASONCODE_UNSPECIFIED_ERROR\fP = 128, \fBMQTTREASONCODE_MALFORMED_PACKET\fP = 129, \fBMQTTREASONCODE_PROTOCOL_ERROR\fP = 130, \fBMQTTREASONCODE_IMPLEMENTATION_SPECIFIC_ERROR\fP = 131, \fBMQTTREASONCODE_UNSUPPORTED_PROTOCOL_VERSION\fP = 132, \fBMQTTREASONCODE_CLIENT_IDENTIFIER_NOT_VALID\fP = 133, \fBMQTTREASONCODE_BAD_USER_NAME_OR_PASSWORD\fP = 134, \fBMQTTREASONCODE_NOT_AUTHORIZED\fP = 135, \fBMQTTREASONCODE_SERVER_UNAVAILABLE\fP = 136, \fBMQTTREASONCODE_SERVER_BUSY\fP = 137, \fBMQTTREASONCODE_BANNED\fP = 138, \fBMQTTREASONCODE_SERVER_SHUTTING_DOWN\fP = 139, \fBMQTTREASONCODE_BAD_AUTHENTICATION_METHOD\fP = 140, \fBMQTTREASONCODE_KEEP_ALIVE_TIMEOUT\fP = 141, \fBMQTTREASONCODE_SESSION_TAKEN_OVER\fP = 142, \fBMQTTREASONCODE_TOPIC_FILTER_INVALID\fP = 143, \fBMQTTREASONCODE_TOPIC_NAME_INVALID\fP = 144, \fBMQTTREASONCODE_PACKET_IDENTIFIER_IN_USE\fP = 145, \fBMQTTREASONCODE_PACKET_IDENTIFIER_NOT_FOUND\fP = 146, \fBMQTTREASONCODE_RECEIVE_MAXIMUM_EXCEEDED\fP = 147, \fBMQTTREASONCODE_TOPIC_ALIAS_INVALID\fP = 148, \fBMQTTREASONCODE_PACKET_TOO_LARGE\fP = 149, \fBMQTTREASONCODE_MESSAGE_RATE_TOO_HIGH\fP = 150, \fBMQTTREASONCODE_QUOTA_EXCEEDED\fP = 151, \fBMQTTREASONCODE_ADMINISTRATIVE_ACTION\fP = 152, \fBMQTTREASONCODE_PAYLOAD_FORMAT_INVALID\fP = 153, \fBMQTTREASONCODE_RETAIN_NOT_SUPPORTED\fP = 154, \fBMQTTREASONCODE_QOS_NOT_SUPPORTED\fP = 155, \fBMQTTREASONCODE_USE_ANOTHER_SERVER\fP = 156, \fBMQTTREASONCODE_SERVER_MOVED\fP = 157, \fBMQTTREASONCODE_SHARED_SUBSCRIPTIONS_NOT_SUPPORTED\fP = 158, \fBMQTTREASONCODE_CONNECTION_RATE_EXCEEDED\fP = 159, \fBMQTTREASONCODE_MAXIMUM_CONNECT_TIME\fP = 160, \fBMQTTREASONCODE_SUBSCRIPTION_IDENTIFIERS_NOT_SUPPORTED\fP = 161, \fBMQTTREASONCODE_WILDCARD_SUBSCRIPTIONS_NOT_SUPPORTED\fP = 162 }"
.br
.in -1c
.SS "Functions"

.in +1c
.ti -1c
.RI "const char * \fBMQTTReasonCode_toString\fP (enum \fBMQTTReasonCodes\fP value)"
.br
.in -1c
.SH "Enumeration Type Documentation"
.PP 
.SS "enum \fBMQTTReasonCodes\fP"
The MQTT V5 one byte reason code 
.PP
\fBEnumerator\fP
.in +1c
.TP
\f(BIMQTTREASONCODE_SUCCESS \fP
.TP
\f(BIMQTTREASONCODE_NORMAL_DISCONNECTION \fP
.TP
\f(BIMQTTREASONCODE_GRANTED_QOS_0 \fP
.TP
\f(BIMQTTREASONCODE_GRANTED_QOS_1 \fP
.TP
\f(BIMQTTREASONCODE_GRANTED_QOS_2 \fP
.TP
\f(BIMQTTREASONCODE_DISCONNECT_WITH_WILL_MESSAGE \fP
.TP
\f(BIMQTTREASONCODE_NO_MATCHING_SUBSCRIBERS \fP
.TP
\f(BIMQTTREASONCODE_NO_SUBSCRIPTION_FOUND \fP
.TP
\f(BIMQTTREASONCODE_CONTINUE_AUTHENTICATION \fP
.TP
\f(BIMQTTREASONCODE_RE_AUTHENTICATE \fP
.TP
\f(BIMQTTREASONCODE_UNSPECIFIED_ERROR \fP
.TP
\f(BIMQTTREASONCODE_MALFORMED_PACKET \fP
.TP
\f(BIMQTTREASONCODE_PROTOCOL_ERROR \fP
.TP
\f(BIMQTTREASONCODE_IMPLEMENTATION_SPECIFIC_ERROR \fP
.TP
\f(BIMQTTREASONCODE_UNSUPPORTED_PROTOCOL_VERSION \fP
.TP
\f(BIMQTTREASONCODE_CLIENT_IDENTIFIER_NOT_VALID \fP
.TP
\f(BIMQTTREASONCODE_BAD_USER_NAME_OR_PASSWORD \fP
.TP
\f(BIMQTTREASONCODE_NOT_AUTHORIZED \fP
.TP
\f(BIMQTTREASONCODE_SERVER_UNAVAILABLE \fP
.TP
\f(BIMQTTREASONCODE_SERVER_BUSY \fP
.TP
\f(BIMQTTREASONCODE_BANNED \fP
.TP
\f(BIMQTTREASONCODE_SERVER_SHUTTING_DOWN \fP
.TP
\f(BIMQTTREASONCODE_BAD_AUTHENTICATION_METHOD \fP
.TP
\f(BIMQTTREASONCODE_KEEP_ALIVE_TIMEOUT \fP
.TP
\f(BIMQTTREASONCODE_SESSION_TAKEN_OVER \fP
.TP
\f(BIMQTTREASONCODE_TOPIC_FILTER_INVALID \fP
.TP
\f(BIMQTTREASONCODE_TOPIC_NAME_INVALID \fP
.TP
\f(BIMQTTREASONCODE_PACKET_IDENTIFIER_IN_USE \fP
.TP
\f(BIMQTTREASONCODE_PACKET_IDENTIFIER_NOT_FOUND \fP
.TP
\f(BIMQTTREASONCODE_RECEIVE_MAXIMUM_EXCEEDED \fP
.TP
\f(BIMQTTREASONCODE_TOPIC_ALIAS_INVALID \fP
.TP
\f(BIMQTTREASONCODE_PACKET_TOO_LARGE \fP
.TP
\f(BIMQTTREASONCODE_MESSAGE_RATE_TOO_HIGH \fP
.TP
\f(BIMQTTREASONCODE_QUOTA_EXCEEDED \fP
.TP
\f(BIMQTTREASONCODE_ADMINISTRATIVE_ACTION \fP
.TP
\f(BIMQTTREASONCODE_PAYLOAD_FORMAT_INVALID \fP
.TP
\f(BIMQTTREASONCODE_RETAIN_NOT_SUPPORTED \fP
.TP
\f(BIMQTTREASONCODE_QOS_NOT_SUPPORTED \fP
.TP
\f(BIMQTTREASONCODE_USE_ANOTHER_SERVER \fP
.TP
\f(BIMQTTREASONCODE_SERVER_MOVED \fP
.TP
\f(BIMQTTREASONCODE_SHARED_SUBSCRIPTIONS_NOT_SUPPORTED \fP
.TP
\f(BIMQTTREASONCODE_CONNECTION_RATE_EXCEEDED \fP
.TP
\f(BIMQTTREASONCODE_MAXIMUM_CONNECT_TIME \fP
.TP
\f(BIMQTTREASONCODE_SUBSCRIPTION_IDENTIFIERS_NOT_SUPPORTED \fP
.TP
\f(BIMQTTREASONCODE_WILDCARD_SUBSCRIPTIONS_NOT_SUPPORTED \fP
.SH "Function Documentation"
.PP 
.SS "const char * MQTTReasonCode_toString (enum \fBMQTTReasonCodes\fP value)\fR [extern]\fP"
Returns a printable string description of an MQTT V5 reason code\&. 
.PP
\fBParameters\fP
.RS 4
\fIvalue\fP an MQTT V5 reason code\&. 
.RE
.PP
\fBReturns\fP
.RS 4
the printable string description of the input reason code\&. NULL if the code was not found\&. 
.RE
.PP

.SH "Author"
.PP 
Generated automatically by Doxygen for Paho MQTT C Client Library from the source code\&.
