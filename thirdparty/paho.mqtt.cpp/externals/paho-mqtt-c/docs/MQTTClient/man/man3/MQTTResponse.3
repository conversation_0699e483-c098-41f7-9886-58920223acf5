.TH "MQTTResponse" 3 "Tue Jan 7 2025 13:21:06" "Paho MQTT C Client Library" \" -*- nroff -*-
.ad l
.nh
.SH NAME
MQTTResponse
.SH SYNOPSIS
.br
.PP
.PP
\fR#include <MQTTClient\&.h>\fP
.SS "Data Fields"

.in +1c
.ti -1c
.RI "int \fBversion\fP"
.br
.ti -1c
.RI "enum \fBMQTTReasonCodes\fP \fBreasonCode\fP"
.br
.ti -1c
.RI "int \fBreasonCodeCount\fP"
.br
.ti -1c
.RI "enum \fBMQTTReasonCodes\fP * \fBreasonCodes\fP"
.br
.ti -1c
.RI "\fBMQTTProperties\fP * \fBproperties\fP"
.br
.in -1c
.SH "Detailed Description"
.PP 
MQTT version 5\&.0 response information 
.SH "Field Documentation"
.PP 
.SS "int version"

.SS "enum \fBMQTTReasonCodes\fP reasonCode"

.SS "int reasonCodeCount"

.SS "enum \fBMQTTReasonCodes\fP* reasonCodes"

.SS "\fBMQTTProperties\fP* properties"


.SH "Author"
.PP 
Generated automatically by Doxygen for Paho MQTT C Client Library from the source code\&.
