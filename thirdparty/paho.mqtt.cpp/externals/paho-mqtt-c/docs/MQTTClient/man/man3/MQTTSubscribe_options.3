.TH "MQTTSubscribe_options" 3 "Tue Jan 7 2025 13:21:06" "Paho MQTT C Client Library" \" -*- nroff -*-
.ad l
.nh
.SH NAME
MQTTSubscribe_options
.SH SYNOPSIS
.br
.PP
.PP
\fR#include <MQTTSubscribeOpts\&.h>\fP
.SS "Data Fields"

.in +1c
.ti -1c
.RI "char \fBstruct_id\fP [4]"
.br
.ti -1c
.RI "int \fBstruct_version\fP"
.br
.ti -1c
.RI "unsigned char \fBnoLocal\fP"
.br
.ti -1c
.RI "unsigned char \fBretainAsPublished\fP"
.br
.ti -1c
.RI "unsigned char \fBretainHandling\fP"
.br
.in -1c
.SH "Detailed Description"
.PP 
The MQTT V5 subscribe options, apart from QoS which existed before V5\&. 
.SH "Field Documentation"
.PP 
.SS "char struct_id[4]"
The eyecatcher for this structure\&. Must be MQSO\&. 
.SS "int struct_version"
The version number of this structure\&. Must be 0\&. 
.SS "unsigned char noLocal"
To not receive our own publications, set to 1\&. 0 is the original MQTT behaviour - all messages matching the subscription are received\&. 
.SS "unsigned char retainAsPublished"
To keep the retain flag as on the original publish message, set to 1\&. If 0, defaults to the original MQTT behaviour where the retain flag is only set on publications sent by a broker if in response to a subscribe request\&. 
.SS "unsigned char retainHandling"
0 - send retained messages at the time of the subscribe (original MQTT behaviour) 1 - send retained messages on subscribe only if the subscription is new 2 - do not send retained messages at all 

.SH "Author"
.PP 
Generated automatically by Doxygen for Paho MQTT C Client Library from the source code\&.
