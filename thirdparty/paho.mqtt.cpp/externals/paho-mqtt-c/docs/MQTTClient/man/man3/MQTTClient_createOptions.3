.TH "MQTTClient_createOptions" 3 "Tue Jan 7 2025 13:21:06" "Paho MQTT C Client Library" \" -*- nroff -*-
.ad l
.nh
.SH NAME
MQTTClient_createOptions
.SH SYNOPSIS
.br
.PP
.PP
\fR#include <MQTTClient\&.h>\fP
.SS "Data Fields"

.in +1c
.ti -1c
.RI "char \fBstruct_id\fP [4]"
.br
.ti -1c
.RI "int \fBstruct_version\fP"
.br
.ti -1c
.RI "int \fBMQTTVersion\fP"
.br
.in -1c
.SH "Detailed Description"
.PP 
Options for the \fBMQTTClient_createWithOptions\fP call 
.SH "Field Documentation"
.PP 
.SS "char struct_id[4]"
The eyecatcher for this structure\&. must be MQCO\&. 
.SS "int struct_version"
The version number of this structure\&. Must be 0 
.SS "int MQTTVersion"
Whether the MQTT version is 3\&.1, 3\&.1\&.1, or 5\&. To use V5, this must be set\&. MQTT V5 has to be chosen here, because during the create call the message persistence is initialized, and we want to know whether the format of any persisted messages is appropriate for the MQTT version we are going to connect with\&. Selecting 3\&.1 or 3\&.1\&.1 and attempting to read 5\&.0 persisted messages will result in an error on create\&. 
.br
 

.SH "Author"
.PP 
Generated automatically by Doxygen for Paho MQTT C Client Library from the source code\&.
