<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho MQTT C Client Library: Data Fields - Variables</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all variables with links to the structures/unions they belong to:</div>

<h3><a id="index_a" name="index_a"></a>- a -</h3><ul>
<li>array&#160;:&#160;<a class="el" href="struct_m_q_t_t_properties.html#a3ac4c38b423393c1553dcf8b71e7dd58">MQTTProperties</a></li>
</ul>


<h3><a id="index_b" name="index_b"></a>- b -</h3><ul>
<li>binarypwd&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#ae7280d284792990b5d8f6f29d4e0b113">MQTTClient_connectOptions</a></li>
<li>byte&#160;:&#160;<a class="el" href="struct_m_q_t_t_property.html#a1581cde4f73c9a797ae1e7afcc1bb3de">MQTTProperty</a></li>
</ul>


<h3><a id="index_c" name="index_c"></a>- c -</h3><ul>
<li>CApath&#160;:&#160;<a class="el" href="struct_m_q_t_t_client___s_s_l_options.html#a3078b3c824cc9753a57898072445c34d">MQTTClient_SSLOptions</a></li>
<li>cleansession&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">MQTTClient_connectOptions</a></li>
<li>cleanstart&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#acdcb75a5d5981da027bce83849140f7b">MQTTClient_connectOptions</a></li>
<li>connectTimeout&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#a38c6aa24b36d981c49405db425c24db0">MQTTClient_connectOptions</a></li>
<li>context&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__persistence.html#ae376f130b17d169ee51be68077a89ed0">MQTTClient_persistence</a></li>
<li>count&#160;:&#160;<a class="el" href="struct_m_q_t_t_properties.html#ad43c3812e6d13e0518d9f8b8f463ffcf">MQTTProperties</a></li>
</ul>


<h3><a id="index_d" name="index_d"></a>- d -</h3><ul>
<li>data&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#a0d49d74db4c035719c3867723cf7e779">MQTTClient_connectOptions</a>, <a class="el" href="struct_m_q_t_t_client__will_options.html#a0d49d74db4c035719c3867723cf7e779">MQTTClient_willOptions</a>, <a class="el" href="struct_m_q_t_t_len_string.html#a91a70b77df95bd8b0830b49a094c2acb">MQTTLenString</a>, <a class="el" href="struct_m_q_t_t_property.html#aa43ebcb9f97210421431a671384ef159">MQTTProperty</a></li>
<li>disableDefaultTrustStore&#160;:&#160;<a class="el" href="struct_m_q_t_t_client___s_s_l_options.html#a0826fcae7c2816e04772c61542c6846b">MQTTClient_SSLOptions</a></li>
<li>do_openssl_init&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__init__options.html#a5929146596391e2838ef95feb89776da">MQTTClient_init_options</a></li>
<li>dup&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__message.html#adc4cf3f551bb367858644559d69cfdf5">MQTTClient_message</a></li>
</ul>


<h3><a id="index_e" name="index_e"></a>- e -</h3><ul>
<li>enabledCipherSuites&#160;:&#160;<a class="el" href="struct_m_q_t_t_client___s_s_l_options.html#aa683926d52134077f27d6dc67bda13ab">MQTTClient_SSLOptions</a></li>
<li>enableServerCertAuth&#160;:&#160;<a class="el" href="struct_m_q_t_t_client___s_s_l_options.html#a75f6c13b7634e15f96dd9f17db6cf0be">MQTTClient_SSLOptions</a></li>
</ul>


<h3><a id="index_h" name="index_h"></a>- h -</h3><ul>
<li>httpHeaders&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#a018eec60631f40c01e6dcb727bffd33f">MQTTClient_connectOptions</a></li>
<li>httpProxy&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#add124780ab2de397a96780576c2f112c">MQTTClient_connectOptions</a></li>
<li>httpsProxy&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#a388b78d8a75658928238f700f207ad92">MQTTClient_connectOptions</a></li>
</ul>


<h3><a id="index_i" name="index_i"></a>- i -</h3><ul>
<li>identifier&#160;:&#160;<a class="el" href="struct_m_q_t_t_property.html#a2ff04e8cc70fbaa9bcb9a4fb3d510882">MQTTProperty</a></li>
<li>integer2&#160;:&#160;<a class="el" href="struct_m_q_t_t_property.html#a0289ec2e0df8789139386b0ddf5c71c3">MQTTProperty</a></li>
<li>integer4&#160;:&#160;<a class="el" href="struct_m_q_t_t_property.html#a813425ef31abb5ef0091e3043e8a366b">MQTTProperty</a></li>
</ul>


<h3><a id="index_k" name="index_k"></a>- k -</h3><ul>
<li>keepAliveInterval&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#ac8dd0930672a9c7d71fc645aa1f0521d">MQTTClient_connectOptions</a></li>
<li>keyStore&#160;:&#160;<a class="el" href="struct_m_q_t_t_client___s_s_l_options.html#a32b476382955289ce427112b59f21c3e">MQTTClient_SSLOptions</a></li>
</ul>


<h3><a id="index_l" name="index_l"></a>- l -</h3><ul>
<li>len&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#afed088663f8704004425cdae2120b9b3">MQTTClient_connectOptions</a>, <a class="el" href="struct_m_q_t_t_client__will_options.html#afed088663f8704004425cdae2120b9b3">MQTTClient_willOptions</a>, <a class="el" href="struct_m_q_t_t_len_string.html#afed088663f8704004425cdae2120b9b3">MQTTLenString</a></li>
<li>length&#160;:&#160;<a class="el" href="struct_m_q_t_t_properties.html#a9f59b34b1f25fe00023291b678246bcc">MQTTProperties</a></li>
</ul>


<h3><a id="index_m" name="index_m"></a>- m -</h3><ul>
<li>max_count&#160;:&#160;<a class="el" href="struct_m_q_t_t_properties.html#a8de324382d8fd2f5939bf3372e059383">MQTTProperties</a></li>
<li>maxInflightMessages&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#ae3f99bf4663ab7b9e9259feeba41fab2">MQTTClient_connectOptions</a></li>
<li>message&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__will_options.html#a254bf0858da09c96a48daf64404eb4f8">MQTTClient_willOptions</a></li>
<li>MQTTVersion&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTClient_connectOptions</a>, <a class="el" href="struct_m_q_t_t_client__create_options.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTClient_createOptions</a></li>
<li>msgid&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__message.html#a6174c42da8c55c86e7255be2848dc4ac">MQTTClient_message</a></li>
</ul>


<h3><a id="index_n" name="index_n"></a>- n -</h3><ul>
<li>name&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__name_value.html#a8f8f80d37794cde9472343e4487ba3eb">MQTTClient_nameValue</a></li>
<li>noLocal&#160;:&#160;<a class="el" href="struct_m_q_t_t_subscribe__options.html#abbb6a188886c12f305cbe69358515d8b">MQTTSubscribe_options</a></li>
</ul>


<h3><a id="index_p" name="index_p"></a>- p -</h3><ul>
<li>password&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#aa4a2ebcb494493f648ae1e6975672575">MQTTClient_connectOptions</a></li>
<li>payload&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__message.html#a9eff55064941fb604452abb0050ea99d">MQTTClient_message</a>, <a class="el" href="struct_m_q_t_t_client__will_options.html#a0e9356b973a918c25981982fe84e35d7">MQTTClient_willOptions</a></li>
<li>payloadlen&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__message.html#aa3cb44feb3ae6d11b3a4cad2d94cb33a">MQTTClient_message</a></li>
<li>pclear&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__persistence.html#abc192dc88113c7d933b29d3561badbf5">MQTTClient_persistence</a></li>
<li>pclose&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__persistence.html#a7e50506912d2ec0e014cc25ec28fb402">MQTTClient_persistence</a></li>
<li>pcontainskey&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__persistence.html#ac103711576267f791325f2b70b6dc49d">MQTTClient_persistence</a></li>
<li>pget&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__persistence.html#a49155000b82a28ac3b3cb878f3a092d4">MQTTClient_persistence</a></li>
<li>pkeys&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__persistence.html#a407e86a809e4b0b098a8c158f53b9606">MQTTClient_persistence</a></li>
<li>popen&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__persistence.html#a1bae211b32415e6b349d5ae71599f9f4">MQTTClient_persistence</a></li>
<li>pput&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__persistence.html#a4114d9b9971cee18d7e4b9dd5736a608">MQTTClient_persistence</a></li>
<li>premove&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__persistence.html#a53150e443ca721b8623689371c2fbdb9">MQTTClient_persistence</a></li>
<li>privateKey&#160;:&#160;<a class="el" href="struct_m_q_t_t_client___s_s_l_options.html#a7dd436cbb916fba200595c3519f09ec4">MQTTClient_SSLOptions</a></li>
<li>privateKeyPassword&#160;:&#160;<a class="el" href="struct_m_q_t_t_client___s_s_l_options.html#abb427571ba37b51f6985f1a6906ca031">MQTTClient_SSLOptions</a></li>
<li>properties&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__message.html#a1594008402f7307e4de8fa6131656dde">MQTTClient_message</a>, <a class="el" href="struct_m_q_t_t_response.html#a72e9294467b8329a78bc840fe6c5b230">MQTTResponse</a></li>
<li>protos&#160;:&#160;<a class="el" href="struct_m_q_t_t_client___s_s_l_options.html#a4f8661600fb8bacf031150f8dcd293a5">MQTTClient_SSLOptions</a></li>
<li>protos_len&#160;:&#160;<a class="el" href="struct_m_q_t_t_client___s_s_l_options.html#a26f5d839c92f9772c2a5d05486277a42">MQTTClient_SSLOptions</a></li>
</ul>


<h3><a id="index_q" name="index_q"></a>- q -</h3><ul>
<li>qos&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__message.html#a35738099155a0e4f54050da474bab2e7">MQTTClient_message</a>, <a class="el" href="struct_m_q_t_t_client__will_options.html#a35738099155a0e4f54050da474bab2e7">MQTTClient_willOptions</a></li>
</ul>


<h3><a id="index_r" name="index_r"></a>- r -</h3><ul>
<li>reasonCode&#160;:&#160;<a class="el" href="struct_m_q_t_t_response.html#a580d8a8ecb285f5a86c2a3865438f8ee">MQTTResponse</a></li>
<li>reasonCodeCount&#160;:&#160;<a class="el" href="struct_m_q_t_t_response.html#ac97316626bd4faa6b71277c221275f4b">MQTTResponse</a></li>
<li>reasonCodes&#160;:&#160;<a class="el" href="struct_m_q_t_t_response.html#a2199c9d905dbfa279895cf8123c10f4f">MQTTResponse</a></li>
<li>reliable&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#a9f1cdffc99659fd4e2d20e6de3c64df0">MQTTClient_connectOptions</a></li>
<li>retainAsPublished&#160;:&#160;<a class="el" href="struct_m_q_t_t_subscribe__options.html#a8ba074ad218224ee4a8ca802c5e36944">MQTTSubscribe_options</a></li>
<li>retained&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__message.html#a6a4904c112507a43e7dc8495b62cc0fc">MQTTClient_message</a>, <a class="el" href="struct_m_q_t_t_client__will_options.html#a6a4904c112507a43e7dc8495b62cc0fc">MQTTClient_willOptions</a></li>
<li>retainHandling&#160;:&#160;<a class="el" href="struct_m_q_t_t_subscribe__options.html#a11f17b62e40ecdfe107101ae164367a3">MQTTSubscribe_options</a></li>
<li>retryInterval&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#ac73f57846c42bcaa9a47e6721a957748">MQTTClient_connectOptions</a></li>
<li>returned&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#afbca347de18f7a8c57de1f16d3dadde6">MQTTClient_connectOptions</a></li>
</ul>


<h3><a id="index_s" name="index_s"></a>- s -</h3><ul>
<li>serverURI&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#a313446ca7679b36652722ffe53d05228">MQTTClient_connectOptions</a></li>
<li>serverURIcount&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#aa82629005937abd92e97084a428cd61f">MQTTClient_connectOptions</a></li>
<li>serverURIs&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#aba22d81c407fb2ba590dba476240d3e9">MQTTClient_connectOptions</a></li>
<li>sessionPresent&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#a44baf2cb9a0bbcec3ed2eace43f832d1">MQTTClient_connectOptions</a></li>
<li>ssl&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#a8a0b0f0fc7c675312dc232e2458078c7">MQTTClient_connectOptions</a></li>
<li>ssl_error_cb&#160;:&#160;<a class="el" href="struct_m_q_t_t_client___s_s_l_options.html#a21b6ca8a73ba197e65f6a93365d39c04">MQTTClient_SSLOptions</a></li>
<li>ssl_error_context&#160;:&#160;<a class="el" href="struct_m_q_t_t_client___s_s_l_options.html#a189f11195f4d5a70024adffdb050885f">MQTTClient_SSLOptions</a></li>
<li>ssl_psk_cb&#160;:&#160;<a class="el" href="struct_m_q_t_t_client___s_s_l_options.html#a94317cdaf352f9ae496976f8a30f8fee">MQTTClient_SSLOptions</a></li>
<li>ssl_psk_context&#160;:&#160;<a class="el" href="struct_m_q_t_t_client___s_s_l_options.html#ab7f597518dd5b9db5a515081f8e0bd1f">MQTTClient_SSLOptions</a></li>
<li>sslVersion&#160;:&#160;<a class="el" href="struct_m_q_t_t_client___s_s_l_options.html#a3543ea1481b68d73cdde833280bb9c45">MQTTClient_SSLOptions</a></li>
<li>struct_id&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#aa5326df180cb23c59afbcab711a06479">MQTTClient_connectOptions</a>, <a class="el" href="struct_m_q_t_t_client__create_options.html#aa5326df180cb23c59afbcab711a06479">MQTTClient_createOptions</a>, <a class="el" href="struct_m_q_t_t_client__init__options.html#aa5326df180cb23c59afbcab711a06479">MQTTClient_init_options</a>, <a class="el" href="struct_m_q_t_t_client__message.html#aa5326df180cb23c59afbcab711a06479">MQTTClient_message</a>, <a class="el" href="struct_m_q_t_t_client___s_s_l_options.html#aa5326df180cb23c59afbcab711a06479">MQTTClient_SSLOptions</a>, <a class="el" href="struct_m_q_t_t_client__will_options.html#aa5326df180cb23c59afbcab711a06479">MQTTClient_willOptions</a>, <a class="el" href="struct_m_q_t_t_subscribe__options.html#aa5326df180cb23c59afbcab711a06479">MQTTSubscribe_options</a></li>
<li>struct_version&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTClient_connectOptions</a>, <a class="el" href="struct_m_q_t_t_client__create_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTClient_createOptions</a>, <a class="el" href="struct_m_q_t_t_client__init__options.html#a0761a5e5be0383882e42924de8e51f82">MQTTClient_init_options</a>, <a class="el" href="struct_m_q_t_t_client__message.html#a0761a5e5be0383882e42924de8e51f82">MQTTClient_message</a>, <a class="el" href="struct_m_q_t_t_client___s_s_l_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTClient_SSLOptions</a>, <a class="el" href="struct_m_q_t_t_client__will_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTClient_willOptions</a>, <a class="el" href="struct_m_q_t_t_subscribe__options.html#a0761a5e5be0383882e42924de8e51f82">MQTTSubscribe_options</a></li>
</ul>


<h3><a id="index_t" name="index_t"></a>- t -</h3><ul>
<li>topicName&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__will_options.html#a0e20a7b350881d05108d6342884198a5">MQTTClient_willOptions</a></li>
<li>trustStore&#160;:&#160;<a class="el" href="struct_m_q_t_t_client___s_s_l_options.html#a032835d4c4a1c1e19b53c330a673a6e0">MQTTClient_SSLOptions</a></li>
</ul>


<h3><a id="index_u" name="index_u"></a>- u -</h3><ul>
<li>username&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#aba2dfcdfda80edcb531a5a7115d3e043">MQTTClient_connectOptions</a></li>
</ul>


<h3><a id="index_v" name="index_v"></a>- v -</h3><ul>
<li>value&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__name_value.html#a8556878012feffc9e0beb86cd78f424d">MQTTClient_nameValue</a>, <a class="el" href="struct_m_q_t_t_property.html#a09e85ff5ad73824d6c2edc1ce4283a17">MQTTProperty</a></li>
<li>verify&#160;:&#160;<a class="el" href="struct_m_q_t_t_client___s_s_l_options.html#a94900629685d5ed08f66fd2931f573ce">MQTTClient_SSLOptions</a></li>
<li>version&#160;:&#160;<a class="el" href="struct_m_q_t_t_response.html#aad880fc4455c253781e8968f2239d56f">MQTTResponse</a></li>
</ul>


<h3><a id="index_w" name="index_w"></a>- w -</h3><ul>
<li>will&#160;:&#160;<a class="el" href="struct_m_q_t_t_client__connect_options.html#a0a880e99d47eb2efe552abe5079bdc9d">MQTTClient_connectOptions</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:07 for Paho MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
