<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho MQTT C Client Library: MQTTReasonCodes.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">MQTTReasonCodes.h</div></div>
</div><!--header-->
<div class="contents">
<a href="_m_q_t_t_reason_codes_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="comment">/*******************************************************************************</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span><span class="comment"> * Copyright (c) 2017, 2020 IBM Corp. and others</span></div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span><span class="comment"> *</span></div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="comment"> * All rights reserved. This program and the accompanying materials</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span><span class="comment"> * are made available under the terms of the Eclipse Public License v2.0</span></div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span><span class="comment"> * and Eclipse Distribution License v1.0 which accompany this distribution.</span></div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno">    7</span><span class="comment"> *</span></div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span><span class="comment"> * The Eclipse Public License is available at</span></div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno">    9</span><span class="comment"> *    https://www.eclipse.org/legal/epl-2.0/</span></div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno">   10</span><span class="comment"> * and the Eclipse Distribution License is available at</span></div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno">   11</span><span class="comment"> *   http://www.eclipse.org/org/documents/edl-v10.php.</span></div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno">   12</span><span class="comment"> *</span></div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span><span class="comment"> * Contributors:</span></div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno">   14</span><span class="comment"> *    Ian Craggs - initial API and implementation and/or initial documentation</span></div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span><span class="comment"> *******************************************************************************/</span></div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno">   16</span> </div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno">   17</span><span class="preprocessor">#if !defined(MQTTREASONCODES_H)</span></div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno">   18</span><span class="preprocessor">#define MQTTREASONCODES_H</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno">   19</span> </div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno">   20</span><span class="preprocessor">#include &quot;MQTTExportDeclarations.h&quot;</span></div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno">   21</span> </div>
<div class="foldopen" id="foldopen00023" data-start="{" data-end="};">
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">   23</a></span><span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> {</div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969">   24</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969">MQTTREASONCODE_SUCCESS</a> = 0,</div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a3590f41d984646bc58c82734c1516c92">   25</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a3590f41d984646bc58c82734c1516c92">MQTTREASONCODE_NORMAL_DISCONNECTION</a> = 0,</div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a3fd0d12c0e44b4df9f716aef89b61aff">   26</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a3fd0d12c0e44b4df9f716aef89b61aff">MQTTREASONCODE_GRANTED_QOS_0</a> = 0,</div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a07578b30b2d72af2eeea6be268475876">   27</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a07578b30b2d72af2eeea6be268475876">MQTTREASONCODE_GRANTED_QOS_1</a> = 1,</div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a74ac34a39a849c9c369b18545a4b1f93">   28</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a74ac34a39a849c9c369b18545a4b1f93">MQTTREASONCODE_GRANTED_QOS_2</a> = 2,</div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a55f533a6cc98417d08dac8cc69da0ed3">   29</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a55f533a6cc98417d08dac8cc69da0ed3">MQTTREASONCODE_DISCONNECT_WITH_WILL_MESSAGE</a> = 4,</div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1720d8b04af4c0d92e27b378d735e899">   30</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1720d8b04af4c0d92e27b378d735e899">MQTTREASONCODE_NO_MATCHING_SUBSCRIBERS</a> = 16,</div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a55208c34a26f67e112d53c54be37acb9">   31</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a55208c34a26f67e112d53c54be37acb9">MQTTREASONCODE_NO_SUBSCRIPTION_FOUND</a> = 17,</div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a0c0726c0e87eaddd636708497c69d055">   32</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a0c0726c0e87eaddd636708497c69d055">MQTTREASONCODE_CONTINUE_AUTHENTICATION</a> = 24,</div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6cc1b342856c1d96d54c368148b536f7">   33</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6cc1b342856c1d96d54c368148b536f7">MQTTREASONCODE_RE_AUTHENTICATE</a> = 25,</div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1881ee597bfef9157f0034a1377328e3">   34</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1881ee597bfef9157f0034a1377328e3">MQTTREASONCODE_UNSPECIFIED_ERROR</a> = 128,</div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a2cbee3502c00d304bf1091195457fcf5">   35</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a2cbee3502c00d304bf1091195457fcf5">MQTTREASONCODE_MALFORMED_PACKET</a> = 129,</div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ae0dad403f352e31449764e2ac94c7756">   36</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ae0dad403f352e31449764e2ac94c7756">MQTTREASONCODE_PROTOCOL_ERROR</a> = 130,</div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a41629fa453cdf14ef6a5370a16d5a19c">   37</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a41629fa453cdf14ef6a5370a16d5a19c">MQTTREASONCODE_IMPLEMENTATION_SPECIFIC_ERROR</a> = 131,</div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a021ceca20e6d35279075a2b93ece973d">   38</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a021ceca20e6d35279075a2b93ece973d">MQTTREASONCODE_UNSUPPORTED_PROTOCOL_VERSION</a> = 132,</div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ab58bb236e7dbd000a56c590c01bc73fd">   39</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ab58bb236e7dbd000a56c590c01bc73fd">MQTTREASONCODE_CLIENT_IDENTIFIER_NOT_VALID</a> = 133,</div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279abfc617112d5856722108912c5c6633ff">   40</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279abfc617112d5856722108912c5c6633ff">MQTTREASONCODE_BAD_USER_NAME_OR_PASSWORD</a> = 134,</div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a91a14fc763349cf4a7047d24f13d0803">   41</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a91a14fc763349cf4a7047d24f13d0803">MQTTREASONCODE_NOT_AUTHORIZED</a> = 135,</div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a0cfd4de78870b3fb0499b916d06d40bb">   42</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a0cfd4de78870b3fb0499b916d06d40bb">MQTTREASONCODE_SERVER_UNAVAILABLE</a> = 136,</div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af507e75147b0b34f36955c9f62389a74">   43</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af507e75147b0b34f36955c9f62389a74">MQTTREASONCODE_SERVER_BUSY</a> = 137,</div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ab4cf7578f0078293fa66a4cd5e5d4aa4">   44</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ab4cf7578f0078293fa66a4cd5e5d4aa4">MQTTREASONCODE_BANNED</a> = 138,</div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a085e1572ffce61838807b7429b691113">   45</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a085e1572ffce61838807b7429b691113">MQTTREASONCODE_SERVER_SHUTTING_DOWN</a> = 139,</div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af62e569703d7a7f0acffaa59522b9dc3">   46</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af62e569703d7a7f0acffaa59522b9dc3">MQTTREASONCODE_BAD_AUTHENTICATION_METHOD</a> = 140,</div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af21a6c320e34993d7aa169330ab23409">   47</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af21a6c320e34993d7aa169330ab23409">MQTTREASONCODE_KEEP_ALIVE_TIMEOUT</a> = 141,</div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ad15ffa6884f97976e237afafcbccea21">   48</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ad15ffa6884f97976e237afafcbccea21">MQTTREASONCODE_SESSION_TAKEN_OVER</a> = 142,</div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a00319b171f469824dd6938cbd0212b5b">   49</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a00319b171f469824dd6938cbd0212b5b">MQTTREASONCODE_TOPIC_FILTER_INVALID</a> = 143,</div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6268968177868576f6b9239aa9afd8ac">   50</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6268968177868576f6b9239aa9afd8ac">MQTTREASONCODE_TOPIC_NAME_INVALID</a> = 144,</div>
<div class="line"><a id="l00051" name="l00051"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279adaee01dbc97a0773b5032a29c797613a">   51</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279adaee01dbc97a0773b5032a29c797613a">MQTTREASONCODE_PACKET_IDENTIFIER_IN_USE</a> = 145,</div>
<div class="line"><a id="l00052" name="l00052"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a4908a8293054f8ff8d6c47fe0cf31932">   52</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a4908a8293054f8ff8d6c47fe0cf31932">MQTTREASONCODE_PACKET_IDENTIFIER_NOT_FOUND</a> = 146,</div>
<div class="line"><a id="l00053" name="l00053"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a45afaacbefd2d816fddf9fe9804b61d1">   53</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a45afaacbefd2d816fddf9fe9804b61d1">MQTTREASONCODE_RECEIVE_MAXIMUM_EXCEEDED</a> = 147,</div>
<div class="line"><a id="l00054" name="l00054"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a8e0fcdd051e154e319058600b58652ec">   54</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a8e0fcdd051e154e319058600b58652ec">MQTTREASONCODE_TOPIC_ALIAS_INVALID</a> = 148,</div>
<div class="line"><a id="l00055" name="l00055"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a11a587e15c468bf1c6ba9df7e8fd78aa">   55</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a11a587e15c468bf1c6ba9df7e8fd78aa">MQTTREASONCODE_PACKET_TOO_LARGE</a> = 149,</div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af76d0e32fb44fa94e407b1af5dc7aa4e">   56</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af76d0e32fb44fa94e407b1af5dc7aa4e">MQTTREASONCODE_MESSAGE_RATE_TOO_HIGH</a> = 150,</div>
<div class="line"><a id="l00057" name="l00057"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a954fcabf6e88925b2a57bcd84032d9f9">   57</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a954fcabf6e88925b2a57bcd84032d9f9">MQTTREASONCODE_QUOTA_EXCEEDED</a> = 151,</div>
<div class="line"><a id="l00058" name="l00058"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ae1e3b428072be26d2cbf6f88361f76cc">   58</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ae1e3b428072be26d2cbf6f88361f76cc">MQTTREASONCODE_ADMINISTRATIVE_ACTION</a> = 152,</div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a2d629400116e1723c5e2e597bbfe29ca">   59</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a2d629400116e1723c5e2e597bbfe29ca">MQTTREASONCODE_PAYLOAD_FORMAT_INVALID</a> = 153,</div>
<div class="line"><a id="l00060" name="l00060"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279aa4378012148d98599398bc4a3480c38f">   60</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279aa4378012148d98599398bc4a3480c38f">MQTTREASONCODE_RETAIN_NOT_SUPPORTED</a> = 154,</div>
<div class="line"><a id="l00061" name="l00061"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a83865a2440b512e5602152521e3810bb">   61</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a83865a2440b512e5602152521e3810bb">MQTTREASONCODE_QOS_NOT_SUPPORTED</a> = 155,</div>
<div class="line"><a id="l00062" name="l00062"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279aabaee4062c4e4941b9eed59f09e9440c">   62</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279aabaee4062c4e4941b9eed59f09e9440c">MQTTREASONCODE_USE_ANOTHER_SERVER</a> = 156,</div>
<div class="line"><a id="l00063" name="l00063"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a783254c7acf8de52ee345bc176f9d6c0">   63</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a783254c7acf8de52ee345bc176f9d6c0">MQTTREASONCODE_SERVER_MOVED</a> = 157,</div>
<div class="line"><a id="l00064" name="l00064"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1c694648e36a40162939a2785450b6bd">   64</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1c694648e36a40162939a2785450b6bd">MQTTREASONCODE_SHARED_SUBSCRIPTIONS_NOT_SUPPORTED</a> = 158,</div>
<div class="line"><a id="l00065" name="l00065"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a879c56ed34fa2dd6492e7a34a9747bc1">   65</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a879c56ed34fa2dd6492e7a34a9747bc1">MQTTREASONCODE_CONNECTION_RATE_EXCEEDED</a> = 159,</div>
<div class="line"><a id="l00066" name="l00066"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6f07c3b42690afc7b117321dc4e2657f">   66</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6f07c3b42690afc7b117321dc4e2657f">MQTTREASONCODE_MAXIMUM_CONNECT_TIME</a> = 160,</div>
<div class="line"><a id="l00067" name="l00067"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a7bcd0f9b21c398a217667aebb4107842">   67</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a7bcd0f9b21c398a217667aebb4107842">MQTTREASONCODE_SUBSCRIPTION_IDENTIFIERS_NOT_SUPPORTED</a> = 161,</div>
<div class="line"><a id="l00068" name="l00068"></a><span class="lineno">   68</span>  <a class="code hl_enumvalue" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a81b5708f676f52594b680f085e444e1f">MQTTREASONCODE_WILDCARD_SUBSCRIPTIONS_NOT_SUPPORTED</a> = 162</div>
<div class="line"><a id="l00069" name="l00069"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a81b5708f676f52594b680f085e444e1f">   69</a></span>};</div>
</div>
<div class="line"><a id="l00070" name="l00070"></a><span class="lineno">   70</span> </div>
<div class="line"><a id="l00077" name="l00077"></a><span class="lineno"><a class="line" href="_m_q_t_t_reason_codes_8h.html#a385737b840eb180f35b9a714ea295ceb">   77</a></span>LIBMQTT_API <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_function" href="_m_q_t_t_reason_codes_8h.html#a385737b840eb180f35b9a714ea295ceb">MQTTReasonCode_toString</a>(<span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> value);</div>
<div class="line"><a id="l00078" name="l00078"></a><span class="lineno">   78</span> </div>
<div class="line"><a id="l00079" name="l00079"></a><span class="lineno">   79</span><span class="preprocessor">#endif</span></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_a385737b840eb180f35b9a714ea295ceb"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#a385737b840eb180f35b9a714ea295ceb">MQTTReasonCode_toString</a></div><div class="ttdeci">const char * MQTTReasonCode_toString(enum MQTTReasonCodes value)</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a></div><div class="ttdeci">MQTTReasonCodes</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:23</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a00319b171f469824dd6938cbd0212b5b"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a00319b171f469824dd6938cbd0212b5b">MQTTREASONCODE_TOPIC_FILTER_INVALID</a></div><div class="ttdeci">@ MQTTREASONCODE_TOPIC_FILTER_INVALID</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:49</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a021ceca20e6d35279075a2b93ece973d"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a021ceca20e6d35279075a2b93ece973d">MQTTREASONCODE_UNSUPPORTED_PROTOCOL_VERSION</a></div><div class="ttdeci">@ MQTTREASONCODE_UNSUPPORTED_PROTOCOL_VERSION</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:38</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a07578b30b2d72af2eeea6be268475876"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a07578b30b2d72af2eeea6be268475876">MQTTREASONCODE_GRANTED_QOS_1</a></div><div class="ttdeci">@ MQTTREASONCODE_GRANTED_QOS_1</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:27</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a085e1572ffce61838807b7429b691113"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a085e1572ffce61838807b7429b691113">MQTTREASONCODE_SERVER_SHUTTING_DOWN</a></div><div class="ttdeci">@ MQTTREASONCODE_SERVER_SHUTTING_DOWN</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:45</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a0c0726c0e87eaddd636708497c69d055"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a0c0726c0e87eaddd636708497c69d055">MQTTREASONCODE_CONTINUE_AUTHENTICATION</a></div><div class="ttdeci">@ MQTTREASONCODE_CONTINUE_AUTHENTICATION</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:32</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a0cfd4de78870b3fb0499b916d06d40bb"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a0cfd4de78870b3fb0499b916d06d40bb">MQTTREASONCODE_SERVER_UNAVAILABLE</a></div><div class="ttdeci">@ MQTTREASONCODE_SERVER_UNAVAILABLE</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:42</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a11a587e15c468bf1c6ba9df7e8fd78aa"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a11a587e15c468bf1c6ba9df7e8fd78aa">MQTTREASONCODE_PACKET_TOO_LARGE</a></div><div class="ttdeci">@ MQTTREASONCODE_PACKET_TOO_LARGE</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:55</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a1720d8b04af4c0d92e27b378d735e899"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1720d8b04af4c0d92e27b378d735e899">MQTTREASONCODE_NO_MATCHING_SUBSCRIBERS</a></div><div class="ttdeci">@ MQTTREASONCODE_NO_MATCHING_SUBSCRIBERS</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:30</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a1881ee597bfef9157f0034a1377328e3"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1881ee597bfef9157f0034a1377328e3">MQTTREASONCODE_UNSPECIFIED_ERROR</a></div><div class="ttdeci">@ MQTTREASONCODE_UNSPECIFIED_ERROR</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:34</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a1c694648e36a40162939a2785450b6bd"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1c694648e36a40162939a2785450b6bd">MQTTREASONCODE_SHARED_SUBSCRIPTIONS_NOT_SUPPORTED</a></div><div class="ttdeci">@ MQTTREASONCODE_SHARED_SUBSCRIPTIONS_NOT_SUPPORTED</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:64</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a2cbee3502c00d304bf1091195457fcf5"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a2cbee3502c00d304bf1091195457fcf5">MQTTREASONCODE_MALFORMED_PACKET</a></div><div class="ttdeci">@ MQTTREASONCODE_MALFORMED_PACKET</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:35</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a2d629400116e1723c5e2e597bbfe29ca"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a2d629400116e1723c5e2e597bbfe29ca">MQTTREASONCODE_PAYLOAD_FORMAT_INVALID</a></div><div class="ttdeci">@ MQTTREASONCODE_PAYLOAD_FORMAT_INVALID</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:59</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a3590f41d984646bc58c82734c1516c92"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a3590f41d984646bc58c82734c1516c92">MQTTREASONCODE_NORMAL_DISCONNECTION</a></div><div class="ttdeci">@ MQTTREASONCODE_NORMAL_DISCONNECTION</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:25</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a3fd0d12c0e44b4df9f716aef89b61aff"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a3fd0d12c0e44b4df9f716aef89b61aff">MQTTREASONCODE_GRANTED_QOS_0</a></div><div class="ttdeci">@ MQTTREASONCODE_GRANTED_QOS_0</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:26</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a41629fa453cdf14ef6a5370a16d5a19c"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a41629fa453cdf14ef6a5370a16d5a19c">MQTTREASONCODE_IMPLEMENTATION_SPECIFIC_ERROR</a></div><div class="ttdeci">@ MQTTREASONCODE_IMPLEMENTATION_SPECIFIC_ERROR</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:37</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a45afaacbefd2d816fddf9fe9804b61d1"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a45afaacbefd2d816fddf9fe9804b61d1">MQTTREASONCODE_RECEIVE_MAXIMUM_EXCEEDED</a></div><div class="ttdeci">@ MQTTREASONCODE_RECEIVE_MAXIMUM_EXCEEDED</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:53</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a4908a8293054f8ff8d6c47fe0cf31932"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a4908a8293054f8ff8d6c47fe0cf31932">MQTTREASONCODE_PACKET_IDENTIFIER_NOT_FOUND</a></div><div class="ttdeci">@ MQTTREASONCODE_PACKET_IDENTIFIER_NOT_FOUND</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:52</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a55208c34a26f67e112d53c54be37acb9"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a55208c34a26f67e112d53c54be37acb9">MQTTREASONCODE_NO_SUBSCRIPTION_FOUND</a></div><div class="ttdeci">@ MQTTREASONCODE_NO_SUBSCRIPTION_FOUND</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:31</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a55f533a6cc98417d08dac8cc69da0ed3"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a55f533a6cc98417d08dac8cc69da0ed3">MQTTREASONCODE_DISCONNECT_WITH_WILL_MESSAGE</a></div><div class="ttdeci">@ MQTTREASONCODE_DISCONNECT_WITH_WILL_MESSAGE</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:29</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a6268968177868576f6b9239aa9afd8ac"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6268968177868576f6b9239aa9afd8ac">MQTTREASONCODE_TOPIC_NAME_INVALID</a></div><div class="ttdeci">@ MQTTREASONCODE_TOPIC_NAME_INVALID</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:50</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969">MQTTREASONCODE_SUCCESS</a></div><div class="ttdeci">@ MQTTREASONCODE_SUCCESS</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:24</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a6cc1b342856c1d96d54c368148b536f7"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6cc1b342856c1d96d54c368148b536f7">MQTTREASONCODE_RE_AUTHENTICATE</a></div><div class="ttdeci">@ MQTTREASONCODE_RE_AUTHENTICATE</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:33</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a6f07c3b42690afc7b117321dc4e2657f"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6f07c3b42690afc7b117321dc4e2657f">MQTTREASONCODE_MAXIMUM_CONNECT_TIME</a></div><div class="ttdeci">@ MQTTREASONCODE_MAXIMUM_CONNECT_TIME</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:66</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a74ac34a39a849c9c369b18545a4b1f93"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a74ac34a39a849c9c369b18545a4b1f93">MQTTREASONCODE_GRANTED_QOS_2</a></div><div class="ttdeci">@ MQTTREASONCODE_GRANTED_QOS_2</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:28</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a783254c7acf8de52ee345bc176f9d6c0"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a783254c7acf8de52ee345bc176f9d6c0">MQTTREASONCODE_SERVER_MOVED</a></div><div class="ttdeci">@ MQTTREASONCODE_SERVER_MOVED</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:63</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a7bcd0f9b21c398a217667aebb4107842"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a7bcd0f9b21c398a217667aebb4107842">MQTTREASONCODE_SUBSCRIPTION_IDENTIFIERS_NOT_SUPPORTED</a></div><div class="ttdeci">@ MQTTREASONCODE_SUBSCRIPTION_IDENTIFIERS_NOT_SUPPORTED</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:67</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a81b5708f676f52594b680f085e444e1f"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a81b5708f676f52594b680f085e444e1f">MQTTREASONCODE_WILDCARD_SUBSCRIPTIONS_NOT_SUPPORTED</a></div><div class="ttdeci">@ MQTTREASONCODE_WILDCARD_SUBSCRIPTIONS_NOT_SUPPORTED</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:68</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a83865a2440b512e5602152521e3810bb"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a83865a2440b512e5602152521e3810bb">MQTTREASONCODE_QOS_NOT_SUPPORTED</a></div><div class="ttdeci">@ MQTTREASONCODE_QOS_NOT_SUPPORTED</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:61</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a879c56ed34fa2dd6492e7a34a9747bc1"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a879c56ed34fa2dd6492e7a34a9747bc1">MQTTREASONCODE_CONNECTION_RATE_EXCEEDED</a></div><div class="ttdeci">@ MQTTREASONCODE_CONNECTION_RATE_EXCEEDED</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:65</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a8e0fcdd051e154e319058600b58652ec"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a8e0fcdd051e154e319058600b58652ec">MQTTREASONCODE_TOPIC_ALIAS_INVALID</a></div><div class="ttdeci">@ MQTTREASONCODE_TOPIC_ALIAS_INVALID</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:54</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a91a14fc763349cf4a7047d24f13d0803"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a91a14fc763349cf4a7047d24f13d0803">MQTTREASONCODE_NOT_AUTHORIZED</a></div><div class="ttdeci">@ MQTTREASONCODE_NOT_AUTHORIZED</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:41</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a954fcabf6e88925b2a57bcd84032d9f9"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a954fcabf6e88925b2a57bcd84032d9f9">MQTTREASONCODE_QUOTA_EXCEEDED</a></div><div class="ttdeci">@ MQTTREASONCODE_QUOTA_EXCEEDED</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:57</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279aa4378012148d98599398bc4a3480c38f"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279aa4378012148d98599398bc4a3480c38f">MQTTREASONCODE_RETAIN_NOT_SUPPORTED</a></div><div class="ttdeci">@ MQTTREASONCODE_RETAIN_NOT_SUPPORTED</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:60</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279aabaee4062c4e4941b9eed59f09e9440c"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279aabaee4062c4e4941b9eed59f09e9440c">MQTTREASONCODE_USE_ANOTHER_SERVER</a></div><div class="ttdeci">@ MQTTREASONCODE_USE_ANOTHER_SERVER</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:62</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279ab4cf7578f0078293fa66a4cd5e5d4aa4"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ab4cf7578f0078293fa66a4cd5e5d4aa4">MQTTREASONCODE_BANNED</a></div><div class="ttdeci">@ MQTTREASONCODE_BANNED</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:44</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279ab58bb236e7dbd000a56c590c01bc73fd"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ab58bb236e7dbd000a56c590c01bc73fd">MQTTREASONCODE_CLIENT_IDENTIFIER_NOT_VALID</a></div><div class="ttdeci">@ MQTTREASONCODE_CLIENT_IDENTIFIER_NOT_VALID</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:39</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279abfc617112d5856722108912c5c6633ff"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279abfc617112d5856722108912c5c6633ff">MQTTREASONCODE_BAD_USER_NAME_OR_PASSWORD</a></div><div class="ttdeci">@ MQTTREASONCODE_BAD_USER_NAME_OR_PASSWORD</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:40</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279ad15ffa6884f97976e237afafcbccea21"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ad15ffa6884f97976e237afafcbccea21">MQTTREASONCODE_SESSION_TAKEN_OVER</a></div><div class="ttdeci">@ MQTTREASONCODE_SESSION_TAKEN_OVER</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:48</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279adaee01dbc97a0773b5032a29c797613a"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279adaee01dbc97a0773b5032a29c797613a">MQTTREASONCODE_PACKET_IDENTIFIER_IN_USE</a></div><div class="ttdeci">@ MQTTREASONCODE_PACKET_IDENTIFIER_IN_USE</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:51</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279ae0dad403f352e31449764e2ac94c7756"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ae0dad403f352e31449764e2ac94c7756">MQTTREASONCODE_PROTOCOL_ERROR</a></div><div class="ttdeci">@ MQTTREASONCODE_PROTOCOL_ERROR</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:36</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279ae1e3b428072be26d2cbf6f88361f76cc"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ae1e3b428072be26d2cbf6f88361f76cc">MQTTREASONCODE_ADMINISTRATIVE_ACTION</a></div><div class="ttdeci">@ MQTTREASONCODE_ADMINISTRATIVE_ACTION</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:58</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279af21a6c320e34993d7aa169330ab23409"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af21a6c320e34993d7aa169330ab23409">MQTTREASONCODE_KEEP_ALIVE_TIMEOUT</a></div><div class="ttdeci">@ MQTTREASONCODE_KEEP_ALIVE_TIMEOUT</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:47</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279af507e75147b0b34f36955c9f62389a74"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af507e75147b0b34f36955c9f62389a74">MQTTREASONCODE_SERVER_BUSY</a></div><div class="ttdeci">@ MQTTREASONCODE_SERVER_BUSY</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:43</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279af62e569703d7a7f0acffaa59522b9dc3"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af62e569703d7a7f0acffaa59522b9dc3">MQTTREASONCODE_BAD_AUTHENTICATION_METHOD</a></div><div class="ttdeci">@ MQTTREASONCODE_BAD_AUTHENTICATION_METHOD</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:46</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279af76d0e32fb44fa94e407b1af5dc7aa4e"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af76d0e32fb44fa94e407b1af5dc7aa4e">MQTTREASONCODE_MESSAGE_RATE_TOO_HIGH</a></div><div class="ttdeci">@ MQTTREASONCODE_MESSAGE_RATE_TOO_HIGH</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:56</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:06 for Paho MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
