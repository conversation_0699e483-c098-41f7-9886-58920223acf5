<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho MQTT C Client Library: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all functions with links to the files they belong to:</div>

<h3><a id="index_m" name="index_m"></a>- m -</h3><ul>
<li>MQTTClient_connect()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aaa8ae61cd65c9dc0846df10122d7bd4e">MQTTClient.h</a></li>
<li>MQTTClient_connect5()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aa777f80cb3eec5610f976aff30b8c0d6">MQTTClient.h</a></li>
<li>MQTTClient_create()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient.h</a></li>
<li>MQTTClient_createWithOptions()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#ade24f717a9b39d38b081e1d5e0db1661">MQTTClient.h</a></li>
<li>MQTTClient_destroy()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#ae700c3f5cfea3813264ce95e7c8cf498">MQTTClient.h</a></li>
<li>MQTTClient_disconnect()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a1e4d90c13a3c0705bc4a13bfe64e6525">MQTTClient.h</a></li>
<li>MQTTClient_disconnect5()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a1762c469715b7f718c4e63a427e6c13c">MQTTClient.h</a></li>
<li>MQTTClient_free()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a203b545c999beb6b825ec99b6aea79ab">MQTTClient.h</a></li>
<li>MQTTClient_freeMessage()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#abd8abde4f39d3e689029de27f7a98a65">MQTTClient.h</a></li>
<li>MQTTClient_getPendingDeliveryTokens()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a2a617c6b0492c04a4ddea592f5e53604">MQTTClient.h</a></li>
<li>MQTTClient_getVersionInfo()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aef6eba956a5ff6072854a9e353487087">MQTTClient.h</a></li>
<li>MQTTClient_global_init()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a21804ede1a506d1d69a472bc30acc8ba">MQTTClient.h</a></li>
<li>MQTTClient_isConnected()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a6e8231e8c47f6f67f7ebbb5dcb4c69c0">MQTTClient.h</a></li>
<li>MQTTClient_malloc()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a1f3ae01af021b014df147c9996156a69">MQTTClient.h</a></li>
<li>MQTTClient_publish()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#afe9c34013c3511b8ef6cd36bf703678d">MQTTClient.h</a></li>
<li>MQTTClient_publish5()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a8148186cc7683a6bb57f621653df51df">MQTTClient.h</a></li>
<li>MQTTClient_publishMessage()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#ace320b8a92c7087d9dd5cf242d50389d">MQTTClient.h</a></li>
<li>MQTTClient_publishMessage5()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a362042ce973c012bad6a1aa3b5984f5d">MQTTClient.h</a></li>
<li>MQTTClient_receive()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a4c2df88d00a3dadd510a8cb774739366">MQTTClient.h</a></li>
<li>MQTTClient_setCallbacks()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aad27d07782991a4937ebf2f39a021f83">MQTTClient.h</a></li>
<li>MQTTClient_setCommandTimeout()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a96067a2fb74d2a61c7e93015629548e0">MQTTClient.h</a></li>
<li>MQTTClient_setDisconnected()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a8adea083a162735d5c7592160088eea0">MQTTClient.h</a></li>
<li>MQTTClient_setPublished()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a9f13911351a3de6b1ebdabd4cb4116ba">MQTTClient.h</a></li>
<li>MQTTClient_setTraceCallback()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a22870f94aa4cb1827626612f1ded7c69">MQTTClient.h</a></li>
<li>MQTTClient_setTraceLevel()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a4dfa35d29db54b10b15b8ac2d9a778be">MQTTClient.h</a></li>
<li>MQTTClient_strerror()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a68535b4c6d8f28b29a52569926cdeb50">MQTTClient.h</a></li>
<li>MQTTClient_subscribe()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a9c1c28258f0d5c6a44ff53a98618f5f3">MQTTClient.h</a></li>
<li>MQTTClient_subscribe5()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#af35ab7375435f7b6388c5ff4610dad3d">MQTTClient.h</a></li>
<li>MQTTClient_subscribeMany()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a92fa1c13f3db8399e042fbdbdfb692b3">MQTTClient.h</a></li>
<li>MQTTClient_subscribeMany5()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a5390c2402f135c12826ffbf6fc261f7c">MQTTClient.h</a></li>
<li>MQTTClient_unsubscribe()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aa8731be3dbc6a25f41f037f8bbbb054b">MQTTClient.h</a></li>
<li>MQTTClient_unsubscribe5()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a58356c13867f18df60fd4c7ec9457c48">MQTTClient.h</a></li>
<li>MQTTClient_unsubscribeMany()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a50abbce720d50b9f84b97ff9fa1f546d">MQTTClient.h</a></li>
<li>MQTTClient_unsubscribeMany5()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a46bdb532d2153110ccffb2f0748d1ba5">MQTTClient.h</a></li>
<li>MQTTClient_waitForCompletion()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a83807ec81fe8c3941e368ab329d43067">MQTTClient.h</a></li>
<li>MQTTClient_yield()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a8ad3d29864a9ca08202b0832e0f6678e">MQTTClient.h</a></li>
<li>MQTTProperties_add()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a88f1d21556c2d23330d71357cd226a15">MQTTProperties.h</a></li>
<li>MQTTProperties_copy()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a69b3e474ee2f828e5b827d615fe0fe72">MQTTProperties.h</a></li>
<li>MQTTProperties_free()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#ab68247ed365ee51170a9309c828b1823">MQTTProperties.h</a></li>
<li>MQTTProperties_getNumericValue()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#ad8643f0e68deb29e16ef88fc225e03c2">MQTTProperties.h</a></li>
<li>MQTTProperties_getNumericValueAt()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a1d4f3fd86fc47241fefe623556d99ea9">MQTTProperties.h</a></li>
<li>MQTTProperties_getProperty()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#ac5c49930de80af1e0df0f0584f142078">MQTTProperties.h</a></li>
<li>MQTTProperties_getPropertyAt()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a345b64fc33afd05148aa018c24c42c80">MQTTProperties.h</a></li>
<li>MQTTProperties_hasProperty()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#abde30a2a44f41c649bd84f4d1467b72c">MQTTProperties.h</a></li>
<li>MQTTProperties_len()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a2850f38d4ff89af52999fdc42cdff6fa">MQTTProperties.h</a></li>
<li>MQTTProperties_propertyCount()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#ac7ea96a57ad09e9d0fc3203d008f52aa">MQTTProperties.h</a></li>
<li>MQTTProperties_read()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#afcb874dfcc9f0eaa0b063e2fad740871">MQTTProperties.h</a></li>
<li>MQTTProperties_write()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#ade0027a4e571bd288fe40271ff7aa497">MQTTProperties.h</a></li>
<li>MQTTProperty_getType()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a7d30ad0520bc9b9366e700d4b493b173">MQTTProperties.h</a></li>
<li>MQTTPropertyName()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a69d277e6a0f27a05279eca2736e09840">MQTTProperties.h</a></li>
<li>MQTTReasonCode_toString()&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#a385737b840eb180f35b9a714ea295ceb">MQTTReasonCodes.h</a></li>
<li>MQTTResponse_free()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a01bd2c5f98ec5c0636a106db33f2b01b">MQTTClient.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:07 for Paho MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
