<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho MQTT C Client Library: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all macros with links to the files they belong to:</div>

<h3><a id="index_m" name="index_m"></a>- m -</h3><ul>
<li>MQTT_BAD_SUBSCRIBE&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#ade337b363b7f4bc7c1a7b2858e0380bd">MQTTClient.h</a></li>
<li>MQTT_INVALID_PROPERTY_ID&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#afc56d2e8937a0c8f180d68ad93945945">MQTTProperties.h</a></li>
<li>MQTT_SSL_VERSION_DEFAULT&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a2549ea897af26c76198284731db9e721">MQTTClient.h</a></li>
<li>MQTT_SSL_VERSION_TLS_1_0&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a7e5da3d6f0d2b53409bbfcf6e56f3d2d">MQTTClient.h</a></li>
<li>MQTT_SSL_VERSION_TLS_1_1&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#abdff87efa3f2ee473a1591e10638b537">MQTTClient.h</a></li>
<li>MQTT_SSL_VERSION_TLS_1_2&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a3a94dbdeafbb73c73a068e7c2085fbab">MQTTClient.h</a></li>
<li>MQTTCLIENT_0_LEN_WILL_TOPIC&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aacf90ba5292e25122e6fd5ec2a38efe5">MQTTClient.h</a></li>
<li>MQTTCLIENT_BAD_MQTT_OPTION&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a1babaca56ffae802fa1e246a2649927e">MQTTClient.h</a></li>
<li>MQTTCLIENT_BAD_MQTT_VERSION&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aab84cecd25638896eb45b8f5ffd82bf7">MQTTClient.h</a></li>
<li>MQTTCLIENT_BAD_PROTOCOL&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a1d0cb25b450136f036a238546487344a">MQTTClient.h</a></li>
<li>MQTTCLIENT_BAD_QOS&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a51cc8ca032acf4ae14f83996524b8cdc">MQTTClient.h</a></li>
<li>MQTTCLIENT_BAD_STRUCTURE&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a747615d8064e3fe024ae5565ec63e1ce">MQTTClient.h</a></li>
<li>MQTTCLIENT_BAD_UTF8_STRING&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a678a4744192de9c8dca220d9965809dd">MQTTClient.h</a></li>
<li>MQTTClient_connectOptions_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aefd7c865f2641c8155b763fdf3061c25">MQTTClient.h</a></li>
<li>MQTTClient_connectOptions_initializer5&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a1f0c7608262ac9c00cb94e9c8f9fc984">MQTTClient.h</a></li>
<li>MQTTClient_connectOptions_initializer5_ws&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a8c37c9f77f0b67e2520c8f91acf1afea">MQTTClient.h</a></li>
<li>MQTTClient_connectOptions_initializer_ws&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a0a98fda162a78ee8c8cbd7d9d39494f4">MQTTClient.h</a></li>
<li>MQTTClient_createOptions_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a763e477a5aceb6aff279111c7693e691">MQTTClient.h</a></li>
<li>MQTTCLIENT_DISCONNECTED&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a561d053311cb492cf7226f419ee0d516">MQTTClient.h</a></li>
<li>MQTTCLIENT_FAILURE&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#af33a6d6c0e8a6a747bf39638e0bba36b">MQTTClient.h</a></li>
<li>MQTTClient_init_options_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#ac17057c8c22c0717d3adf4e040440f73">MQTTClient.h</a></li>
<li>MQTTCLIENT_MAX_MESSAGES_INFLIGHT&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a8fc442fc2e9dfb422a163ab1fa02e0cb">MQTTClient.h</a></li>
<li>MQTTClient_message_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aa1fd995924d3df75959fcf57e87aefac">MQTTClient.h</a></li>
<li>MQTTCLIENT_NULL_PARAMETER&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#ac3232abd7f86bbba26faea0e2b132c3c">MQTTClient.h</a></li>
<li>MQTTCLIENT_PERSISTENCE_DEFAULT&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#aaa948291718a9c06369b854b0f64bc32">MQTTClientPersistence.h</a></li>
<li>MQTTCLIENT_PERSISTENCE_ERROR&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#ab716e21e53c84a5ad62aa962a2a8f7db">MQTTClientPersistence.h</a></li>
<li>MQTTCLIENT_PERSISTENCE_NONE&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#ae01e089313a65ac4661ed216b6ac00fa">MQTTClientPersistence.h</a></li>
<li>MQTTCLIENT_PERSISTENCE_USER&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#a5dc68b8616e4041e037bad94ce07681b">MQTTClientPersistence.h</a></li>
<li>MQTTCLIENT_SSL_NOT_SUPPORTED&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a1c67fc83ba1a8f26236aa49b127bdb61">MQTTClient.h</a></li>
<li>MQTTClient_SSLOptions_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#ab9b2a2c6b52dbb2ac842ad99a9ce6d99">MQTTClient.h</a></li>
<li>MQTTCLIENT_SUCCESS&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#acba095704d79e5a1996389fa26203f73">MQTTClient.h</a></li>
<li>MQTTCLIENT_TOPICNAME_TRUNCATED&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a29afebfce0bdf6cda1e37abc0c4b6690">MQTTClient.h</a></li>
<li>MQTTClient_willOptions_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aae0811659c59f5dad0467544f91645eb">MQTTClient.h</a></li>
<li>MQTTCLIENT_WRONG_MQTT_VERSION&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#ae9070d21de569f999a9575049cdd6da1">MQTTClient.h</a></li>
<li>MQTTProperties_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a5a80e158486a414ccdfcdd7f75f23988">MQTTProperties.h</a></li>
<li>MQTTResponse_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a17f171200136bcfa933eb50ef21531a7">MQTTClient.h</a></li>
<li>MQTTSubscribe_options_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_subscribe_opts_8h.html#aec3b45fd0367106eea344396f87cfda7">MQTTSubscribeOpts.h</a></li>
<li>MQTTVERSION_3_1&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a4603b988e76872e1f23f135d225ce2fb">MQTTClient.h</a></li>
<li>MQTTVERSION_3_1_1&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#ac79cc6fdeaa9e3f4ee12c3418898b1ef">MQTTClient.h</a></li>
<li>MQTTVERSION_5&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#af8b176fa4d5b89789767ce972338e1e3">MQTTClient.h</a></li>
<li>MQTTVERSION_DEFAULT&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a75b80b01f98d5a1ffa2a4d42995a8397">MQTTClient.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:07 for Paho MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
