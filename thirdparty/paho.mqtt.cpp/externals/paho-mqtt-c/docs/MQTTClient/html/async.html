<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho MQTT C Client Library: Asynchronous vs synchronous client applications</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div><div class="header">
  <div class="headertitle"><div class="title">Asynchronous vs synchronous client applications</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>This client library supports two modes of operation. These are referred to as <b>synchronous</b> and <b>asynchronous</b> modes. If your application calls <a class="el" href="_m_q_t_t_client_8h.html#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks()</a>, this puts the client into asynchronous mode, otherwise it operates in synchronous mode.</p>
<p>In synchronous mode, the client application runs on a single thread. Messages are published using the <a class="el" href="_m_q_t_t_client_8h.html#afe9c34013c3511b8ef6cd36bf703678d">MQTTClient_publish()</a> and <a class="el" href="_m_q_t_t_client_8h.html#ace320b8a92c7087d9dd5cf242d50389d">MQTTClient_publishMessage()</a> functions. To determine that a QoS1 or QoS2 (see <a class="el" href="qos.html">Quality of service</a>) message has been successfully delivered, the application must call the <a class="el" href="_m_q_t_t_client_8h.html#a83807ec81fe8c3941e368ab329d43067">MQTTClient_waitForCompletion()</a> function. An example showing synchronous publication is shown in <a class="el" href="pubsync.html">Synchronous publication example</a>. Receiving messages in synchronous mode uses the <a class="el" href="_m_q_t_t_client_8h.html#a4c2df88d00a3dadd510a8cb774739366">MQTTClient_receive()</a> function. Client applications must call either <a class="el" href="_m_q_t_t_client_8h.html#a4c2df88d00a3dadd510a8cb774739366">MQTTClient_receive()</a> or <a class="el" href="_m_q_t_t_client_8h.html#a8ad3d29864a9ca08202b0832e0f6678e">MQTTClient_yield()</a> relatively frequently in order to allow processing of acknowledgements and the MQTT "pings" that keep the network connection to the server alive.</p>
<p>In asynchronous mode, the client application runs on several threads. The main program calls functions in the client library to publish and subscribe, just as for the synchronous mode. Processing of handshaking and maintaining the network connection is performed in the background, however. Notifications of status and message reception are provided to the client application using callbacks registered with the library by the call to <a class="el" href="_m_q_t_t_client_8h.html#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks()</a> (see <a class="el" href="_m_q_t_t_client_8h.html#aa42130dd069e7e949bcab37b6dce64a5">MQTTClient_messageArrived()</a>, <a class="el" href="_m_q_t_t_client_8h.html#a6bb253f16754e7cc81798c9fda0e36cf">MQTTClient_connectionLost()</a> and <a class="el" href="_m_q_t_t_client_8h.html#abef83794d8252551ed248cde6eb845a6">MQTTClient_deliveryComplete()</a>). This API is not thread safe however - it is not possible to call it from multiple threads without synchronization. You can use the MQTTAsync API for that. </p>
</div></div><!-- contents -->
</div><!-- PageDoc -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:06 for Paho MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
