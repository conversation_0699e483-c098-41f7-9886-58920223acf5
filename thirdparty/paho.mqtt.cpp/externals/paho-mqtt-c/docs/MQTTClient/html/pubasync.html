<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho MQTT C Client Library: Asynchronous publication example</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div><div class="header">
  <div class="headertitle"><div class="title">Asynchronous publication example</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><div class="fragment"><div class="line"><span class="preprocessor">#include &lt;stdio.h&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;stdlib.h&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;string.h&gt;</span></div>
<div class="line"><span class="preprocessor">#include &quot;<a class="code" href="_m_q_t_t_client_8h.html">MQTTClient.h</a>&quot;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#if !defined(_WIN32)</span></div>
<div class="line"><span class="preprocessor">#include &lt;unistd.h&gt;</span></div>
<div class="line"><span class="preprocessor">#else</span></div>
<div class="line"><span class="preprocessor">#include &lt;windows.h&gt;</span></div>
<div class="line"><span class="preprocessor">#endif</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#define ADDRESS     &quot;tcp://mqtt.eclipseprojects.io:1883&quot;</span></div>
<div class="line"><span class="preprocessor">#define CLIENTID    &quot;ExampleClientPub&quot;</span></div>
<div class="line"><span class="preprocessor">#define TOPIC       &quot;MQTT Examples&quot;</span></div>
<div class="line"><span class="preprocessor">#define PAYLOAD     &quot;Hello World!&quot;</span></div>
<div class="line"><span class="preprocessor">#define QOS         1</span></div>
<div class="line"><span class="preprocessor">#define TIMEOUT     10000L</span></div>
<div class="line"> </div>
<div class="line"><a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> deliveredtoken;</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> delivered(<span class="keywordtype">void</span> *context, <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> dt)</div>
<div class="line">{</div>
<div class="line">    printf(<span class="stringliteral">&quot;Message with token value %d delivery confirmed\n&quot;</span>, dt);</div>
<div class="line">    deliveredtoken = dt;</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> msgarrvd(<span class="keywordtype">void</span> *context, <span class="keywordtype">char</span> *topicName, <span class="keywordtype">int</span> topicLen, <a class="code hl_struct" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a> *message)</div>
<div class="line">{</div>
<div class="line">    printf(<span class="stringliteral">&quot;Message arrived\n&quot;</span>);</div>
<div class="line">    printf(<span class="stringliteral">&quot;     topic: %s\n&quot;</span>, topicName);</div>
<div class="line">    printf(<span class="stringliteral">&quot;   message: %.*s\n&quot;</span>, message-&gt;<a class="code hl_variable" href="struct_m_q_t_t_client__message.html#aa3cb44feb3ae6d11b3a4cad2d94cb33a">payloadlen</a>, (<span class="keywordtype">char</span>*)message-&gt;<a class="code hl_variable" href="struct_m_q_t_t_client__message.html#a9eff55064941fb604452abb0050ea99d">payload</a>);</div>
<div class="line">    <a class="code hl_function" href="_m_q_t_t_client_8h.html#abd8abde4f39d3e689029de27f7a98a65">MQTTClient_freeMessage</a>(&amp;message);</div>
<div class="line">    <a class="code hl_function" href="_m_q_t_t_client_8h.html#a203b545c999beb6b825ec99b6aea79ab">MQTTClient_free</a>(topicName);</div>
<div class="line">    <span class="keywordflow">return</span> 1;</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> connlost(<span class="keywordtype">void</span> *context, <span class="keywordtype">char</span> *cause)</div>
<div class="line">{</div>
<div class="line">    printf(<span class="stringliteral">&quot;\nConnection lost\n&quot;</span>);</div>
<div class="line">    printf(<span class="stringliteral">&quot;     cause: %s\n&quot;</span>, cause);</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main(<span class="keywordtype">int</span> argc, <span class="keywordtype">char</span>* argv[])</div>
<div class="line">{</div>
<div class="line">    <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> client;</div>
<div class="line">    <a class="code hl_struct" href="struct_m_q_t_t_client__connect_options.html">MQTTClient_connectOptions</a> conn_opts = <a class="code hl_define" href="_m_q_t_t_client_8h.html#aefd7c865f2641c8155b763fdf3061c25">MQTTClient_connectOptions_initializer</a>;</div>
<div class="line">    <a class="code hl_struct" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a> pubmsg = <a class="code hl_define" href="_m_q_t_t_client_8h.html#aa1fd995924d3df75959fcf57e87aefac">MQTTClient_message_initializer</a>;</div>
<div class="line">    <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> token;</div>
<div class="line">    <span class="keywordtype">int</span> rc;</div>
<div class="line"> </div>
<div class="line">    <span class="keywordflow">if</span> ((rc = <a class="code hl_function" href="_m_q_t_t_client_8h.html#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create</a>(&amp;client, ADDRESS, CLIENTID,</div>
<div class="line">        <a class="code hl_define" href="_m_q_t_t_client_persistence_8h.html#ae01e089313a65ac4661ed216b6ac00fa">MQTTCLIENT_PERSISTENCE_NONE</a>, NULL)) != <a class="code hl_define" href="_m_q_t_t_client_8h.html#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a>)</div>
<div class="line">    {</div>
<div class="line">        printf(<span class="stringliteral">&quot;Failed to create client, return code %d\n&quot;</span>, rc);</div>
<div class="line">        rc = EXIT_FAILURE;</div>
<div class="line">        <span class="keywordflow">goto</span> exit;</div>
<div class="line">    }</div>
<div class="line"> </div>
<div class="line">    <span class="keywordflow">if</span> ((rc = <a class="code hl_function" href="_m_q_t_t_client_8h.html#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks</a>(client, NULL, connlost, msgarrvd, delivered)) != <a class="code hl_define" href="_m_q_t_t_client_8h.html#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a>)</div>
<div class="line">    {</div>
<div class="line">        printf(<span class="stringliteral">&quot;Failed to set callbacks, return code %d\n&quot;</span>, rc);</div>
<div class="line">        rc = EXIT_FAILURE;</div>
<div class="line">        <span class="keywordflow">goto</span> destroy_exit;</div>
<div class="line">    }</div>
<div class="line"> </div>
<div class="line">    conn_opts.<a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#ac8dd0930672a9c7d71fc645aa1f0521d">keepAliveInterval</a> = 20;</div>
<div class="line">    conn_opts.<a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">cleansession</a> = 1;</div>
<div class="line">    <span class="keywordflow">if</span> ((rc = <a class="code hl_function" href="_m_q_t_t_client_8h.html#aaa8ae61cd65c9dc0846df10122d7bd4e">MQTTClient_connect</a>(client, &amp;conn_opts)) != <a class="code hl_define" href="_m_q_t_t_client_8h.html#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a>)</div>
<div class="line">    {</div>
<div class="line">        printf(<span class="stringliteral">&quot;Failed to connect, return code %d\n&quot;</span>, rc);</div>
<div class="line">        rc = EXIT_FAILURE;</div>
<div class="line">        <span class="keywordflow">goto</span> destroy_exit;</div>
<div class="line">    }</div>
<div class="line"> </div>
<div class="line">    pubmsg.<a class="code hl_variable" href="struct_m_q_t_t_client__message.html#a9eff55064941fb604452abb0050ea99d">payload</a> = PAYLOAD;</div>
<div class="line">    pubmsg.<a class="code hl_variable" href="struct_m_q_t_t_client__message.html#aa3cb44feb3ae6d11b3a4cad2d94cb33a">payloadlen</a> = (int)strlen(PAYLOAD);</div>
<div class="line">    pubmsg.<a class="code hl_variable" href="struct_m_q_t_t_client__message.html#a35738099155a0e4f54050da474bab2e7">qos</a> = QOS;</div>
<div class="line">    pubmsg.<a class="code hl_variable" href="struct_m_q_t_t_client__message.html#a6a4904c112507a43e7dc8495b62cc0fc">retained</a> = 0;</div>
<div class="line">    deliveredtoken = 0;</div>
<div class="line">    <span class="keywordflow">if</span> ((rc = <a class="code hl_function" href="_m_q_t_t_client_8h.html#ace320b8a92c7087d9dd5cf242d50389d">MQTTClient_publishMessage</a>(client, TOPIC, &amp;pubmsg, &amp;token)) != <a class="code hl_define" href="_m_q_t_t_client_8h.html#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a>)</div>
<div class="line">    {</div>
<div class="line">        printf(<span class="stringliteral">&quot;Failed to publish message, return code %d\n&quot;</span>, rc);</div>
<div class="line">        rc = EXIT_FAILURE;</div>
<div class="line">    }</div>
<div class="line">    <span class="keywordflow">else</span></div>
<div class="line">    {</div>
<div class="line">        printf(<span class="stringliteral">&quot;Waiting for publication of %s\n&quot;</span></div>
<div class="line">            <span class="stringliteral">&quot;on topic %s for client with ClientID: %s\n&quot;</span>,</div>
<div class="line">            PAYLOAD, TOPIC, CLIENTID);</div>
<div class="line">        <span class="keywordflow">while</span> (deliveredtoken != token)</div>
<div class="line">        {</div>
<div class="line"><span class="preprocessor">                        #if defined(_WIN32)</span></div>
<div class="line">                                Sleep(100);</div>
<div class="line"><span class="preprocessor">                        #else</span></div>
<div class="line">                                usleep(10000L);</div>
<div class="line"><span class="preprocessor">                        #endif</span></div>
<div class="line">        }</div>
<div class="line">    }</div>
<div class="line"> </div>
<div class="line">    <span class="keywordflow">if</span> ((rc = <a class="code hl_function" href="_m_q_t_t_client_8h.html#a1e4d90c13a3c0705bc4a13bfe64e6525">MQTTClient_disconnect</a>(client, 10000)) != <a class="code hl_define" href="_m_q_t_t_client_8h.html#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a>)</div>
<div class="line">    {</div>
<div class="line">        printf(<span class="stringliteral">&quot;Failed to disconnect, return code %d\n&quot;</span>, rc);</div>
<div class="line">        rc = EXIT_FAILURE;</div>
<div class="line">    }</div>
<div class="line"> </div>
<div class="line">destroy_exit:</div>
<div class="line">    <a class="code hl_function" href="_m_q_t_t_client_8h.html#ae700c3f5cfea3813264ce95e7c8cf498">MQTTClient_destroy</a>(&amp;client);</div>
<div class="line"> </div>
<div class="line">exit:</div>
<div class="line">    <span class="keywordflow">return</span> rc;</div>
<div class="line">}</div>
<div class="ttc" id="a_m_q_t_t_client_8h_html"><div class="ttname"><a href="_m_q_t_t_client_8h.html">MQTTClient.h</a></div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a1e4d90c13a3c0705bc4a13bfe64e6525"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a1e4d90c13a3c0705bc4a13bfe64e6525">MQTTClient_disconnect</a></div><div class="ttdeci">int MQTTClient_disconnect(MQTTClient handle, int timeout)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a203b545c999beb6b825ec99b6aea79ab"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a203b545c999beb6b825ec99b6aea79ab">MQTTClient_free</a></div><div class="ttdeci">void MQTTClient_free(void *ptr)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a73e49030fd8b7074aa1aa45669b7fe8d"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a></div><div class="ttdeci">int MQTTClient_deliveryToken</div><div class="ttdef"><b>Definition</b> MQTTClient.h:267</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a7649e3913f9a216424d296f88a969c59"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></div><div class="ttdeci">void * MQTTClient</div><div class="ttdef"><b>Definition</b> MQTTClient.h:256</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a9a0518d9ca924d12c1329dbe3de5f2b6"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create</a></div><div class="ttdeci">int MQTTClient_create(MQTTClient *handle, const char *serverURI, const char *clientId, int persistence_type, void *persistence_context)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aa1fd995924d3df75959fcf57e87aefac"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aa1fd995924d3df75959fcf57e87aefac">MQTTClient_message_initializer</a></div><div class="ttdeci">#define MQTTClient_message_initializer</div><div class="ttdef"><b>Definition</b> MQTTClient.h:337</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aaa8ae61cd65c9dc0846df10122d7bd4e"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aaa8ae61cd65c9dc0846df10122d7bd4e">MQTTClient_connect</a></div><div class="ttdeci">int MQTTClient_connect(MQTTClient handle, MQTTClient_connectOptions *options)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aad27d07782991a4937ebf2f39a021f83"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks</a></div><div class="ttdeci">int MQTTClient_setCallbacks(MQTTClient handle, void *context, MQTTClient_connectionLost *cl, MQTTClient_messageArrived *ma, MQTTClient_deliveryComplete *dc)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_abd8abde4f39d3e689029de27f7a98a65"><div class="ttname"><a href="_m_q_t_t_client_8h.html#abd8abde4f39d3e689029de27f7a98a65">MQTTClient_freeMessage</a></div><div class="ttdeci">void MQTTClient_freeMessage(MQTTClient_message **msg)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_acba095704d79e5a1996389fa26203f73"><div class="ttname"><a href="_m_q_t_t_client_8h.html#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a></div><div class="ttdeci">#define MQTTCLIENT_SUCCESS</div><div class="ttdef"><b>Definition</b> MQTTClient.h:137</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_ace320b8a92c7087d9dd5cf242d50389d"><div class="ttname"><a href="_m_q_t_t_client_8h.html#ace320b8a92c7087d9dd5cf242d50389d">MQTTClient_publishMessage</a></div><div class="ttdeci">int MQTTClient_publishMessage(MQTTClient handle, const char *topicName, MQTTClient_message *msg, MQTTClient_deliveryToken *dt)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_ae700c3f5cfea3813264ce95e7c8cf498"><div class="ttname"><a href="_m_q_t_t_client_8h.html#ae700c3f5cfea3813264ce95e7c8cf498">MQTTClient_destroy</a></div><div class="ttdeci">void MQTTClient_destroy(MQTTClient *handle)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aefd7c865f2641c8155b763fdf3061c25"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aefd7c865f2641c8155b763fdf3061c25">MQTTClient_connectOptions_initializer</a></div><div class="ttdeci">#define MQTTClient_connectOptions_initializer</div><div class="ttdef"><b>Definition</b> MQTTClient.h:990</div></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html_ae01e089313a65ac4661ed216b6ac00fa"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html#ae01e089313a65ac4661ed216b6ac00fa">MQTTCLIENT_PERSISTENCE_NONE</a></div><div class="ttdeci">#define MQTTCLIENT_PERSISTENCE_NONE</div><div class="ttdef"><b>Definition</b> MQTTClientPersistence.h:74</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html">MQTTClient_connectOptions</a></div><div class="ttdef"><b>Definition</b> MQTTClient.h:831</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_a036c36a2a4d3a3ffae9ab4dd8b3e7f7b"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">MQTTClient_connectOptions::cleansession</a></div><div class="ttdeci">int cleansession</div><div class="ttdef"><b>Definition</b> MQTTClient.h:876</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_ac8dd0930672a9c7d71fc645aa1f0521d"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#ac8dd0930672a9c7d71fc645aa1f0521d">MQTTClient_connectOptions::keepAliveInterval</a></div><div class="ttdeci">int keepAliveInterval</div><div class="ttdef"><b>Definition</b> MQTTClient.h:854</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__message_html"><div class="ttname"><a href="struct_m_q_t_t_client__message.html">MQTTClient_message</a></div><div class="ttdef"><b>Definition</b> MQTTClient.h:277</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__message_html_a35738099155a0e4f54050da474bab2e7"><div class="ttname"><a href="struct_m_q_t_t_client__message.html#a35738099155a0e4f54050da474bab2e7">MQTTClient_message::qos</a></div><div class="ttdeci">int qos</div><div class="ttdef"><b>Definition</b> MQTTClient.h:300</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__message_html_a6a4904c112507a43e7dc8495b62cc0fc"><div class="ttname"><a href="struct_m_q_t_t_client__message.html#a6a4904c112507a43e7dc8495b62cc0fc">MQTTClient_message::retained</a></div><div class="ttdeci">int retained</div><div class="ttdef"><b>Definition</b> MQTTClient.h:319</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__message_html_a9eff55064941fb604452abb0050ea99d"><div class="ttname"><a href="struct_m_q_t_t_client__message.html#a9eff55064941fb604452abb0050ea99d">MQTTClient_message::payload</a></div><div class="ttdeci">void * payload</div><div class="ttdef"><b>Definition</b> MQTTClient.h:286</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__message_html_aa3cb44feb3ae6d11b3a4cad2d94cb33a"><div class="ttname"><a href="struct_m_q_t_t_client__message.html#aa3cb44feb3ae6d11b3a4cad2d94cb33a">MQTTClient_message::payloadlen</a></div><div class="ttdeci">int payloadlen</div><div class="ttdef"><b>Definition</b> MQTTClient.h:284</div></div>
</div><!-- fragment --> </div></div><!-- contents -->
</div><!-- PageDoc -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:06 for Paho MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
