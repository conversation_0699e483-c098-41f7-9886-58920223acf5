<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho MQTT C Client Library: MQTTClient_connectOptions Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">MQTTClient_connectOptions Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="_m_q_t_t_client_8h_source.html">MQTTClient.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:aa5326df180cb23c59afbcab711a06479" id="r_aa5326df180cb23c59afbcab711a06479"><td class="memItemLeft" align="right" valign="top">char&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa5326df180cb23c59afbcab711a06479">struct_id</a> [4]</td></tr>
<tr class="separator:aa5326df180cb23c59afbcab711a06479"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0761a5e5be0383882e42924de8e51f82" id="r_a0761a5e5be0383882e42924de8e51f82"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0761a5e5be0383882e42924de8e51f82">struct_version</a></td></tr>
<tr class="separator:a0761a5e5be0383882e42924de8e51f82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac8dd0930672a9c7d71fc645aa1f0521d" id="r_ac8dd0930672a9c7d71fc645aa1f0521d"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac8dd0930672a9c7d71fc645aa1f0521d">keepAliveInterval</a></td></tr>
<tr class="separator:ac8dd0930672a9c7d71fc645aa1f0521d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a036c36a2a4d3a3ffae9ab4dd8b3e7f7b" id="r_a036c36a2a4d3a3ffae9ab4dd8b3e7f7b"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">cleansession</a></td></tr>
<tr class="separator:a036c36a2a4d3a3ffae9ab4dd8b3e7f7b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9f1cdffc99659fd4e2d20e6de3c64df0" id="r_a9f1cdffc99659fd4e2d20e6de3c64df0"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9f1cdffc99659fd4e2d20e6de3c64df0">reliable</a></td></tr>
<tr class="separator:a9f1cdffc99659fd4e2d20e6de3c64df0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0a880e99d47eb2efe552abe5079bdc9d" id="r_a0a880e99d47eb2efe552abe5079bdc9d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_client__will_options.html">MQTTClient_willOptions</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0a880e99d47eb2efe552abe5079bdc9d">will</a></td></tr>
<tr class="separator:a0a880e99d47eb2efe552abe5079bdc9d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba2dfcdfda80edcb531a5a7115d3e043" id="r_aba2dfcdfda80edcb531a5a7115d3e043"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aba2dfcdfda80edcb531a5a7115d3e043">username</a></td></tr>
<tr class="separator:aba2dfcdfda80edcb531a5a7115d3e043"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa4a2ebcb494493f648ae1e6975672575" id="r_aa4a2ebcb494493f648ae1e6975672575"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa4a2ebcb494493f648ae1e6975672575">password</a></td></tr>
<tr class="separator:aa4a2ebcb494493f648ae1e6975672575"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a38c6aa24b36d981c49405db425c24db0" id="r_a38c6aa24b36d981c49405db425c24db0"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a38c6aa24b36d981c49405db425c24db0">connectTimeout</a></td></tr>
<tr class="separator:a38c6aa24b36d981c49405db425c24db0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac73f57846c42bcaa9a47e6721a957748" id="r_ac73f57846c42bcaa9a47e6721a957748"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac73f57846c42bcaa9a47e6721a957748">retryInterval</a></td></tr>
<tr class="separator:ac73f57846c42bcaa9a47e6721a957748"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a0b0f0fc7c675312dc232e2458078c7" id="r_a8a0b0f0fc7c675312dc232e2458078c7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_client___s_s_l_options.html">MQTTClient_SSLOptions</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8a0b0f0fc7c675312dc232e2458078c7">ssl</a></td></tr>
<tr class="separator:a8a0b0f0fc7c675312dc232e2458078c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa82629005937abd92e97084a428cd61f" id="r_aa82629005937abd92e97084a428cd61f"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa82629005937abd92e97084a428cd61f">serverURIcount</a></td></tr>
<tr class="separator:aa82629005937abd92e97084a428cd61f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba22d81c407fb2ba590dba476240d3e9" id="r_aba22d81c407fb2ba590dba476240d3e9"><td class="memItemLeft" align="right" valign="top">char *const  *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aba22d81c407fb2ba590dba476240d3e9">serverURIs</a></td></tr>
<tr class="separator:aba22d81c407fb2ba590dba476240d3e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a12d546fd0ccf4e1091b18e1b735c7240" id="r_a12d546fd0ccf4e1091b18e1b735c7240"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a12d546fd0ccf4e1091b18e1b735c7240">MQTTVersion</a></td></tr>
<tr class="separator:a12d546fd0ccf4e1091b18e1b735c7240"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afbca347de18f7a8c57de1f16d3dadde6" id="r_afbca347de18f7a8c57de1f16d3dadde6"><td class="memItemLeft" >struct {&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a313446ca7679b36652722ffe53d05228" id="r_a313446ca7679b36652722ffe53d05228"><td class="memItemLeft" >&#160;&#160;&#160;const char *&#160;&#160;&#160;<a class="el" href="#a313446ca7679b36652722ffe53d05228">serverURI</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a313446ca7679b36652722ffe53d05228"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a12d546fd0ccf4e1091b18e1b735c7240" id="r_a12d546fd0ccf4e1091b18e1b735c7240"><td class="memItemLeft" >&#160;&#160;&#160;int&#160;&#160;&#160;<a class="el" href="#a12d546fd0ccf4e1091b18e1b735c7240">MQTTVersion</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a12d546fd0ccf4e1091b18e1b735c7240"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44baf2cb9a0bbcec3ed2eace43f832d1" id="r_a44baf2cb9a0bbcec3ed2eace43f832d1"><td class="memItemLeft" >&#160;&#160;&#160;int&#160;&#160;&#160;<a class="el" href="#a44baf2cb9a0bbcec3ed2eace43f832d1">sessionPresent</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a44baf2cb9a0bbcec3ed2eace43f832d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afbca347de18f7a8c57de1f16d3dadde6" id="r_afbca347de18f7a8c57de1f16d3dadde6"><td class="memItemLeft" valign="top">}&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#afbca347de18f7a8c57de1f16d3dadde6">returned</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:afbca347de18f7a8c57de1f16d3dadde6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae7280d284792990b5d8f6f29d4e0b113" id="r_ae7280d284792990b5d8f6f29d4e0b113"><td class="memItemLeft" >struct {&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afed088663f8704004425cdae2120b9b3" id="r_afed088663f8704004425cdae2120b9b3"><td class="memItemLeft" >&#160;&#160;&#160;int&#160;&#160;&#160;<a class="el" href="#afed088663f8704004425cdae2120b9b3">len</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:afed088663f8704004425cdae2120b9b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d49d74db4c035719c3867723cf7e779" id="r_a0d49d74db4c035719c3867723cf7e779"><td class="memItemLeft" >&#160;&#160;&#160;const void *&#160;&#160;&#160;<a class="el" href="#a0d49d74db4c035719c3867723cf7e779">data</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a0d49d74db4c035719c3867723cf7e779"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae7280d284792990b5d8f6f29d4e0b113" id="r_ae7280d284792990b5d8f6f29d4e0b113"><td class="memItemLeft" valign="top">}&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae7280d284792990b5d8f6f29d4e0b113">binarypwd</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:ae7280d284792990b5d8f6f29d4e0b113"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae3f99bf4663ab7b9e9259feeba41fab2" id="r_ae3f99bf4663ab7b9e9259feeba41fab2"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae3f99bf4663ab7b9e9259feeba41fab2">maxInflightMessages</a></td></tr>
<tr class="separator:ae3f99bf4663ab7b9e9259feeba41fab2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acdcb75a5d5981da027bce83849140f7b" id="r_acdcb75a5d5981da027bce83849140f7b"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acdcb75a5d5981da027bce83849140f7b">cleanstart</a></td></tr>
<tr class="separator:acdcb75a5d5981da027bce83849140f7b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a018eec60631f40c01e6dcb727bffd33f" id="r_a018eec60631f40c01e6dcb727bffd33f"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="struct_m_q_t_t_client__name_value.html">MQTTClient_nameValue</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a018eec60631f40c01e6dcb727bffd33f">httpHeaders</a></td></tr>
<tr class="separator:a018eec60631f40c01e6dcb727bffd33f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add124780ab2de397a96780576c2f112c" id="r_add124780ab2de397a96780576c2f112c"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#add124780ab2de397a96780576c2f112c">httpProxy</a></td></tr>
<tr class="separator:add124780ab2de397a96780576c2f112c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a388b78d8a75658928238f700f207ad92" id="r_a388b78d8a75658928238f700f207ad92"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a388b78d8a75658928238f700f207ad92">httpsProxy</a></td></tr>
<tr class="separator:a388b78d8a75658928238f700f207ad92"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="struct_m_q_t_t_client__connect_options.html">MQTTClient_connectOptions</a> defines several settings that control the way the client connects to an MQTT server.</p>
<p><b>Note:</b> Default values are not defined for members of <a class="el" href="struct_m_q_t_t_client__connect_options.html">MQTTClient_connectOptions</a> so it is good practice to specify all settings. If the <a class="el" href="struct_m_q_t_t_client__connect_options.html">MQTTClient_connectOptions</a> structure is defined as an automatic variable, all members are set to random values and thus must be set by the client application. If the <a class="el" href="struct_m_q_t_t_client__connect_options.html">MQTTClient_connectOptions</a> structure is defined as a static variable, initialization (in compliant compilers) sets all values to 0 (NULL for pointers). A <a class="el" href="#ac8dd0930672a9c7d71fc645aa1f0521d">keepAliveInterval</a> setting of 0 prevents correct operation of the client and so you <b>must</b> at least set a value for <a class="el" href="#ac8dd0930672a9c7d71fc645aa1f0521d">keepAliveInterval</a>.</p>
<p>Suitable default values are set in the following initializers:</p><ul>
<li>MQTTClient_connectOptions_initializer: for MQTT 3.1.1 non-WebSockets</li>
<li>MQTTClient_connectOptions_initializer5: for MQTT 5.0 non-WebSockets</li>
<li>MQTTClient_connectOptions_initializer_ws: for MQTT 3.1.1 WebSockets</li>
<li>MQTTClient_connectOptions_initializer5_ws: for MQTT 5.0 WebSockets </li>
</ul>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="aa5326df180cb23c59afbcab711a06479" name="aa5326df180cb23c59afbcab711a06479"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa5326df180cb23c59afbcab711a06479">&#9670;&#160;</a></span>struct_id</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char struct_id[4]</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The eyecatcher for this structure. must be MQTC. </p>

</div>
</div>
<a id="a0761a5e5be0383882e42924de8e51f82" name="a0761a5e5be0383882e42924de8e51f82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0761a5e5be0383882e42924de8e51f82">&#9670;&#160;</a></span>struct_version</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int struct_version</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The version number of this structure. Must be 0, 1, 2, 3, 4, 5, 6, 7 or 8. 0 signifies no SSL options and no serverURIs 1 signifies no serverURIs 2 signifies no MQTTVersion 3 signifies no returned values 4 signifies no binary password option 5 signifies no maxInflightMessages and cleanstart 6 signifies no HTTP headers option 7 signifies no HTTP proxy and HTTPS proxy options </p>

</div>
</div>
<a id="ac8dd0930672a9c7d71fc645aa1f0521d" name="ac8dd0930672a9c7d71fc645aa1f0521d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac8dd0930672a9c7d71fc645aa1f0521d">&#9670;&#160;</a></span>keepAliveInterval</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int keepAliveInterval</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The "keep alive" interval, measured in seconds, defines the maximum time that should pass without communication between the client and the server The client will ensure that at least one message travels across the network within each keep alive period. In the absence of a data-related message during the time period, the client sends a very small MQTT "ping" message, which the server will acknowledge. The keep alive interval enables the client to detect when the server is no longer available without having to wait for the long TCP/IP timeout. </p>

</div>
</div>
<a id="a036c36a2a4d3a3ffae9ab4dd8b3e7f7b" name="a036c36a2a4d3a3ffae9ab4dd8b3e7f7b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">&#9670;&#160;</a></span>cleansession</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int cleansession</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a boolean value. The cleansession setting controls the behaviour of both the client and the server at connection and disconnection time. The client and server both maintain session state information. This information is used to ensure "at least once" and "exactly once" delivery, and "exactly once" receipt of messages. Session state also includes subscriptions created by an MQTT client. You can choose to maintain or discard state information between sessions.</p>
<p>When cleansession is true, the state information is discarded at connect and disconnect. Setting cleansession to false keeps the state information. When you connect an MQTT client application with <a class="el" href="_m_q_t_t_client_8h.html#aaa8ae61cd65c9dc0846df10122d7bd4e">MQTTClient_connect()</a>, the client identifies the connection using the client identifier and the address of the server. The server checks whether session information for this client has been saved from a previous connection to the server. If a previous session still exists, and cleansession=true, then the previous session information at the client and server is cleared. If cleansession=false, the previous session is resumed. If no previous session exists, a new session is started. </p>

</div>
</div>
<a id="a9f1cdffc99659fd4e2d20e6de3c64df0" name="a9f1cdffc99659fd4e2d20e6de3c64df0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9f1cdffc99659fd4e2d20e6de3c64df0">&#9670;&#160;</a></span>reliable</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int reliable</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a boolean value that controls how many messages can be in-flight simultaneously. Setting <em>reliable</em> to true means that a published message must be completed (acknowledgements received) before another can be sent. Attempts to publish additional messages receive an <a class="el" href="_m_q_t_t_client_8h.html#a8fc442fc2e9dfb422a163ab1fa02e0cb">MQTTCLIENT_MAX_MESSAGES_INFLIGHT</a> return code. Setting this flag to false allows up to 10 messages to be in-flight. This can increase overall throughput in some circumstances. </p>

</div>
</div>
<a id="a0a880e99d47eb2efe552abe5079bdc9d" name="a0a880e99d47eb2efe552abe5079bdc9d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0a880e99d47eb2efe552abe5079bdc9d">&#9670;&#160;</a></span>will</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_client__will_options.html">MQTTClient_willOptions</a>* will</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a pointer to an <a class="el" href="struct_m_q_t_t_client__will_options.html">MQTTClient_willOptions</a> structure. If your application does not make use of the Last Will and Testament feature, set this pointer to NULL. </p>

</div>
</div>
<a id="aba2dfcdfda80edcb531a5a7115d3e043" name="aba2dfcdfda80edcb531a5a7115d3e043"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aba2dfcdfda80edcb531a5a7115d3e043">&#9670;&#160;</a></span>username</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* username</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>MQTT servers that support the MQTT v3.1.1 protocol provide authentication and authorisation by user name and password. This is the user name parameter. </p>

</div>
</div>
<a id="aa4a2ebcb494493f648ae1e6975672575" name="aa4a2ebcb494493f648ae1e6975672575"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa4a2ebcb494493f648ae1e6975672575">&#9670;&#160;</a></span>password</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* password</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>MQTT servers that support the MQTT v3.1.1 protocol provide authentication and authorisation by user name and password. This is the password parameter. </p>

</div>
</div>
<a id="a38c6aa24b36d981c49405db425c24db0" name="a38c6aa24b36d981c49405db425c24db0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a38c6aa24b36d981c49405db425c24db0">&#9670;&#160;</a></span>connectTimeout</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int connectTimeout</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The time interval in seconds to allow a connect to complete. </p>

</div>
</div>
<a id="ac73f57846c42bcaa9a47e6721a957748" name="ac73f57846c42bcaa9a47e6721a957748"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac73f57846c42bcaa9a47e6721a957748">&#9670;&#160;</a></span>retryInterval</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int retryInterval</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The time interval in seconds after which unacknowledged publish requests are retried during a TCP session. With MQTT 3.1.1 and later, retries are not required except on reconnect. 0 turns off in-session retries, and is the recommended setting. Adding retries to an already overloaded network only exacerbates the problem. </p>

</div>
</div>
<a id="a8a0b0f0fc7c675312dc232e2458078c7" name="a8a0b0f0fc7c675312dc232e2458078c7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8a0b0f0fc7c675312dc232e2458078c7">&#9670;&#160;</a></span>ssl</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_client___s_s_l_options.html">MQTTClient_SSLOptions</a>* ssl</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a pointer to an <a class="el" href="struct_m_q_t_t_client___s_s_l_options.html">MQTTClient_SSLOptions</a> structure. If your application does not make use of SSL, set this pointer to NULL. </p>

</div>
</div>
<a id="aa82629005937abd92e97084a428cd61f" name="aa82629005937abd92e97084a428cd61f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa82629005937abd92e97084a428cd61f">&#9670;&#160;</a></span>serverURIcount</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int serverURIcount</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The number of entries in the optional serverURIs array. Defaults to 0. </p>

</div>
</div>
<a id="aba22d81c407fb2ba590dba476240d3e9" name="aba22d81c407fb2ba590dba476240d3e9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aba22d81c407fb2ba590dba476240d3e9">&#9670;&#160;</a></span>serverURIs</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char* const* serverURIs</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>An optional array of null-terminated strings specifying the servers to which the client will connect. Each string takes the form <em>protocol://host:port</em>. <em>protocol</em> must be <em>tcp</em>, <em>ssl</em>, <em>ws</em> or <em>wss</em>. The TLS enabled prefixes (ssl, wss) are only valid if a TLS version of the library is linked with. For <em>host</em>, you can specify either an IP address or a host name. For instance, to connect to a server running on the local machines with the default MQTT port, specify <em>tcp://localhost:1883</em>. If this list is empty (the default), the server URI specified on <a class="el" href="_m_q_t_t_client_8h.html#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a> is used. </p>

</div>
</div>
<a id="a12d546fd0ccf4e1091b18e1b735c7240" name="a12d546fd0ccf4e1091b18e1b735c7240"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a12d546fd0ccf4e1091b18e1b735c7240">&#9670;&#160;</a></span>MQTTVersion</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTVersion</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Sets the version of MQTT to be used on the connect. MQTTVERSION_DEFAULT (0) = default: start with 3.1.1, and if that fails, fall back to 3.1 MQTTVERSION_3_1 (3) = only try version 3.1 MQTTVERSION_3_1_1 (4) = only try version 3.1.1 MQTTVERSION_5 (5) = only try version 5.0</p>
<p>the MQTT version used to connect with </p>

</div>
</div>
<a id="a313446ca7679b36652722ffe53d05228" name="a313446ca7679b36652722ffe53d05228"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a313446ca7679b36652722ffe53d05228">&#9670;&#160;</a></span>serverURI</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* serverURI</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>the serverURI connected to </p>

</div>
</div>
<a id="a44baf2cb9a0bbcec3ed2eace43f832d1" name="a44baf2cb9a0bbcec3ed2eace43f832d1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a44baf2cb9a0bbcec3ed2eace43f832d1">&#9670;&#160;</a></span>sessionPresent</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int sessionPresent</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>if the MQTT version is 3.1.1, the value of sessionPresent returned in the connack </p>

</div>
</div>
<a id="afbca347de18f7a8c57de1f16d3dadde6" name="afbca347de18f7a8c57de1f16d3dadde6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afbca347de18f7a8c57de1f16d3dadde6">&#9670;&#160;</a></span>[struct]</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct  { ... }  returned</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returned from the connect when the MQTT version used to connect is 3.1.1 </p>

</div>
</div>
<a id="afed088663f8704004425cdae2120b9b3" name="afed088663f8704004425cdae2120b9b3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afed088663f8704004425cdae2120b9b3">&#9670;&#160;</a></span>len</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int len</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>binary password length </p>

</div>
</div>
<a id="a0d49d74db4c035719c3867723cf7e779" name="a0d49d74db4c035719c3867723cf7e779"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0d49d74db4c035719c3867723cf7e779">&#9670;&#160;</a></span>data</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const void* data</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>binary password data </p>

</div>
</div>
<a id="ae7280d284792990b5d8f6f29d4e0b113" name="ae7280d284792990b5d8f6f29d4e0b113"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae7280d284792990b5d8f6f29d4e0b113">&#9670;&#160;</a></span>[struct]</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct  { ... }  binarypwd</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Optional binary password. Only checked and used if the password option is NULL </p>

</div>
</div>
<a id="ae3f99bf4663ab7b9e9259feeba41fab2" name="ae3f99bf4663ab7b9e9259feeba41fab2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae3f99bf4663ab7b9e9259feeba41fab2">&#9670;&#160;</a></span>maxInflightMessages</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int maxInflightMessages</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The maximum number of messages in flight </p>

</div>
</div>
<a id="acdcb75a5d5981da027bce83849140f7b" name="acdcb75a5d5981da027bce83849140f7b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acdcb75a5d5981da027bce83849140f7b">&#9670;&#160;</a></span>cleanstart</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int cleanstart</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a018eec60631f40c01e6dcb727bffd33f" name="a018eec60631f40c01e6dcb727bffd33f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a018eec60631f40c01e6dcb727bffd33f">&#9670;&#160;</a></span>httpHeaders</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="struct_m_q_t_t_client__name_value.html">MQTTClient_nameValue</a>* httpHeaders</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>HTTP headers for websockets </p>

</div>
</div>
<a id="add124780ab2de397a96780576c2f112c" name="add124780ab2de397a96780576c2f112c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#add124780ab2de397a96780576c2f112c">&#9670;&#160;</a></span>httpProxy</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* httpProxy</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The string value of the HTTP proxy. Examples:</p><ul>
<li><a href="http://your.proxy.server:8080/">http://your.proxy.server:8080/</a></li>
<li><a href="http://user:<EMAIL>:8080/">http://user:<EMAIL>:8080/</a> </li>
</ul>

</div>
</div>
<a id="a388b78d8a75658928238f700f207ad92" name="a388b78d8a75658928238f700f207ad92"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a388b78d8a75658928238f700f207ad92">&#9670;&#160;</a></span>httpsProxy</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* httpsProxy</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>HTTPS proxy setting. See <a class="el" href="#add124780ab2de397a96780576c2f112c">MQTTClient_connectOptions.httpProxy</a> and the section <a class="el" href="_h_t_t_p_proxies.html">HTTP Proxies</a>. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_m_q_t_t_client_8h_source.html">MQTTClient.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:06 for Paho MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
