<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho MQTT C Client Library: MQTTClient_willOptions Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">MQTTClient_willOptions Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="_m_q_t_t_client_8h_source.html">MQTTClient.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:aa5326df180cb23c59afbcab711a06479" id="r_aa5326df180cb23c59afbcab711a06479"><td class="memItemLeft" align="right" valign="top">char&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa5326df180cb23c59afbcab711a06479">struct_id</a> [4]</td></tr>
<tr class="separator:aa5326df180cb23c59afbcab711a06479"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0761a5e5be0383882e42924de8e51f82" id="r_a0761a5e5be0383882e42924de8e51f82"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0761a5e5be0383882e42924de8e51f82">struct_version</a></td></tr>
<tr class="separator:a0761a5e5be0383882e42924de8e51f82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e20a7b350881d05108d6342884198a5" id="r_a0e20a7b350881d05108d6342884198a5"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0e20a7b350881d05108d6342884198a5">topicName</a></td></tr>
<tr class="separator:a0e20a7b350881d05108d6342884198a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a254bf0858da09c96a48daf64404eb4f8" id="r_a254bf0858da09c96a48daf64404eb4f8"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a254bf0858da09c96a48daf64404eb4f8">message</a></td></tr>
<tr class="separator:a254bf0858da09c96a48daf64404eb4f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6a4904c112507a43e7dc8495b62cc0fc" id="r_a6a4904c112507a43e7dc8495b62cc0fc"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6a4904c112507a43e7dc8495b62cc0fc">retained</a></td></tr>
<tr class="separator:a6a4904c112507a43e7dc8495b62cc0fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a35738099155a0e4f54050da474bab2e7" id="r_a35738099155a0e4f54050da474bab2e7"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a35738099155a0e4f54050da474bab2e7">qos</a></td></tr>
<tr class="separator:a35738099155a0e4f54050da474bab2e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e9356b973a918c25981982fe84e35d7" id="r_a0e9356b973a918c25981982fe84e35d7"><td class="memItemLeft" >struct {&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afed088663f8704004425cdae2120b9b3" id="r_afed088663f8704004425cdae2120b9b3"><td class="memItemLeft" >&#160;&#160;&#160;int&#160;&#160;&#160;<a class="el" href="#afed088663f8704004425cdae2120b9b3">len</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:afed088663f8704004425cdae2120b9b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d49d74db4c035719c3867723cf7e779" id="r_a0d49d74db4c035719c3867723cf7e779"><td class="memItemLeft" >&#160;&#160;&#160;const void *&#160;&#160;&#160;<a class="el" href="#a0d49d74db4c035719c3867723cf7e779">data</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a0d49d74db4c035719c3867723cf7e779"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e9356b973a918c25981982fe84e35d7" id="r_a0e9356b973a918c25981982fe84e35d7"><td class="memItemLeft" valign="top">}&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0e9356b973a918c25981982fe84e35d7">payload</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a0e9356b973a918c25981982fe84e35d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="struct_m_q_t_t_client__will_options.html">MQTTClient_willOptions</a> defines the MQTT "Last Will and Testament" (LWT) settings for the client. In the event that a client unexpectedly loses its connection to the server, the server publishes the LWT message to the LWT topic on behalf of the client. This allows other clients (subscribed to the LWT topic) to be made aware that the client has disconnected. To enable the LWT function for a specific client, a valid pointer to an <a class="el" href="struct_m_q_t_t_client__will_options.html">MQTTClient_willOptions</a> structure is passed in the <a class="el" href="struct_m_q_t_t_client__connect_options.html">MQTTClient_connectOptions</a> structure used in the <a class="el" href="_m_q_t_t_client_8h.html#aaa8ae61cd65c9dc0846df10122d7bd4e">MQTTClient_connect()</a> call that connects the client to the server. The pointer to <a class="el" href="struct_m_q_t_t_client__will_options.html">MQTTClient_willOptions</a> can be set to NULL if the LWT function is not required. </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="aa5326df180cb23c59afbcab711a06479" name="aa5326df180cb23c59afbcab711a06479"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa5326df180cb23c59afbcab711a06479">&#9670;&#160;</a></span>struct_id</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char struct_id[4]</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The eyecatcher for this structure. must be MQTW. </p>

</div>
</div>
<a id="a0761a5e5be0383882e42924de8e51f82" name="a0761a5e5be0383882e42924de8e51f82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0761a5e5be0383882e42924de8e51f82">&#9670;&#160;</a></span>struct_version</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int struct_version</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The version number of this structure. Must be 0 or 1 0 means there is no binary payload option </p>

</div>
</div>
<a id="a0e20a7b350881d05108d6342884198a5" name="a0e20a7b350881d05108d6342884198a5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0e20a7b350881d05108d6342884198a5">&#9670;&#160;</a></span>topicName</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* topicName</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The LWT topic to which the LWT message will be published. </p>

</div>
</div>
<a id="a254bf0858da09c96a48daf64404eb4f8" name="a254bf0858da09c96a48daf64404eb4f8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a254bf0858da09c96a48daf64404eb4f8">&#9670;&#160;</a></span>message</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* message</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The LWT payload in string form. </p>

</div>
</div>
<a id="a6a4904c112507a43e7dc8495b62cc0fc" name="a6a4904c112507a43e7dc8495b62cc0fc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6a4904c112507a43e7dc8495b62cc0fc">&#9670;&#160;</a></span>retained</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int retained</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The retained flag for the LWT message (see <a class="el" href="struct_m_q_t_t_client__message.html#a6a4904c112507a43e7dc8495b62cc0fc">MQTTClient_message.retained</a>). </p>

</div>
</div>
<a id="a35738099155a0e4f54050da474bab2e7" name="a35738099155a0e4f54050da474bab2e7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a35738099155a0e4f54050da474bab2e7">&#9670;&#160;</a></span>qos</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int qos</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The quality of service setting for the LWT message (see <a class="el" href="struct_m_q_t_t_client__message.html#a35738099155a0e4f54050da474bab2e7">MQTTClient_message.qos</a> and <a class="el" href="qos.html">Quality of service</a>). </p>

</div>
</div>
<a id="afed088663f8704004425cdae2120b9b3" name="afed088663f8704004425cdae2120b9b3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afed088663f8704004425cdae2120b9b3">&#9670;&#160;</a></span>len</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int len</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>binary payload length </p>

</div>
</div>
<a id="a0d49d74db4c035719c3867723cf7e779" name="a0d49d74db4c035719c3867723cf7e779"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0d49d74db4c035719c3867723cf7e779">&#9670;&#160;</a></span>data</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const void* data</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>binary payload data </p>

</div>
</div>
<a id="a0e9356b973a918c25981982fe84e35d7" name="a0e9356b973a918c25981982fe84e35d7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0e9356b973a918c25981982fe84e35d7">&#9670;&#160;</a></span>[struct]</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct  { ... }  payload</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The LWT payload in binary form. This is only checked and used if the message option is NULL </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_m_q_t_t_client_8h_source.html">MQTTClient.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:06 for Paho MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
