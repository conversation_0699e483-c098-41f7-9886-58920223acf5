<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho MQTT C Client Library: MQTTProperties.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">MQTTProperties.h</div></div>
</div><!--header-->
<div class="contents">
<a href="_m_q_t_t_properties_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="comment">/*******************************************************************************</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span><span class="comment"> * Copyright (c) 2017, 2024 IBM Corp. and others</span></div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span><span class="comment"> *</span></div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="comment"> * All rights reserved. This program and the accompanying materials</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span><span class="comment"> * are made available under the terms of the Eclipse Public License v2.0</span></div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span><span class="comment"> * and Eclipse Distribution License v1.0 which accompany this distribution.</span></div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno">    7</span><span class="comment"> *</span></div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span><span class="comment"> * The Eclipse Public License is available at</span></div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno">    9</span><span class="comment"> *    https://www.eclipse.org/legal/epl-2.0/</span></div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno">   10</span><span class="comment"> * and the Eclipse Distribution License is available at</span></div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno">   11</span><span class="comment"> *   http://www.eclipse.org/org/documents/edl-v10.php.</span></div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno">   12</span><span class="comment"> *</span></div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span><span class="comment"> * Contributors:</span></div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno">   14</span><span class="comment"> *    Ian Craggs - initial API and implementation and/or initial documentation</span></div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span><span class="comment"> *******************************************************************************/</span></div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno">   16</span> </div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno">   17</span><span class="preprocessor">#if !defined(MQTTPROPERTIES_H)</span></div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno">   18</span><span class="preprocessor">#define MQTTPROPERTIES_H</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno">   19</span> </div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno">   20</span><span class="preprocessor">#include &quot;MQTTExportDeclarations.h&quot;</span></div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno">   21</span> </div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno">   22</span><span class="preprocessor">#include &lt;stdint.h&gt;</span></div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno">   23</span> </div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#afc56d2e8937a0c8f180d68ad93945945">   24</a></span><span class="preprocessor">#define MQTT_INVALID_PROPERTY_ID -2</span></div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno">   25</span> </div>
<div class="foldopen" id="foldopen00027" data-start="{" data-end="};">
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">   27</a></span><span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> {</div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ae5d077520427d03b44096f631411575d">   28</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ae5d077520427d03b44096f631411575d">MQTTPROPERTY_CODE_PAYLOAD_FORMAT_INDICATOR</a> = 1,  </div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a284c0e62d47ee8d358b16a8075632b4a">   29</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a284c0e62d47ee8d358b16a8075632b4a">MQTTPROPERTY_CODE_MESSAGE_EXPIRY_INTERVAL</a> = 2,   </div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a4027d9e0fb53a62ae35963e700b56198">   30</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a4027d9e0fb53a62ae35963e700b56198">MQTTPROPERTY_CODE_CONTENT_TYPE</a> = 3,              </div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a7fa9996eef721d318504fbb0a8d4bac5">   31</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a7fa9996eef721d318504fbb0a8d4bac5">MQTTPROPERTY_CODE_RESPONSE_TOPIC</a> = 8,            </div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a887d3dd3f0ce31255324f5a1ba8b72c5">   32</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a887d3dd3f0ce31255324f5a1ba8b72c5">MQTTPROPERTY_CODE_CORRELATION_DATA</a> = 9,          </div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a70ead9c93f06396a4d9469b65bff0c96">   33</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a70ead9c93f06396a4d9469b65bff0c96">MQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIER</a> = 11,  </div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a22e4caa63f63ca3f9b1c1330711ee766">   34</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a22e4caa63f63ca3f9b1c1330711ee766">MQTTPROPERTY_CODE_SESSION_EXPIRY_INTERVAL</a> = 17,  </div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab5153f683f5a25f6e3a9e3aeb37f1fd6">   35</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab5153f683f5a25f6e3a9e3aeb37f1fd6">MQTTPROPERTY_CODE_ASSIGNED_CLIENT_IDENTIFIER</a> = 18,</div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a768d84858fd18d5d5a7dee394929c672">   36</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a768d84858fd18d5d5a7dee394929c672">MQTTPROPERTY_CODE_ASSIGNED_CLIENT_IDENTIFER</a> = 18,</div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab106f320e7537b79644f25d3efcd68c7">   37</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab106f320e7537b79644f25d3efcd68c7">MQTTPROPERTY_CODE_SERVER_KEEP_ALIVE</a> = 19,        </div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a7c53f1e414b577d787b5d51af3204100">   38</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a7c53f1e414b577d787b5d51af3204100">MQTTPROPERTY_CODE_AUTHENTICATION_METHOD</a> = 21,    </div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4abdf9feec165aceefbe7aa46764f6ab6e">   39</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4abdf9feec165aceefbe7aa46764f6ab6e">MQTTPROPERTY_CODE_AUTHENTICATION_DATA</a> = 22,      </div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a3954daf1d5772b5d56eefa1ab6a28aa1">   40</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a3954daf1d5772b5d56eefa1ab6a28aa1">MQTTPROPERTY_CODE_REQUEST_PROBLEM_INFORMATION</a> = 23,</div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a53fd81bc554f152a2772d282be7ce5ef">   41</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a53fd81bc554f152a2772d282be7ce5ef">MQTTPROPERTY_CODE_WILL_DELAY_INTERVAL</a> = 24,      </div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a420b882a337dc1fd5f336ac6cd0529bf">   42</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a420b882a337dc1fd5f336ac6cd0529bf">MQTTPROPERTY_CODE_REQUEST_RESPONSE_INFORMATION</a> = 25,</div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a2584b050f016af496c7f0b46692dbc00">   43</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a2584b050f016af496c7f0b46692dbc00">MQTTPROPERTY_CODE_RESPONSE_INFORMATION</a> = 26,     </div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a0168e8a59f7994c02b7a7fd2fc3735c4">   44</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a0168e8a59f7994c02b7a7fd2fc3735c4">MQTTPROPERTY_CODE_SERVER_REFERENCE</a> = 28,         </div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a3dce8f679474e901ce4aec076e9e59e1">   45</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a3dce8f679474e901ce4aec076e9e59e1">MQTTPROPERTY_CODE_REASON_STRING</a> = 31,            </div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab2688fe8d7d263c27c00d41776cb8f9f">   46</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab2688fe8d7d263c27c00d41776cb8f9f">MQTTPROPERTY_CODE_RECEIVE_MAXIMUM</a> = 33,          </div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a0a0b0b0715ecc9ccf471c75aa4c21c23">   47</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a0a0b0b0715ecc9ccf471c75aa4c21c23">MQTTPROPERTY_CODE_TOPIC_ALIAS_MAXIMUM</a> = 34,      </div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ad4dfb37d341ea190afc144668e5e3bee">   48</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ad4dfb37d341ea190afc144668e5e3bee">MQTTPROPERTY_CODE_TOPIC_ALIAS</a> = 35,              </div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a506faeb89c407cf78853c777d750fa59">   49</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a506faeb89c407cf78853c777d750fa59">MQTTPROPERTY_CODE_MAXIMUM_QOS</a> = 36,              </div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a448b3a40afaa5f7195701e7dc8bed30c">   50</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a448b3a40afaa5f7195701e7dc8bed30c">MQTTPROPERTY_CODE_RETAIN_AVAILABLE</a> = 37,         </div>
<div class="line"><a id="l00051" name="l00051"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a596ff540370235d3eca693ce30dd4af8">   51</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a596ff540370235d3eca693ce30dd4af8">MQTTPROPERTY_CODE_USER_PROPERTY</a> = 38,            </div>
<div class="line"><a id="l00052" name="l00052"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a6834ea9878f028d5fbdeccaaeae492e5">   52</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a6834ea9878f028d5fbdeccaaeae492e5">MQTTPROPERTY_CODE_MAXIMUM_PACKET_SIZE</a> = 39,      </div>
<div class="line"><a id="l00053" name="l00053"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ad05993f90baaee0ba7094ccef4d378b9">   53</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ad05993f90baaee0ba7094ccef4d378b9">MQTTPROPERTY_CODE_WILDCARD_SUBSCRIPTION_AVAILABLE</a> = 40,</div>
<div class="line"><a id="l00054" name="l00054"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a8b366cfd8bd3f388bafb67f3ebf83505">   54</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a8b366cfd8bd3f388bafb67f3ebf83505">MQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIERS_AVAILABLE</a> = 41,</div>
<div class="line"><a id="l00055" name="l00055"></a><span class="lineno">   55</span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ae04a7356f9e11654f15a3b21f2aae636">MQTTPROPERTY_CODE_SHARED_SUBSCRIPTION_AVAILABLE</a> = 42</div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ae04a7356f9e11654f15a3b21f2aae636">   56</a></span>};</div>
</div>
<div class="line"><a id="l00057" name="l00057"></a><span class="lineno">   57</span> </div>
<div class="line"><a id="l00064" name="l00064"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#a69d277e6a0f27a05279eca2736e09840">   64</a></span>LIBMQTT_API <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_function" href="_m_q_t_t_properties_8h.html#a69d277e6a0f27a05279eca2736e09840">MQTTPropertyName</a>(<span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> value);</div>
<div class="line"><a id="l00065" name="l00065"></a><span class="lineno">   65</span> </div>
<div class="foldopen" id="foldopen00067" data-start="{" data-end="};">
<div class="line"><a id="l00067" name="l00067"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958">   67</a></span><span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958">MQTTPropertyTypes</a> {</div>
<div class="line"><a id="l00068" name="l00068"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ac36f96ce58c98a8ebbe0783df030726a">   68</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ac36f96ce58c98a8ebbe0783df030726a">MQTTPROPERTY_TYPE_BYTE</a>,</div>
<div class="line"><a id="l00069" name="l00069"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ae301a9e68326cc2d8bfefeca401e78e6">   69</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ae301a9e68326cc2d8bfefeca401e78e6">MQTTPROPERTY_TYPE_TWO_BYTE_INTEGER</a>,</div>
<div class="line"><a id="l00070" name="l00070"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958aa49c558733bd735ae872fd87ad0d7e15">   70</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958aa49c558733bd735ae872fd87ad0d7e15">MQTTPROPERTY_TYPE_FOUR_BYTE_INTEGER</a>,</div>
<div class="line"><a id="l00071" name="l00071"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a27bbcb5bc4f584f96612c0cec329c6a7">   71</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a27bbcb5bc4f584f96612c0cec329c6a7">MQTTPROPERTY_TYPE_VARIABLE_BYTE_INTEGER</a>,</div>
<div class="line"><a id="l00072" name="l00072"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a6643aed682b9b07f98159856776fe7b4">   72</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a6643aed682b9b07f98159856776fe7b4">MQTTPROPERTY_TYPE_BINARY_DATA</a>,</div>
<div class="line"><a id="l00073" name="l00073"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ad45c866a5bef6c5048a7af21405734d1">   73</a></span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ad45c866a5bef6c5048a7af21405734d1">MQTTPROPERTY_TYPE_UTF_8_ENCODED_STRING</a>,</div>
<div class="line"><a id="l00074" name="l00074"></a><span class="lineno">   74</span>  <a class="code hl_enumvalue" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a28ab5fe5b159f3b3a8884b0f61527214">MQTTPROPERTY_TYPE_UTF_8_STRING_PAIR</a></div>
<div class="line"><a id="l00075" name="l00075"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a28ab5fe5b159f3b3a8884b0f61527214">   75</a></span>};</div>
</div>
<div class="line"><a id="l00076" name="l00076"></a><span class="lineno">   76</span> </div>
<div class="line"><a id="l00082" name="l00082"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#a7d30ad0520bc9b9366e700d4b493b173">   82</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_properties_8h.html#a7d30ad0520bc9b9366e700d4b493b173">MQTTProperty_getType</a>(<span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> value);</div>
<div class="line"><a id="l00083" name="l00083"></a><span class="lineno">   83</span> </div>
<div class="foldopen" id="foldopen00087" data-start="{" data-end="};">
<div class="line"><a id="l00087" name="l00087"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_len_string.html">   87</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l00088" name="l00088"></a><span class="lineno">   88</span>{</div>
<div class="line"><a id="l00089" name="l00089"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_len_string.html#afed088663f8704004425cdae2120b9b3">   89</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_len_string.html#afed088663f8704004425cdae2120b9b3">len</a>; </div>
<div class="line"><a id="l00090" name="l00090"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_len_string.html#a91a70b77df95bd8b0830b49a094c2acb">   90</a></span>        <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_len_string.html#a91a70b77df95bd8b0830b49a094c2acb">data</a>; </div>
<div class="line"><a id="l00091" name="l00091"></a><span class="lineno">   91</span>} <a class="code hl_struct" href="struct_m_q_t_t_len_string.html">MQTTLenString</a>;</div>
</div>
<div class="line"><a id="l00092" name="l00092"></a><span class="lineno">   92</span> </div>
<div class="line"><a id="l00093" name="l00093"></a><span class="lineno">   93</span> </div>
<div class="foldopen" id="foldopen00097" data-start="{" data-end="};">
<div class="line"><a id="l00097" name="l00097"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_property.html">   97</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l00098" name="l00098"></a><span class="lineno">   98</span>{</div>
<div class="line"><a id="l00099" name="l00099"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_property.html#a2ff04e8cc70fbaa9bcb9a4fb3d510882">   99</a></span>  <span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> <a class="code hl_variable" href="struct_m_q_t_t_property.html#a2ff04e8cc70fbaa9bcb9a4fb3d510882">identifier</a>; </div>
<div class="line"><a id="l00101" name="l00101"></a><span class="lineno">  101</span>  <span class="keyword">union </span>{</div>
<div class="line"><a id="l00102" name="l00102"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_property.html#a1581cde4f73c9a797ae1e7afcc1bb3de">  102</a></span>    <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> <a class="code hl_variable" href="struct_m_q_t_t_property.html#a1581cde4f73c9a797ae1e7afcc1bb3de">byte</a>;       </div>
<div class="line"><a id="l00103" name="l00103"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_property.html#a0289ec2e0df8789139386b0ddf5c71c3">  103</a></span>    <span class="keywordtype">unsigned</span> <span class="keywordtype">short</span> <a class="code hl_variable" href="struct_m_q_t_t_property.html#a0289ec2e0df8789139386b0ddf5c71c3">integer2</a>;  </div>
<div class="line"><a id="l00104" name="l00104"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_property.html#a813425ef31abb5ef0091e3043e8a366b">  104</a></span>    <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_property.html#a813425ef31abb5ef0091e3043e8a366b">integer4</a>;    </div>
<div class="line"><a id="l00105" name="l00105"></a><span class="lineno">  105</span>    <span class="keyword">struct </span>{</div>
<div class="line"><a id="l00106" name="l00106"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_property.html#aa43ebcb9f97210421431a671384ef159">  106</a></span>      <a class="code hl_struct" href="struct_m_q_t_t_len_string.html">MQTTLenString</a> <a class="code hl_variable" href="struct_m_q_t_t_property.html#aa43ebcb9f97210421431a671384ef159">data</a>;  </div>
<div class="line"><a id="l00107" name="l00107"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_property.html#a09e85ff5ad73824d6c2edc1ce4283a17">  107</a></span>      <a class="code hl_struct" href="struct_m_q_t_t_len_string.html">MQTTLenString</a> <a class="code hl_variable" href="struct_m_q_t_t_property.html#a09e85ff5ad73824d6c2edc1ce4283a17">value</a>; </div>
<div class="line"><a id="l00108" name="l00108"></a><span class="lineno">  108</span>    };</div>
<div class="line"><a id="l00109" name="l00109"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_property.html#a51e698f2da26ad8f7c9e3d0b81e188ad">  109</a></span>  } value;</div>
<div class="line"><a id="l00110" name="l00110"></a><span class="lineno">  110</span>} <a class="code hl_struct" href="struct_m_q_t_t_property.html">MQTTProperty</a>;</div>
</div>
<div class="line"><a id="l00111" name="l00111"></a><span class="lineno">  111</span> </div>
<div class="foldopen" id="foldopen00115" data-start="{" data-end="};">
<div class="line"><a id="l00115" name="l00115"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_properties.html">  115</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a></div>
<div class="line"><a id="l00116" name="l00116"></a><span class="lineno">  116</span>{</div>
<div class="line"><a id="l00117" name="l00117"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_properties.html#ad43c3812e6d13e0518d9f8b8f463ffcf">  117</a></span>  <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_properties.html#ad43c3812e6d13e0518d9f8b8f463ffcf">count</a>;     </div>
<div class="line"><a id="l00118" name="l00118"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_properties.html#a8de324382d8fd2f5939bf3372e059383">  118</a></span>  <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_properties.html#a8de324382d8fd2f5939bf3372e059383">max_count</a>; </div>
<div class="line"><a id="l00119" name="l00119"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_properties.html#a9f59b34b1f25fe00023291b678246bcc">  119</a></span>  <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_properties.html#a9f59b34b1f25fe00023291b678246bcc">length</a>;    </div>
<div class="line"><a id="l00120" name="l00120"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_properties.html#a3ac4c38b423393c1553dcf8b71e7dd58">  120</a></span>  <a class="code hl_struct" href="struct_m_q_t_t_property.html">MQTTProperty</a> *<a class="code hl_variable" href="struct_m_q_t_t_properties.html#a3ac4c38b423393c1553dcf8b71e7dd58">array</a>;  </div>
<div class="line"><a id="l00121" name="l00121"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#a7758f1a5eceb6f46c8540630e39e2fb4">  121</a></span>} <a class="code hl_typedef" href="_m_q_t_t_properties_8h.html#a7758f1a5eceb6f46c8540630e39e2fb4">MQTTProperties</a>;</div>
</div>
<div class="line"><a id="l00122" name="l00122"></a><span class="lineno">  122</span> </div>
<div class="line"><a id="l00123" name="l00123"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#a5a80e158486a414ccdfcdd7f75f23988">  123</a></span><span class="preprocessor">#define MQTTProperties_initializer {0, 0, 0, NULL}</span></div>
<div class="line"><a id="l00124" name="l00124"></a><span class="lineno">  124</span> </div>
<div class="line"><a id="l00130" name="l00130"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#a2850f38d4ff89af52999fdc42cdff6fa">  130</a></span><span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_properties_8h.html#a2850f38d4ff89af52999fdc42cdff6fa">MQTTProperties_len</a>(<span class="keyword">const</span> <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* props);</div>
<div class="line"><a id="l00131" name="l00131"></a><span class="lineno">  131</span> </div>
<div class="line"><a id="l00141" name="l00141"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#a88f1d21556c2d23330d71357cd226a15">  141</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_properties_8h.html#a88f1d21556c2d23330d71357cd226a15">MQTTProperties_add</a>(<a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* props, <span class="keyword">const</span> <a class="code hl_struct" href="struct_m_q_t_t_property.html">MQTTProperty</a>* prop);</div>
<div class="line"><a id="l00142" name="l00142"></a><span class="lineno">  142</span> </div>
<div class="line"><a id="l00149" name="l00149"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#ade0027a4e571bd288fe40271ff7aa497">  149</a></span><span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_properties_8h.html#ade0027a4e571bd288fe40271ff7aa497">MQTTProperties_write</a>(<span class="keywordtype">char</span>** pptr, <span class="keyword">const</span> <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* properties);</div>
<div class="line"><a id="l00150" name="l00150"></a><span class="lineno">  150</span> </div>
<div class="line"><a id="l00158" name="l00158"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#afcb874dfcc9f0eaa0b063e2fad740871">  158</a></span><span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_properties_8h.html#afcb874dfcc9f0eaa0b063e2fad740871">MQTTProperties_read</a>(<a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* properties, <span class="keywordtype">char</span>** pptr, <span class="keywordtype">char</span>* enddata);</div>
<div class="line"><a id="l00159" name="l00159"></a><span class="lineno">  159</span> </div>
<div class="line"><a id="l00164" name="l00164"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#ab68247ed365ee51170a9309c828b1823">  164</a></span>LIBMQTT_API <span class="keywordtype">void</span> <a class="code hl_function" href="_m_q_t_t_properties_8h.html#ab68247ed365ee51170a9309c828b1823">MQTTProperties_free</a>(<a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* properties);</div>
<div class="line"><a id="l00165" name="l00165"></a><span class="lineno">  165</span> </div>
<div class="line"><a id="l00171" name="l00171"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#a69b3e474ee2f828e5b827d615fe0fe72">  171</a></span>LIBMQTT_API <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a> <a class="code hl_function" href="_m_q_t_t_properties_8h.html#a69b3e474ee2f828e5b827d615fe0fe72">MQTTProperties_copy</a>(<span class="keyword">const</span> <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* props);</div>
<div class="line"><a id="l00172" name="l00172"></a><span class="lineno">  172</span> </div>
<div class="line"><a id="l00179" name="l00179"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#abde30a2a44f41c649bd84f4d1467b72c">  179</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_properties_8h.html#abde30a2a44f41c649bd84f4d1467b72c">MQTTProperties_hasProperty</a>(<span class="keyword">const</span> <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props, <span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> propid);</div>
<div class="line"><a id="l00180" name="l00180"></a><span class="lineno">  180</span> </div>
<div class="line"><a id="l00188" name="l00188"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#ac7ea96a57ad09e9d0fc3203d008f52aa">  188</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_properties_8h.html#ac7ea96a57ad09e9d0fc3203d008f52aa">MQTTProperties_propertyCount</a>(<span class="keyword">const</span> <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props, <span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> propid);</div>
<div class="line"><a id="l00189" name="l00189"></a><span class="lineno">  189</span> </div>
<div class="line"><a id="l00196" name="l00196"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#ad8643f0e68deb29e16ef88fc225e03c2">  196</a></span>LIBMQTT_API int64_t <a class="code hl_function" href="_m_q_t_t_properties_8h.html#ad8643f0e68deb29e16ef88fc225e03c2">MQTTProperties_getNumericValue</a>(<span class="keyword">const</span> <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props, <span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> propid);</div>
<div class="line"><a id="l00197" name="l00197"></a><span class="lineno">  197</span> </div>
<div class="line"><a id="l00206" name="l00206"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#a1d4f3fd86fc47241fefe623556d99ea9">  206</a></span>LIBMQTT_API int64_t <a class="code hl_function" href="_m_q_t_t_properties_8h.html#a1d4f3fd86fc47241fefe623556d99ea9">MQTTProperties_getNumericValueAt</a>(<span class="keyword">const</span> <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props, <span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> propid, <span class="keywordtype">int</span> index);</div>
<div class="line"><a id="l00207" name="l00207"></a><span class="lineno">  207</span> </div>
<div class="line"><a id="l00214" name="l00214"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#ac5c49930de80af1e0df0f0584f142078">  214</a></span>LIBMQTT_API <a class="code hl_struct" href="struct_m_q_t_t_property.html">MQTTProperty</a>* <a class="code hl_function" href="_m_q_t_t_properties_8h.html#ac5c49930de80af1e0df0f0584f142078">MQTTProperties_getProperty</a>(<span class="keyword">const</span> <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props, <span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> propid);</div>
<div class="line"><a id="l00215" name="l00215"></a><span class="lineno">  215</span> </div>
<div class="line"><a id="l00223" name="l00223"></a><span class="lineno"><a class="line" href="_m_q_t_t_properties_8h.html#a345b64fc33afd05148aa018c24c42c80">  223</a></span>LIBMQTT_API <a class="code hl_struct" href="struct_m_q_t_t_property.html">MQTTProperty</a>* <a class="code hl_function" href="_m_q_t_t_properties_8h.html#a345b64fc33afd05148aa018c24c42c80">MQTTProperties_getPropertyAt</a>(<span class="keyword">const</span> <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props, <span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> propid, <span class="keywordtype">int</span> index);</div>
<div class="line"><a id="l00224" name="l00224"></a><span class="lineno">  224</span> </div>
<div class="line"><a id="l00225" name="l00225"></a><span class="lineno">  225</span><span class="preprocessor">#endif </span><span class="comment">/* MQTTPROPERTIES_H */</span><span class="preprocessor"></span></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_a1d4f3fd86fc47241fefe623556d99ea9"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#a1d4f3fd86fc47241fefe623556d99ea9">MQTTProperties_getNumericValueAt</a></div><div class="ttdeci">int64_t MQTTProperties_getNumericValueAt(const MQTTProperties *props, enum MQTTPropertyCodes propid, int index)</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_a2850f38d4ff89af52999fdc42cdff6fa"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#a2850f38d4ff89af52999fdc42cdff6fa">MQTTProperties_len</a></div><div class="ttdeci">int MQTTProperties_len(const MQTTProperties *props)</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_a345b64fc33afd05148aa018c24c42c80"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#a345b64fc33afd05148aa018c24c42c80">MQTTProperties_getPropertyAt</a></div><div class="ttdeci">MQTTProperty * MQTTProperties_getPropertyAt(const MQTTProperties *props, enum MQTTPropertyCodes propid, int index)</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_a69b3e474ee2f828e5b827d615fe0fe72"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#a69b3e474ee2f828e5b827d615fe0fe72">MQTTProperties_copy</a></div><div class="ttdeci">MQTTProperties MQTTProperties_copy(const MQTTProperties *props)</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_a69d277e6a0f27a05279eca2736e09840"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#a69d277e6a0f27a05279eca2736e09840">MQTTPropertyName</a></div><div class="ttdeci">const char * MQTTPropertyName(enum MQTTPropertyCodes value)</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_a7758f1a5eceb6f46c8540630e39e2fb4"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#a7758f1a5eceb6f46c8540630e39e2fb4">MQTTProperties</a></div><div class="ttdeci">struct MQTTProperties MQTTProperties</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_a7d30ad0520bc9b9366e700d4b493b173"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#a7d30ad0520bc9b9366e700d4b493b173">MQTTProperty_getType</a></div><div class="ttdeci">int MQTTProperty_getType(enum MQTTPropertyCodes value)</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_a88f1d21556c2d23330d71357cd226a15"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#a88f1d21556c2d23330d71357cd226a15">MQTTProperties_add</a></div><div class="ttdeci">int MQTTProperties_add(MQTTProperties *props, const MQTTProperty *prop)</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_a942f52ef7c232829f6df5c86e07cc958"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958">MQTTPropertyTypes</a></div><div class="ttdeci">MQTTPropertyTypes</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:67</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_a942f52ef7c232829f6df5c86e07cc958a27bbcb5bc4f584f96612c0cec329c6a7"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a27bbcb5bc4f584f96612c0cec329c6a7">MQTTPROPERTY_TYPE_VARIABLE_BYTE_INTEGER</a></div><div class="ttdeci">@ MQTTPROPERTY_TYPE_VARIABLE_BYTE_INTEGER</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:71</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_a942f52ef7c232829f6df5c86e07cc958a28ab5fe5b159f3b3a8884b0f61527214"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a28ab5fe5b159f3b3a8884b0f61527214">MQTTPROPERTY_TYPE_UTF_8_STRING_PAIR</a></div><div class="ttdeci">@ MQTTPROPERTY_TYPE_UTF_8_STRING_PAIR</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:74</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_a942f52ef7c232829f6df5c86e07cc958a6643aed682b9b07f98159856776fe7b4"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a6643aed682b9b07f98159856776fe7b4">MQTTPROPERTY_TYPE_BINARY_DATA</a></div><div class="ttdeci">@ MQTTPROPERTY_TYPE_BINARY_DATA</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:72</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_a942f52ef7c232829f6df5c86e07cc958aa49c558733bd735ae872fd87ad0d7e15"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958aa49c558733bd735ae872fd87ad0d7e15">MQTTPROPERTY_TYPE_FOUR_BYTE_INTEGER</a></div><div class="ttdeci">@ MQTTPROPERTY_TYPE_FOUR_BYTE_INTEGER</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:70</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_a942f52ef7c232829f6df5c86e07cc958ac36f96ce58c98a8ebbe0783df030726a"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ac36f96ce58c98a8ebbe0783df030726a">MQTTPROPERTY_TYPE_BYTE</a></div><div class="ttdeci">@ MQTTPROPERTY_TYPE_BYTE</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:68</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_a942f52ef7c232829f6df5c86e07cc958ad45c866a5bef6c5048a7af21405734d1"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ad45c866a5bef6c5048a7af21405734d1">MQTTPROPERTY_TYPE_UTF_8_ENCODED_STRING</a></div><div class="ttdeci">@ MQTTPROPERTY_TYPE_UTF_8_ENCODED_STRING</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:73</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_a942f52ef7c232829f6df5c86e07cc958ae301a9e68326cc2d8bfefeca401e78e6"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ae301a9e68326cc2d8bfefeca401e78e6">MQTTPROPERTY_TYPE_TWO_BYTE_INTEGER</a></div><div class="ttdeci">@ MQTTPROPERTY_TYPE_TWO_BYTE_INTEGER</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:69</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_ab68247ed365ee51170a9309c828b1823"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#ab68247ed365ee51170a9309c828b1823">MQTTProperties_free</a></div><div class="ttdeci">void MQTTProperties_free(MQTTProperties *properties)</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_abde30a2a44f41c649bd84f4d1467b72c"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#abde30a2a44f41c649bd84f4d1467b72c">MQTTProperties_hasProperty</a></div><div class="ttdeci">int MQTTProperties_hasProperty(const MQTTProperties *props, enum MQTTPropertyCodes propid)</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_ac5c49930de80af1e0df0f0584f142078"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#ac5c49930de80af1e0df0f0584f142078">MQTTProperties_getProperty</a></div><div class="ttdeci">MQTTProperty * MQTTProperties_getProperty(const MQTTProperties *props, enum MQTTPropertyCodes propid)</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_ac7ea96a57ad09e9d0fc3203d008f52aa"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#ac7ea96a57ad09e9d0fc3203d008f52aa">MQTTProperties_propertyCount</a></div><div class="ttdeci">int MQTTProperties_propertyCount(const MQTTProperties *props, enum MQTTPropertyCodes propid)</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_ad8643f0e68deb29e16ef88fc225e03c2"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#ad8643f0e68deb29e16ef88fc225e03c2">MQTTProperties_getNumericValue</a></div><div class="ttdeci">int64_t MQTTProperties_getNumericValue(const MQTTProperties *props, enum MQTTPropertyCodes propid)</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_ade0027a4e571bd288fe40271ff7aa497"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#ade0027a4e571bd288fe40271ff7aa497">MQTTProperties_write</a></div><div class="ttdeci">int MQTTProperties_write(char **pptr, const MQTTProperties *properties)</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a></div><div class="ttdeci">MQTTPropertyCodes</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:27</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a0168e8a59f7994c02b7a7fd2fc3735c4"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a0168e8a59f7994c02b7a7fd2fc3735c4">MQTTPROPERTY_CODE_SERVER_REFERENCE</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_SERVER_REFERENCE</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:44</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a0a0b0b0715ecc9ccf471c75aa4c21c23"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a0a0b0b0715ecc9ccf471c75aa4c21c23">MQTTPROPERTY_CODE_TOPIC_ALIAS_MAXIMUM</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_TOPIC_ALIAS_MAXIMUM</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:47</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a22e4caa63f63ca3f9b1c1330711ee766"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a22e4caa63f63ca3f9b1c1330711ee766">MQTTPROPERTY_CODE_SESSION_EXPIRY_INTERVAL</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_SESSION_EXPIRY_INTERVAL</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:34</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a2584b050f016af496c7f0b46692dbc00"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a2584b050f016af496c7f0b46692dbc00">MQTTPROPERTY_CODE_RESPONSE_INFORMATION</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_RESPONSE_INFORMATION</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:43</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a284c0e62d47ee8d358b16a8075632b4a"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a284c0e62d47ee8d358b16a8075632b4a">MQTTPROPERTY_CODE_MESSAGE_EXPIRY_INTERVAL</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_MESSAGE_EXPIRY_INTERVAL</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:29</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a3954daf1d5772b5d56eefa1ab6a28aa1"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a3954daf1d5772b5d56eefa1ab6a28aa1">MQTTPROPERTY_CODE_REQUEST_PROBLEM_INFORMATION</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_REQUEST_PROBLEM_INFORMATION</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:40</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a3dce8f679474e901ce4aec076e9e59e1"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a3dce8f679474e901ce4aec076e9e59e1">MQTTPROPERTY_CODE_REASON_STRING</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_REASON_STRING</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:45</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a4027d9e0fb53a62ae35963e700b56198"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a4027d9e0fb53a62ae35963e700b56198">MQTTPROPERTY_CODE_CONTENT_TYPE</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_CONTENT_TYPE</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:30</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a420b882a337dc1fd5f336ac6cd0529bf"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a420b882a337dc1fd5f336ac6cd0529bf">MQTTPROPERTY_CODE_REQUEST_RESPONSE_INFORMATION</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_REQUEST_RESPONSE_INFORMATION</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:42</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a448b3a40afaa5f7195701e7dc8bed30c"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a448b3a40afaa5f7195701e7dc8bed30c">MQTTPROPERTY_CODE_RETAIN_AVAILABLE</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_RETAIN_AVAILABLE</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:50</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a506faeb89c407cf78853c777d750fa59"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a506faeb89c407cf78853c777d750fa59">MQTTPROPERTY_CODE_MAXIMUM_QOS</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_MAXIMUM_QOS</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:49</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a53fd81bc554f152a2772d282be7ce5ef"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a53fd81bc554f152a2772d282be7ce5ef">MQTTPROPERTY_CODE_WILL_DELAY_INTERVAL</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_WILL_DELAY_INTERVAL</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:41</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a596ff540370235d3eca693ce30dd4af8"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a596ff540370235d3eca693ce30dd4af8">MQTTPROPERTY_CODE_USER_PROPERTY</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_USER_PROPERTY</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:51</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a6834ea9878f028d5fbdeccaaeae492e5"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a6834ea9878f028d5fbdeccaaeae492e5">MQTTPROPERTY_CODE_MAXIMUM_PACKET_SIZE</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_MAXIMUM_PACKET_SIZE</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:52</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a70ead9c93f06396a4d9469b65bff0c96"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a70ead9c93f06396a4d9469b65bff0c96">MQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIER</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIER</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:33</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a768d84858fd18d5d5a7dee394929c672"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a768d84858fd18d5d5a7dee394929c672">MQTTPROPERTY_CODE_ASSIGNED_CLIENT_IDENTIFER</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_ASSIGNED_CLIENT_IDENTIFER</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:36</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a7c53f1e414b577d787b5d51af3204100"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a7c53f1e414b577d787b5d51af3204100">MQTTPROPERTY_CODE_AUTHENTICATION_METHOD</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_AUTHENTICATION_METHOD</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:38</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a7fa9996eef721d318504fbb0a8d4bac5"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a7fa9996eef721d318504fbb0a8d4bac5">MQTTPROPERTY_CODE_RESPONSE_TOPIC</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_RESPONSE_TOPIC</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:31</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a887d3dd3f0ce31255324f5a1ba8b72c5"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a887d3dd3f0ce31255324f5a1ba8b72c5">MQTTPROPERTY_CODE_CORRELATION_DATA</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_CORRELATION_DATA</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:32</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4a8b366cfd8bd3f388bafb67f3ebf83505"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a8b366cfd8bd3f388bafb67f3ebf83505">MQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIERS_AVAILABLE</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIERS_AVAILABLE</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:54</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4ab106f320e7537b79644f25d3efcd68c7"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab106f320e7537b79644f25d3efcd68c7">MQTTPROPERTY_CODE_SERVER_KEEP_ALIVE</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_SERVER_KEEP_ALIVE</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:37</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4ab2688fe8d7d263c27c00d41776cb8f9f"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab2688fe8d7d263c27c00d41776cb8f9f">MQTTPROPERTY_CODE_RECEIVE_MAXIMUM</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_RECEIVE_MAXIMUM</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:46</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4ab5153f683f5a25f6e3a9e3aeb37f1fd6"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab5153f683f5a25f6e3a9e3aeb37f1fd6">MQTTPROPERTY_CODE_ASSIGNED_CLIENT_IDENTIFIER</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_ASSIGNED_CLIENT_IDENTIFIER</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:35</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4abdf9feec165aceefbe7aa46764f6ab6e"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4abdf9feec165aceefbe7aa46764f6ab6e">MQTTPROPERTY_CODE_AUTHENTICATION_DATA</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_AUTHENTICATION_DATA</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:39</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4ad05993f90baaee0ba7094ccef4d378b9"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ad05993f90baaee0ba7094ccef4d378b9">MQTTPROPERTY_CODE_WILDCARD_SUBSCRIPTION_AVAILABLE</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_WILDCARD_SUBSCRIPTION_AVAILABLE</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:53</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4ad4dfb37d341ea190afc144668e5e3bee"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ad4dfb37d341ea190afc144668e5e3bee">MQTTPROPERTY_CODE_TOPIC_ALIAS</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_TOPIC_ALIAS</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:48</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4ae04a7356f9e11654f15a3b21f2aae636"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ae04a7356f9e11654f15a3b21f2aae636">MQTTPROPERTY_CODE_SHARED_SUBSCRIPTION_AVAILABLE</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_SHARED_SUBSCRIPTION_AVAILABLE</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:55</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_af623c1b670dfe3fda633c068e054d8b4ae5d077520427d03b44096f631411575d"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ae5d077520427d03b44096f631411575d">MQTTPROPERTY_CODE_PAYLOAD_FORMAT_INDICATOR</a></div><div class="ttdeci">@ MQTTPROPERTY_CODE_PAYLOAD_FORMAT_INDICATOR</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:28</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html_afcb874dfcc9f0eaa0b063e2fad740871"><div class="ttname"><a href="_m_q_t_t_properties_8h.html#afcb874dfcc9f0eaa0b063e2fad740871">MQTTProperties_read</a></div><div class="ttdeci">int MQTTProperties_read(MQTTProperties *properties, char **pptr, char *enddata)</div></div>
<div class="ttc" id="astruct_m_q_t_t_len_string_html"><div class="ttname"><a href="struct_m_q_t_t_len_string.html">MQTTLenString</a></div><div class="ttdef"><b>Definition</b> MQTTProperties.h:88</div></div>
<div class="ttc" id="astruct_m_q_t_t_len_string_html_a91a70b77df95bd8b0830b49a094c2acb"><div class="ttname"><a href="struct_m_q_t_t_len_string.html#a91a70b77df95bd8b0830b49a094c2acb">MQTTLenString::data</a></div><div class="ttdeci">char * data</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:90</div></div>
<div class="ttc" id="astruct_m_q_t_t_len_string_html_afed088663f8704004425cdae2120b9b3"><div class="ttname"><a href="struct_m_q_t_t_len_string.html#afed088663f8704004425cdae2120b9b3">MQTTLenString::len</a></div><div class="ttdeci">int len</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:89</div></div>
<div class="ttc" id="astruct_m_q_t_t_properties_html"><div class="ttname"><a href="struct_m_q_t_t_properties.html">MQTTProperties</a></div><div class="ttdef"><b>Definition</b> MQTTProperties.h:116</div></div>
<div class="ttc" id="astruct_m_q_t_t_properties_html_a3ac4c38b423393c1553dcf8b71e7dd58"><div class="ttname"><a href="struct_m_q_t_t_properties.html#a3ac4c38b423393c1553dcf8b71e7dd58">MQTTProperties::array</a></div><div class="ttdeci">MQTTProperty * array</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:120</div></div>
<div class="ttc" id="astruct_m_q_t_t_properties_html_a8de324382d8fd2f5939bf3372e059383"><div class="ttname"><a href="struct_m_q_t_t_properties.html#a8de324382d8fd2f5939bf3372e059383">MQTTProperties::max_count</a></div><div class="ttdeci">int max_count</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:118</div></div>
<div class="ttc" id="astruct_m_q_t_t_properties_html_a9f59b34b1f25fe00023291b678246bcc"><div class="ttname"><a href="struct_m_q_t_t_properties.html#a9f59b34b1f25fe00023291b678246bcc">MQTTProperties::length</a></div><div class="ttdeci">int length</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:119</div></div>
<div class="ttc" id="astruct_m_q_t_t_properties_html_ad43c3812e6d13e0518d9f8b8f463ffcf"><div class="ttname"><a href="struct_m_q_t_t_properties.html#ad43c3812e6d13e0518d9f8b8f463ffcf">MQTTProperties::count</a></div><div class="ttdeci">int count</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:117</div></div>
<div class="ttc" id="astruct_m_q_t_t_property_html"><div class="ttname"><a href="struct_m_q_t_t_property.html">MQTTProperty</a></div><div class="ttdef"><b>Definition</b> MQTTProperties.h:98</div></div>
<div class="ttc" id="astruct_m_q_t_t_property_html_a0289ec2e0df8789139386b0ddf5c71c3"><div class="ttname"><a href="struct_m_q_t_t_property.html#a0289ec2e0df8789139386b0ddf5c71c3">MQTTProperty::integer2</a></div><div class="ttdeci">unsigned short integer2</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:103</div></div>
<div class="ttc" id="astruct_m_q_t_t_property_html_a09e85ff5ad73824d6c2edc1ce4283a17"><div class="ttname"><a href="struct_m_q_t_t_property.html#a09e85ff5ad73824d6c2edc1ce4283a17">MQTTProperty::value</a></div><div class="ttdeci">MQTTLenString value</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:107</div></div>
<div class="ttc" id="astruct_m_q_t_t_property_html_a1581cde4f73c9a797ae1e7afcc1bb3de"><div class="ttname"><a href="struct_m_q_t_t_property.html#a1581cde4f73c9a797ae1e7afcc1bb3de">MQTTProperty::byte</a></div><div class="ttdeci">unsigned char byte</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:102</div></div>
<div class="ttc" id="astruct_m_q_t_t_property_html_a2ff04e8cc70fbaa9bcb9a4fb3d510882"><div class="ttname"><a href="struct_m_q_t_t_property.html#a2ff04e8cc70fbaa9bcb9a4fb3d510882">MQTTProperty::identifier</a></div><div class="ttdeci">enum MQTTPropertyCodes identifier</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:99</div></div>
<div class="ttc" id="astruct_m_q_t_t_property_html_a813425ef31abb5ef0091e3043e8a366b"><div class="ttname"><a href="struct_m_q_t_t_property.html#a813425ef31abb5ef0091e3043e8a366b">MQTTProperty::integer4</a></div><div class="ttdeci">unsigned int integer4</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:104</div></div>
<div class="ttc" id="astruct_m_q_t_t_property_html_aa43ebcb9f97210421431a671384ef159"><div class="ttname"><a href="struct_m_q_t_t_property.html#aa43ebcb9f97210421431a671384ef159">MQTTProperty::data</a></div><div class="ttdeci">MQTTLenString data</div><div class="ttdef"><b>Definition</b> MQTTProperties.h:106</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:06 for Paho MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
