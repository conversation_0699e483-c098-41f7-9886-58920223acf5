<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho MQTT C Client Library: MQTTClient.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#define-members">Macros</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#enum-members">Enumerations</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">MQTTClient.h File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &quot;MQTTExportDeclarations.h&quot;</code><br />
<code>#include &quot;<a class="el" href="_m_q_t_t_properties_8h_source.html">MQTTProperties.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="_m_q_t_t_reason_codes_8h_source.html">MQTTReasonCodes.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="_m_q_t_t_subscribe_opts_8h_source.html">MQTTSubscribeOpts.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="_m_q_t_t_client_persistence_8h_source.html">MQTTClientPersistence.h</a>&quot;</code><br />
</div>
<p><a href="_m_q_t_t_client_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_client__init__options.html">MQTTClient_init_options</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_client__create_options.html">MQTTClient_createOptions</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_client__will_options.html">MQTTClient_willOptions</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_client___s_s_l_options.html">MQTTClient_SSLOptions</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_client__name_value.html">MQTTClient_nameValue</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_client__connect_options.html">MQTTClient_connectOptions</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_response.html">MQTTResponse</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:acba095704d79e5a1996389fa26203f73" id="r_acba095704d79e5a1996389fa26203f73"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:acba095704d79e5a1996389fa26203f73"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af33a6d6c0e8a6a747bf39638e0bba36b" id="r_af33a6d6c0e8a6a747bf39638e0bba36b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af33a6d6c0e8a6a747bf39638e0bba36b">MQTTCLIENT_FAILURE</a>&#160;&#160;&#160;-1</td></tr>
<tr class="separator:af33a6d6c0e8a6a747bf39638e0bba36b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a561d053311cb492cf7226f419ee0d516" id="r_a561d053311cb492cf7226f419ee0d516"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a561d053311cb492cf7226f419ee0d516">MQTTCLIENT_DISCONNECTED</a>&#160;&#160;&#160;-3</td></tr>
<tr class="separator:a561d053311cb492cf7226f419ee0d516"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8fc442fc2e9dfb422a163ab1fa02e0cb" id="r_a8fc442fc2e9dfb422a163ab1fa02e0cb"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8fc442fc2e9dfb422a163ab1fa02e0cb">MQTTCLIENT_MAX_MESSAGES_INFLIGHT</a>&#160;&#160;&#160;-4</td></tr>
<tr class="separator:a8fc442fc2e9dfb422a163ab1fa02e0cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a678a4744192de9c8dca220d9965809dd" id="r_a678a4744192de9c8dca220d9965809dd"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a678a4744192de9c8dca220d9965809dd">MQTTCLIENT_BAD_UTF8_STRING</a>&#160;&#160;&#160;-5</td></tr>
<tr class="separator:a678a4744192de9c8dca220d9965809dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac3232abd7f86bbba26faea0e2b132c3c" id="r_ac3232abd7f86bbba26faea0e2b132c3c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac3232abd7f86bbba26faea0e2b132c3c">MQTTCLIENT_NULL_PARAMETER</a>&#160;&#160;&#160;-6</td></tr>
<tr class="separator:ac3232abd7f86bbba26faea0e2b132c3c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a29afebfce0bdf6cda1e37abc0c4b6690" id="r_a29afebfce0bdf6cda1e37abc0c4b6690"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a29afebfce0bdf6cda1e37abc0c4b6690">MQTTCLIENT_TOPICNAME_TRUNCATED</a>&#160;&#160;&#160;-7</td></tr>
<tr class="separator:a29afebfce0bdf6cda1e37abc0c4b6690"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a747615d8064e3fe024ae5565ec63e1ce" id="r_a747615d8064e3fe024ae5565ec63e1ce"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a747615d8064e3fe024ae5565ec63e1ce">MQTTCLIENT_BAD_STRUCTURE</a>&#160;&#160;&#160;-8</td></tr>
<tr class="separator:a747615d8064e3fe024ae5565ec63e1ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a51cc8ca032acf4ae14f83996524b8cdc" id="r_a51cc8ca032acf4ae14f83996524b8cdc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a51cc8ca032acf4ae14f83996524b8cdc">MQTTCLIENT_BAD_QOS</a>&#160;&#160;&#160;-9</td></tr>
<tr class="separator:a51cc8ca032acf4ae14f83996524b8cdc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1c67fc83ba1a8f26236aa49b127bdb61" id="r_a1c67fc83ba1a8f26236aa49b127bdb61"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1c67fc83ba1a8f26236aa49b127bdb61">MQTTCLIENT_SSL_NOT_SUPPORTED</a>&#160;&#160;&#160;-10</td></tr>
<tr class="separator:a1c67fc83ba1a8f26236aa49b127bdb61"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab84cecd25638896eb45b8f5ffd82bf7" id="r_aab84cecd25638896eb45b8f5ffd82bf7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aab84cecd25638896eb45b8f5ffd82bf7">MQTTCLIENT_BAD_MQTT_VERSION</a>&#160;&#160;&#160;-11</td></tr>
<tr class="separator:aab84cecd25638896eb45b8f5ffd82bf7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1d0cb25b450136f036a238546487344a" id="r_a1d0cb25b450136f036a238546487344a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1d0cb25b450136f036a238546487344a">MQTTCLIENT_BAD_PROTOCOL</a>&#160;&#160;&#160;-14</td></tr>
<tr class="separator:a1d0cb25b450136f036a238546487344a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1babaca56ffae802fa1e246a2649927e" id="r_a1babaca56ffae802fa1e246a2649927e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1babaca56ffae802fa1e246a2649927e">MQTTCLIENT_BAD_MQTT_OPTION</a>&#160;&#160;&#160;-15</td></tr>
<tr class="separator:a1babaca56ffae802fa1e246a2649927e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae9070d21de569f999a9575049cdd6da1" id="r_ae9070d21de569f999a9575049cdd6da1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae9070d21de569f999a9575049cdd6da1">MQTTCLIENT_WRONG_MQTT_VERSION</a>&#160;&#160;&#160;-16</td></tr>
<tr class="separator:ae9070d21de569f999a9575049cdd6da1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aacf90ba5292e25122e6fd5ec2a38efe5" id="r_aacf90ba5292e25122e6fd5ec2a38efe5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aacf90ba5292e25122e6fd5ec2a38efe5">MQTTCLIENT_0_LEN_WILL_TOPIC</a>&#160;&#160;&#160;-17</td></tr>
<tr class="separator:aacf90ba5292e25122e6fd5ec2a38efe5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a75b80b01f98d5a1ffa2a4d42995a8397" id="r_a75b80b01f98d5a1ffa2a4d42995a8397"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a75b80b01f98d5a1ffa2a4d42995a8397">MQTTVERSION_DEFAULT</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:a75b80b01f98d5a1ffa2a4d42995a8397"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4603b988e76872e1f23f135d225ce2fb" id="r_a4603b988e76872e1f23f135d225ce2fb"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4603b988e76872e1f23f135d225ce2fb">MQTTVERSION_3_1</a>&#160;&#160;&#160;3</td></tr>
<tr class="separator:a4603b988e76872e1f23f135d225ce2fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac79cc6fdeaa9e3f4ee12c3418898b1ef" id="r_ac79cc6fdeaa9e3f4ee12c3418898b1ef"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac79cc6fdeaa9e3f4ee12c3418898b1ef">MQTTVERSION_3_1_1</a>&#160;&#160;&#160;4</td></tr>
<tr class="separator:ac79cc6fdeaa9e3f4ee12c3418898b1ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af8b176fa4d5b89789767ce972338e1e3" id="r_af8b176fa4d5b89789767ce972338e1e3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af8b176fa4d5b89789767ce972338e1e3">MQTTVERSION_5</a>&#160;&#160;&#160;5</td></tr>
<tr class="separator:af8b176fa4d5b89789767ce972338e1e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ade337b363b7f4bc7c1a7b2858e0380bd" id="r_ade337b363b7f4bc7c1a7b2858e0380bd"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ade337b363b7f4bc7c1a7b2858e0380bd">MQTT_BAD_SUBSCRIBE</a>&#160;&#160;&#160;0x80</td></tr>
<tr class="separator:ade337b363b7f4bc7c1a7b2858e0380bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac17057c8c22c0717d3adf4e040440f73" id="r_ac17057c8c22c0717d3adf4e040440f73"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac17057c8c22c0717d3adf4e040440f73">MQTTClient_init_options_initializer</a>&#160;&#160;&#160;{ {'M', 'Q', 'T', 'G'}, 0, 0 }</td></tr>
<tr class="separator:ac17057c8c22c0717d3adf4e040440f73"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa1fd995924d3df75959fcf57e87aefac" id="r_aa1fd995924d3df75959fcf57e87aefac"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa1fd995924d3df75959fcf57e87aefac">MQTTClient_message_initializer</a>&#160;&#160;&#160;{ {'M', 'Q', 'T', 'M'}, 1, 0, NULL, 0, 0, 0, 0, <a class="el" href="_m_q_t_t_properties_8h.html#a5a80e158486a414ccdfcdd7f75f23988">MQTTProperties_initializer</a> }</td></tr>
<tr class="separator:aa1fd995924d3df75959fcf57e87aefac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a763e477a5aceb6aff279111c7693e691" id="r_a763e477a5aceb6aff279111c7693e691"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a763e477a5aceb6aff279111c7693e691">MQTTClient_createOptions_initializer</a>&#160;&#160;&#160;{ {'M', 'Q', 'C', 'O'}, 0, <a class="el" href="#a75b80b01f98d5a1ffa2a4d42995a8397">MQTTVERSION_DEFAULT</a> }</td></tr>
<tr class="separator:a763e477a5aceb6aff279111c7693e691"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aae0811659c59f5dad0467544f91645eb" id="r_aae0811659c59f5dad0467544f91645eb"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aae0811659c59f5dad0467544f91645eb">MQTTClient_willOptions_initializer</a>&#160;&#160;&#160;{ {'M', 'Q', 'T', 'W'}, 1, NULL, NULL, 0, 0, {0, NULL} }</td></tr>
<tr class="separator:aae0811659c59f5dad0467544f91645eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2549ea897af26c76198284731db9e721" id="r_a2549ea897af26c76198284731db9e721"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2549ea897af26c76198284731db9e721">MQTT_SSL_VERSION_DEFAULT</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:a2549ea897af26c76198284731db9e721"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7e5da3d6f0d2b53409bbfcf6e56f3d2d" id="r_a7e5da3d6f0d2b53409bbfcf6e56f3d2d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7e5da3d6f0d2b53409bbfcf6e56f3d2d">MQTT_SSL_VERSION_TLS_1_0</a>&#160;&#160;&#160;1</td></tr>
<tr class="separator:a7e5da3d6f0d2b53409bbfcf6e56f3d2d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abdff87efa3f2ee473a1591e10638b537" id="r_abdff87efa3f2ee473a1591e10638b537"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abdff87efa3f2ee473a1591e10638b537">MQTT_SSL_VERSION_TLS_1_1</a>&#160;&#160;&#160;2</td></tr>
<tr class="separator:abdff87efa3f2ee473a1591e10638b537"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3a94dbdeafbb73c73a068e7c2085fbab" id="r_a3a94dbdeafbb73c73a068e7c2085fbab"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3a94dbdeafbb73c73a068e7c2085fbab">MQTT_SSL_VERSION_TLS_1_2</a>&#160;&#160;&#160;3</td></tr>
<tr class="separator:a3a94dbdeafbb73c73a068e7c2085fbab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab9b2a2c6b52dbb2ac842ad99a9ce6d99" id="r_ab9b2a2c6b52dbb2ac842ad99a9ce6d99"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab9b2a2c6b52dbb2ac842ad99a9ce6d99">MQTTClient_SSLOptions_initializer</a>&#160;&#160;&#160;{ {'M', 'Q', 'T', 'S'}, 5, NULL, NULL, NULL, NULL, NULL, 1, <a class="el" href="#a2549ea897af26c76198284731db9e721">MQTT_SSL_VERSION_DEFAULT</a>, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0 }</td></tr>
<tr class="separator:ab9b2a2c6b52dbb2ac842ad99a9ce6d99"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aefd7c865f2641c8155b763fdf3061c25" id="r_aefd7c865f2641c8155b763fdf3061c25"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aefd7c865f2641c8155b763fdf3061c25">MQTTClient_connectOptions_initializer</a></td></tr>
<tr class="separator:aefd7c865f2641c8155b763fdf3061c25"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1f0c7608262ac9c00cb94e9c8f9fc984" id="r_a1f0c7608262ac9c00cb94e9c8f9fc984"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1f0c7608262ac9c00cb94e9c8f9fc984">MQTTClient_connectOptions_initializer5</a></td></tr>
<tr class="separator:a1f0c7608262ac9c00cb94e9c8f9fc984"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0a98fda162a78ee8c8cbd7d9d39494f4" id="r_a0a98fda162a78ee8c8cbd7d9d39494f4"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0a98fda162a78ee8c8cbd7d9d39494f4">MQTTClient_connectOptions_initializer_ws</a></td></tr>
<tr class="separator:a0a98fda162a78ee8c8cbd7d9d39494f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8c37c9f77f0b67e2520c8f91acf1afea" id="r_a8c37c9f77f0b67e2520c8f91acf1afea"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8c37c9f77f0b67e2520c8f91acf1afea">MQTTClient_connectOptions_initializer5_ws</a></td></tr>
<tr class="separator:a8c37c9f77f0b67e2520c8f91acf1afea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a17f171200136bcfa933eb50ef21531a7" id="r_a17f171200136bcfa933eb50ef21531a7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a17f171200136bcfa933eb50ef21531a7">MQTTResponse_initializer</a>&#160;&#160;&#160;{1, <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969">MQTTREASONCODE_SUCCESS</a>, 0, NULL, NULL}</td></tr>
<tr class="separator:a17f171200136bcfa933eb50ef21531a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:a7649e3913f9a216424d296f88a969c59" id="r_a7649e3913f9a216424d296f88a969c59"><td class="memItemLeft" align="right" valign="top">typedef void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td></tr>
<tr class="separator:a7649e3913f9a216424d296f88a969c59"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a73e49030fd8b7074aa1aa45669b7fe8d" id="r_a73e49030fd8b7074aa1aa45669b7fe8d"><td class="memItemLeft" align="right" valign="top">typedef int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a></td></tr>
<tr class="separator:a73e49030fd8b7074aa1aa45669b7fe8d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b2beb5227708f8127b666f5a7fc41b3" id="r_a8b2beb5227708f8127b666f5a7fc41b3"><td class="memItemLeft" align="right" valign="top">typedef int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8b2beb5227708f8127b666f5a7fc41b3">MQTTClient_token</a></td></tr>
<tr class="separator:a8b2beb5227708f8127b666f5a7fc41b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa42130dd069e7e949bcab37b6dce64a5" id="r_aa42130dd069e7e949bcab37b6dce64a5"><td class="memItemLeft" align="right" valign="top">typedef int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa42130dd069e7e949bcab37b6dce64a5">MQTTClient_messageArrived</a>(void *context, char *topicName, int topicLen, <a class="el" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a> *message)</td></tr>
<tr class="separator:aa42130dd069e7e949bcab37b6dce64a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abef83794d8252551ed248cde6eb845a6" id="r_abef83794d8252551ed248cde6eb845a6"><td class="memItemLeft" align="right" valign="top">typedef void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abef83794d8252551ed248cde6eb845a6">MQTTClient_deliveryComplete</a>(void *context, <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> dt)</td></tr>
<tr class="separator:abef83794d8252551ed248cde6eb845a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6bb253f16754e7cc81798c9fda0e36cf" id="r_a6bb253f16754e7cc81798c9fda0e36cf"><td class="memItemLeft" align="right" valign="top">typedef void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6bb253f16754e7cc81798c9fda0e36cf">MQTTClient_connectionLost</a>(void *context, char *cause)</td></tr>
<tr class="separator:a6bb253f16754e7cc81798c9fda0e36cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41108d4cccb67a9d6884ebae52211c46" id="r_a41108d4cccb67a9d6884ebae52211c46"><td class="memItemLeft" align="right" valign="top">typedef void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a41108d4cccb67a9d6884ebae52211c46">MQTTClient_disconnected</a>(void *context, <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *properties, enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reasonCode)</td></tr>
<tr class="separator:a41108d4cccb67a9d6884ebae52211c46"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6c3f51e50e2c47328eee1b0c920ed103" id="r_a6c3f51e50e2c47328eee1b0c920ed103"><td class="memItemLeft" align="right" valign="top">typedef void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6c3f51e50e2c47328eee1b0c920ed103">MQTTClient_published</a>(void *context, int dt, int packet_type, <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *properties, enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reasonCode)</td></tr>
<tr class="separator:a6c3f51e50e2c47328eee1b0c920ed103"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d31d490adbe677902b99eca127bee56" id="r_a0d31d490adbe677902b99eca127bee56"><td class="memItemLeft" align="right" valign="top">typedef struct MQTTResponse&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0d31d490adbe677902b99eca127bee56">MQTTResponse</a></td></tr>
<tr class="separator:a0d31d490adbe677902b99eca127bee56"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa5758290a1162e5135bca97bbfd5774" id="r_afa5758290a1162e5135bca97bbfd5774"><td class="memItemLeft" align="right" valign="top">typedef void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#afa5758290a1162e5135bca97bbfd5774">MQTTClient_traceCallback</a>(enum <a class="el" href="#aa0ae95caa9c16d152b5036b1bac2e09b">MQTTCLIENT_TRACE_LEVELS</a> level, char *message)</td></tr>
<tr class="separator:afa5758290a1162e5135bca97bbfd5774"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="enum-members" name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:aa0ae95caa9c16d152b5036b1bac2e09b" id="r_aa0ae95caa9c16d152b5036b1bac2e09b"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa0ae95caa9c16d152b5036b1bac2e09b">MQTTCLIENT_TRACE_LEVELS</a> { <br />
&#160;&#160;<a class="el" href="#aa0ae95caa9c16d152b5036b1bac2e09ba38a4c3c4e2fc99711793ee2815aee40c">MQTTCLIENT_TRACE_MAXIMUM</a> = 1
, <a class="el" href="#aa0ae95caa9c16d152b5036b1bac2e09ba4bb7e7221b59e9be4515f2182c03ea99">MQTTCLIENT_TRACE_MEDIUM</a>
, <a class="el" href="#aa0ae95caa9c16d152b5036b1bac2e09bacf029d9a231bd07e5e1a6f3bd0b6004e">MQTTCLIENT_TRACE_MINIMUM</a>
, <a class="el" href="#aa0ae95caa9c16d152b5036b1bac2e09ba29f21f77cf34ab2467520d7738fd8eb1">MQTTCLIENT_TRACE_PROTOCOL</a>
, <br />
&#160;&#160;<a class="el" href="#aa0ae95caa9c16d152b5036b1bac2e09ba6eefffc98c1ba698224ba351f12e6a91">MQTTCLIENT_TRACE_ERROR</a>
, <a class="el" href="#aa0ae95caa9c16d152b5036b1bac2e09baf060569bdbb4015cfce028937b4cfa69">MQTTCLIENT_TRACE_SEVERE</a>
, <a class="el" href="#aa0ae95caa9c16d152b5036b1bac2e09ba35626cc4876d074c4c21f8c4f54fdf38">MQTTCLIENT_TRACE_FATAL</a>
<br />
 }</td></tr>
<tr class="separator:aa0ae95caa9c16d152b5036b1bac2e09b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a21804ede1a506d1d69a472bc30acc8ba" id="r_a21804ede1a506d1d69a472bc30acc8ba"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a21804ede1a506d1d69a472bc30acc8ba">MQTTClient_global_init</a> (<a class="el" href="struct_m_q_t_t_client__init__options.html">MQTTClient_init_options</a> *inits)</td></tr>
<tr class="separator:a21804ede1a506d1d69a472bc30acc8ba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad27d07782991a4937ebf2f39a021f83" id="r_aad27d07782991a4937ebf2f39a021f83"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, void *context, <a class="el" href="#a6bb253f16754e7cc81798c9fda0e36cf">MQTTClient_connectionLost</a> *cl, <a class="el" href="#aa42130dd069e7e949bcab37b6dce64a5">MQTTClient_messageArrived</a> *ma, <a class="el" href="#abef83794d8252551ed248cde6eb845a6">MQTTClient_deliveryComplete</a> *dc)</td></tr>
<tr class="separator:aad27d07782991a4937ebf2f39a021f83"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8adea083a162735d5c7592160088eea0" id="r_a8adea083a162735d5c7592160088eea0"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8adea083a162735d5c7592160088eea0">MQTTClient_setDisconnected</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, void *context, <a class="el" href="#a41108d4cccb67a9d6884ebae52211c46">MQTTClient_disconnected</a> *co)</td></tr>
<tr class="separator:a8adea083a162735d5c7592160088eea0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9f13911351a3de6b1ebdabd4cb4116ba" id="r_a9f13911351a3de6b1ebdabd4cb4116ba"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9f13911351a3de6b1ebdabd4cb4116ba">MQTTClient_setPublished</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, void *context, <a class="el" href="#a6c3f51e50e2c47328eee1b0c920ed103">MQTTClient_published</a> *co)</td></tr>
<tr class="separator:a9f13911351a3de6b1ebdabd4cb4116ba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9a0518d9ca924d12c1329dbe3de5f2b6" id="r_a9a0518d9ca924d12c1329dbe3de5f2b6"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> *handle, const char *serverURI, const char *clientId, int persistence_type, void *persistence_context)</td></tr>
<tr class="separator:a9a0518d9ca924d12c1329dbe3de5f2b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ade24f717a9b39d38b081e1d5e0db1661" id="r_ade24f717a9b39d38b081e1d5e0db1661"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ade24f717a9b39d38b081e1d5e0db1661">MQTTClient_createWithOptions</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> *handle, const char *serverURI, const char *clientId, int persistence_type, void *persistence_context, <a class="el" href="struct_m_q_t_t_client__create_options.html">MQTTClient_createOptions</a> *options)</td></tr>
<tr class="separator:ade24f717a9b39d38b081e1d5e0db1661"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aef6eba956a5ff6072854a9e353487087" id="r_aef6eba956a5ff6072854a9e353487087"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_client__name_value.html">MQTTClient_nameValue</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aef6eba956a5ff6072854a9e353487087">MQTTClient_getVersionInfo</a> (void)</td></tr>
<tr class="separator:aef6eba956a5ff6072854a9e353487087"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaa8ae61cd65c9dc0846df10122d7bd4e" id="r_aaa8ae61cd65c9dc0846df10122d7bd4e"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aaa8ae61cd65c9dc0846df10122d7bd4e">MQTTClient_connect</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <a class="el" href="struct_m_q_t_t_client__connect_options.html">MQTTClient_connectOptions</a> *options)</td></tr>
<tr class="separator:aaa8ae61cd65c9dc0846df10122d7bd4e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a01bd2c5f98ec5c0636a106db33f2b01b" id="r_a01bd2c5f98ec5c0636a106db33f2b01b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a01bd2c5f98ec5c0636a106db33f2b01b">MQTTResponse_free</a> (<a class="el" href="struct_m_q_t_t_response.html">MQTTResponse</a> response)</td></tr>
<tr class="separator:a01bd2c5f98ec5c0636a106db33f2b01b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa777f80cb3eec5610f976aff30b8c0d6" id="r_aa777f80cb3eec5610f976aff30b8c0d6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_response.html">MQTTResponse</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa777f80cb3eec5610f976aff30b8c0d6">MQTTClient_connect5</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <a class="el" href="struct_m_q_t_t_client__connect_options.html">MQTTClient_connectOptions</a> *options, <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *connectProperties, <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *willProperties)</td></tr>
<tr class="separator:aa777f80cb3eec5610f976aff30b8c0d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e4d90c13a3c0705bc4a13bfe64e6525" id="r_a1e4d90c13a3c0705bc4a13bfe64e6525"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1e4d90c13a3c0705bc4a13bfe64e6525">MQTTClient_disconnect</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, int timeout)</td></tr>
<tr class="separator:a1e4d90c13a3c0705bc4a13bfe64e6525"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1762c469715b7f718c4e63a427e6c13c" id="r_a1762c469715b7f718c4e63a427e6c13c"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1762c469715b7f718c4e63a427e6c13c">MQTTClient_disconnect5</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, int timeout, enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reason, <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props)</td></tr>
<tr class="separator:a1762c469715b7f718c4e63a427e6c13c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e8231e8c47f6f67f7ebbb5dcb4c69c0" id="r_a6e8231e8c47f6f67f7ebbb5dcb4c69c0"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6e8231e8c47f6f67f7ebbb5dcb4c69c0">MQTTClient_isConnected</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle)</td></tr>
<tr class="separator:a6e8231e8c47f6f67f7ebbb5dcb4c69c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9c1c28258f0d5c6a44ff53a98618f5f3" id="r_a9c1c28258f0d5c6a44ff53a98618f5f3"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9c1c28258f0d5c6a44ff53a98618f5f3">MQTTClient_subscribe</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, const char *topic, int qos)</td></tr>
<tr class="separator:a9c1c28258f0d5c6a44ff53a98618f5f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af35ab7375435f7b6388c5ff4610dad3d" id="r_af35ab7375435f7b6388c5ff4610dad3d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_response.html">MQTTResponse</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af35ab7375435f7b6388c5ff4610dad3d">MQTTClient_subscribe5</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, const char *topic, int qos, <a class="el" href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a> *opts, <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props)</td></tr>
<tr class="separator:af35ab7375435f7b6388c5ff4610dad3d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a92fa1c13f3db8399e042fbdbdfb692b3" id="r_a92fa1c13f3db8399e042fbdbdfb692b3"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a92fa1c13f3db8399e042fbdbdfb692b3">MQTTClient_subscribeMany</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, int count, char *const *topic, int *qos)</td></tr>
<tr class="separator:a92fa1c13f3db8399e042fbdbdfb692b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5390c2402f135c12826ffbf6fc261f7c" id="r_a5390c2402f135c12826ffbf6fc261f7c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_response.html">MQTTResponse</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5390c2402f135c12826ffbf6fc261f7c">MQTTClient_subscribeMany5</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, int count, char *const *topic, int *qos, <a class="el" href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a> *opts, <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props)</td></tr>
<tr class="separator:a5390c2402f135c12826ffbf6fc261f7c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa8731be3dbc6a25f41f037f8bbbb054b" id="r_aa8731be3dbc6a25f41f037f8bbbb054b"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa8731be3dbc6a25f41f037f8bbbb054b">MQTTClient_unsubscribe</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, const char *topic)</td></tr>
<tr class="separator:aa8731be3dbc6a25f41f037f8bbbb054b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a58356c13867f18df60fd4c7ec9457c48" id="r_a58356c13867f18df60fd4c7ec9457c48"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_response.html">MQTTResponse</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a58356c13867f18df60fd4c7ec9457c48">MQTTClient_unsubscribe5</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, const char *topic, <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props)</td></tr>
<tr class="separator:a58356c13867f18df60fd4c7ec9457c48"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a50abbce720d50b9f84b97ff9fa1f546d" id="r_a50abbce720d50b9f84b97ff9fa1f546d"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a50abbce720d50b9f84b97ff9fa1f546d">MQTTClient_unsubscribeMany</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, int count, char *const *topic)</td></tr>
<tr class="separator:a50abbce720d50b9f84b97ff9fa1f546d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a46bdb532d2153110ccffb2f0748d1ba5" id="r_a46bdb532d2153110ccffb2f0748d1ba5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_response.html">MQTTResponse</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a46bdb532d2153110ccffb2f0748d1ba5">MQTTClient_unsubscribeMany5</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, int count, char *const *topic, <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props)</td></tr>
<tr class="separator:a46bdb532d2153110ccffb2f0748d1ba5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afe9c34013c3511b8ef6cd36bf703678d" id="r_afe9c34013c3511b8ef6cd36bf703678d"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#afe9c34013c3511b8ef6cd36bf703678d">MQTTClient_publish</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, const char *topicName, int payloadlen, const void *payload, int qos, int retained, <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> *dt)</td></tr>
<tr class="separator:afe9c34013c3511b8ef6cd36bf703678d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8148186cc7683a6bb57f621653df51df" id="r_a8148186cc7683a6bb57f621653df51df"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_response.html">MQTTResponse</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8148186cc7683a6bb57f621653df51df">MQTTClient_publish5</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, const char *topicName, int payloadlen, const void *payload, int qos, int retained, <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *properties, <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> *dt)</td></tr>
<tr class="separator:a8148186cc7683a6bb57f621653df51df"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ace320b8a92c7087d9dd5cf242d50389d" id="r_ace320b8a92c7087d9dd5cf242d50389d"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ace320b8a92c7087d9dd5cf242d50389d">MQTTClient_publishMessage</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, const char *topicName, <a class="el" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a> *msg, <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> *dt)</td></tr>
<tr class="separator:ace320b8a92c7087d9dd5cf242d50389d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a362042ce973c012bad6a1aa3b5984f5d" id="r_a362042ce973c012bad6a1aa3b5984f5d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_response.html">MQTTResponse</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a362042ce973c012bad6a1aa3b5984f5d">MQTTClient_publishMessage5</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, const char *topicName, <a class="el" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a> *msg, <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> *dt)</td></tr>
<tr class="separator:a362042ce973c012bad6a1aa3b5984f5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a83807ec81fe8c3941e368ab329d43067" id="r_a83807ec81fe8c3941e368ab329d43067"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a83807ec81fe8c3941e368ab329d43067">MQTTClient_waitForCompletion</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> dt, unsigned long timeout)</td></tr>
<tr class="separator:a83807ec81fe8c3941e368ab329d43067"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a617c6b0492c04a4ddea592f5e53604" id="r_a2a617c6b0492c04a4ddea592f5e53604"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2a617c6b0492c04a4ddea592f5e53604">MQTTClient_getPendingDeliveryTokens</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> **tokens)</td></tr>
<tr class="separator:a2a617c6b0492c04a4ddea592f5e53604"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8ad3d29864a9ca08202b0832e0f6678e" id="r_a8ad3d29864a9ca08202b0832e0f6678e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8ad3d29864a9ca08202b0832e0f6678e">MQTTClient_yield</a> (void)</td></tr>
<tr class="separator:a8ad3d29864a9ca08202b0832e0f6678e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4c2df88d00a3dadd510a8cb774739366" id="r_a4c2df88d00a3dadd510a8cb774739366"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4c2df88d00a3dadd510a8cb774739366">MQTTClient_receive</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, char **topicName, int *topicLen, <a class="el" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a> **message, unsigned long timeout)</td></tr>
<tr class="separator:a4c2df88d00a3dadd510a8cb774739366"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd8abde4f39d3e689029de27f7a98a65" id="r_abd8abde4f39d3e689029de27f7a98a65"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abd8abde4f39d3e689029de27f7a98a65">MQTTClient_freeMessage</a> (<a class="el" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a> **msg)</td></tr>
<tr class="separator:abd8abde4f39d3e689029de27f7a98a65"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a203b545c999beb6b825ec99b6aea79ab" id="r_a203b545c999beb6b825ec99b6aea79ab"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a203b545c999beb6b825ec99b6aea79ab">MQTTClient_free</a> (void *ptr)</td></tr>
<tr class="separator:a203b545c999beb6b825ec99b6aea79ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1f3ae01af021b014df147c9996156a69" id="r_a1f3ae01af021b014df147c9996156a69"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1f3ae01af021b014df147c9996156a69">MQTTClient_malloc</a> (size_t size)</td></tr>
<tr class="separator:a1f3ae01af021b014df147c9996156a69"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae700c3f5cfea3813264ce95e7c8cf498" id="r_ae700c3f5cfea3813264ce95e7c8cf498"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae700c3f5cfea3813264ce95e7c8cf498">MQTTClient_destroy</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> *handle)</td></tr>
<tr class="separator:ae700c3f5cfea3813264ce95e7c8cf498"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4dfa35d29db54b10b15b8ac2d9a778be" id="r_a4dfa35d29db54b10b15b8ac2d9a778be"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4dfa35d29db54b10b15b8ac2d9a778be">MQTTClient_setTraceLevel</a> (enum <a class="el" href="#aa0ae95caa9c16d152b5036b1bac2e09b">MQTTCLIENT_TRACE_LEVELS</a> level)</td></tr>
<tr class="separator:a4dfa35d29db54b10b15b8ac2d9a778be"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a22870f94aa4cb1827626612f1ded7c69" id="r_a22870f94aa4cb1827626612f1ded7c69"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a22870f94aa4cb1827626612f1ded7c69">MQTTClient_setTraceCallback</a> (<a class="el" href="#afa5758290a1162e5135bca97bbfd5774">MQTTClient_traceCallback</a> *callback)</td></tr>
<tr class="separator:a22870f94aa4cb1827626612f1ded7c69"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a96067a2fb74d2a61c7e93015629548e0" id="r_a96067a2fb74d2a61c7e93015629548e0"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a96067a2fb74d2a61c7e93015629548e0">MQTTClient_setCommandTimeout</a> (<a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, unsigned long milliSeconds)</td></tr>
<tr class="separator:a96067a2fb74d2a61c7e93015629548e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a68535b4c6d8f28b29a52569926cdeb50" id="r_a68535b4c6d8f28b29a52569926cdeb50"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a68535b4c6d8f28b29a52569926cdeb50">MQTTClient_strerror</a> (int code)</td></tr>
<tr class="separator:a68535b4c6d8f28b29a52569926cdeb50"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="acba095704d79e5a1996389fa26203f73" name="acba095704d79e5a1996389fa26203f73"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acba095704d79e5a1996389fa26203f73">&#9670;&#160;</a></span>MQTTCLIENT_SUCCESS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_SUCCESS&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: No error. Indicates successful completion of an MQTT client operation. </p>

</div>
</div>
<a id="af33a6d6c0e8a6a747bf39638e0bba36b" name="af33a6d6c0e8a6a747bf39638e0bba36b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af33a6d6c0e8a6a747bf39638e0bba36b">&#9670;&#160;</a></span>MQTTCLIENT_FAILURE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_FAILURE&#160;&#160;&#160;-1</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: A generic error code indicating the failure of an MQTT client operation. </p>

</div>
</div>
<a id="a561d053311cb492cf7226f419ee0d516" name="a561d053311cb492cf7226f419ee0d516"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a561d053311cb492cf7226f419ee0d516">&#9670;&#160;</a></span>MQTTCLIENT_DISCONNECTED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_DISCONNECTED&#160;&#160;&#160;-3</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: The client is disconnected. </p>

</div>
</div>
<a id="a8fc442fc2e9dfb422a163ab1fa02e0cb" name="a8fc442fc2e9dfb422a163ab1fa02e0cb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8fc442fc2e9dfb422a163ab1fa02e0cb">&#9670;&#160;</a></span>MQTTCLIENT_MAX_MESSAGES_INFLIGHT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_MAX_MESSAGES_INFLIGHT&#160;&#160;&#160;-4</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: The maximum number of messages allowed to be simultaneously in-flight has been reached. </p>

</div>
</div>
<a id="a678a4744192de9c8dca220d9965809dd" name="a678a4744192de9c8dca220d9965809dd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a678a4744192de9c8dca220d9965809dd">&#9670;&#160;</a></span>MQTTCLIENT_BAD_UTF8_STRING</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_BAD_UTF8_STRING&#160;&#160;&#160;-5</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: An invalid UTF-8 string has been detected. </p>

</div>
</div>
<a id="ac3232abd7f86bbba26faea0e2b132c3c" name="ac3232abd7f86bbba26faea0e2b132c3c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac3232abd7f86bbba26faea0e2b132c3c">&#9670;&#160;</a></span>MQTTCLIENT_NULL_PARAMETER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_NULL_PARAMETER&#160;&#160;&#160;-6</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: A NULL parameter has been supplied when this is invalid. </p>

</div>
</div>
<a id="a29afebfce0bdf6cda1e37abc0c4b6690" name="a29afebfce0bdf6cda1e37abc0c4b6690"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a29afebfce0bdf6cda1e37abc0c4b6690">&#9670;&#160;</a></span>MQTTCLIENT_TOPICNAME_TRUNCATED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_TOPICNAME_TRUNCATED&#160;&#160;&#160;-7</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: The topic has been truncated (the topic string includes embedded NULL characters). String functions will not access the full topic. Use the topic length value to access the full topic. </p>

</div>
</div>
<a id="a747615d8064e3fe024ae5565ec63e1ce" name="a747615d8064e3fe024ae5565ec63e1ce"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a747615d8064e3fe024ae5565ec63e1ce">&#9670;&#160;</a></span>MQTTCLIENT_BAD_STRUCTURE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_BAD_STRUCTURE&#160;&#160;&#160;-8</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: A structure parameter does not have the correct eyecatcher and version number. </p>

</div>
</div>
<a id="a51cc8ca032acf4ae14f83996524b8cdc" name="a51cc8ca032acf4ae14f83996524b8cdc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a51cc8ca032acf4ae14f83996524b8cdc">&#9670;&#160;</a></span>MQTTCLIENT_BAD_QOS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_BAD_QOS&#160;&#160;&#160;-9</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: A QoS value that falls outside of the acceptable range (0,1,2) </p>

</div>
</div>
<a id="a1c67fc83ba1a8f26236aa49b127bdb61" name="a1c67fc83ba1a8f26236aa49b127bdb61"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1c67fc83ba1a8f26236aa49b127bdb61">&#9670;&#160;</a></span>MQTTCLIENT_SSL_NOT_SUPPORTED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_SSL_NOT_SUPPORTED&#160;&#160;&#160;-10</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: Attempting SSL connection using non-SSL version of library </p>

</div>
</div>
<a id="aab84cecd25638896eb45b8f5ffd82bf7" name="aab84cecd25638896eb45b8f5ffd82bf7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aab84cecd25638896eb45b8f5ffd82bf7">&#9670;&#160;</a></span>MQTTCLIENT_BAD_MQTT_VERSION</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_BAD_MQTT_VERSION&#160;&#160;&#160;-11</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: unrecognized MQTT version </p>

</div>
</div>
<a id="a1d0cb25b450136f036a238546487344a" name="a1d0cb25b450136f036a238546487344a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1d0cb25b450136f036a238546487344a">&#9670;&#160;</a></span>MQTTCLIENT_BAD_PROTOCOL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_BAD_PROTOCOL&#160;&#160;&#160;-14</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: protocol prefix in serverURI should be: </p><ul>
<li><em>tcp://</em> or <em>mqtt://</em> - Insecure TCP </li>
<li><em>ssl://</em> or <em>mqtts://</em> - Encrypted SSL/TLS </li>
<li><em>ws://</em> - Insecure websockets </li>
<li><em>wss://</em> - Secure web sockets The TLS enabled prefixes (ssl, mqtts, wss) are only valid if a TLS version of the library is linked with. </li>
</ul>

</div>
</div>
<a id="a1babaca56ffae802fa1e246a2649927e" name="a1babaca56ffae802fa1e246a2649927e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1babaca56ffae802fa1e246a2649927e">&#9670;&#160;</a></span>MQTTCLIENT_BAD_MQTT_OPTION</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_BAD_MQTT_OPTION&#160;&#160;&#160;-15</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: option not applicable to the requested version of MQTT </p>

</div>
</div>
<a id="ae9070d21de569f999a9575049cdd6da1" name="ae9070d21de569f999a9575049cdd6da1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae9070d21de569f999a9575049cdd6da1">&#9670;&#160;</a></span>MQTTCLIENT_WRONG_MQTT_VERSION</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_WRONG_MQTT_VERSION&#160;&#160;&#160;-16</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: call not applicable to the requested version of MQTT </p>

</div>
</div>
<a id="aacf90ba5292e25122e6fd5ec2a38efe5" name="aacf90ba5292e25122e6fd5ec2a38efe5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aacf90ba5292e25122e6fd5ec2a38efe5">&#9670;&#160;</a></span>MQTTCLIENT_0_LEN_WILL_TOPIC</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_0_LEN_WILL_TOPIC&#160;&#160;&#160;-17</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: 0 length will topic on connect </p>

</div>
</div>
<a id="a75b80b01f98d5a1ffa2a4d42995a8397" name="a75b80b01f98d5a1ffa2a4d42995a8397"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a75b80b01f98d5a1ffa2a4d42995a8397">&#9670;&#160;</a></span>MQTTVERSION_DEFAULT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTVERSION_DEFAULT&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Default MQTT version to connect with. Use 3.1.1 then fall back to 3.1 </p>

</div>
</div>
<a id="a4603b988e76872e1f23f135d225ce2fb" name="a4603b988e76872e1f23f135d225ce2fb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4603b988e76872e1f23f135d225ce2fb">&#9670;&#160;</a></span>MQTTVERSION_3_1</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTVERSION_3_1&#160;&#160;&#160;3</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>MQTT version to connect with: 3.1 </p>

</div>
</div>
<a id="ac79cc6fdeaa9e3f4ee12c3418898b1ef" name="ac79cc6fdeaa9e3f4ee12c3418898b1ef"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac79cc6fdeaa9e3f4ee12c3418898b1ef">&#9670;&#160;</a></span>MQTTVERSION_3_1_1</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTVERSION_3_1_1&#160;&#160;&#160;4</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>MQTT version to connect with: 3.1.1 </p>

</div>
</div>
<a id="af8b176fa4d5b89789767ce972338e1e3" name="af8b176fa4d5b89789767ce972338e1e3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af8b176fa4d5b89789767ce972338e1e3">&#9670;&#160;</a></span>MQTTVERSION_5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTVERSION_5&#160;&#160;&#160;5</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>MQTT version to connect with: 5 </p>

</div>
</div>
<a id="ade337b363b7f4bc7c1a7b2858e0380bd" name="ade337b363b7f4bc7c1a7b2858e0380bd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ade337b363b7f4bc7c1a7b2858e0380bd">&#9670;&#160;</a></span>MQTT_BAD_SUBSCRIBE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTT_BAD_SUBSCRIBE&#160;&#160;&#160;0x80</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Bad return code from subscribe, as defined in the 3.1.1 specification </p>

</div>
</div>
<a id="ac17057c8c22c0717d3adf4e040440f73" name="ac17057c8c22c0717d3adf4e040440f73"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac17057c8c22c0717d3adf4e040440f73">&#9670;&#160;</a></span>MQTTClient_init_options_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTClient_init_options_initializer&#160;&#160;&#160;{ {'M', 'Q', 'T', 'G'}, 0, 0 }</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa1fd995924d3df75959fcf57e87aefac" name="aa1fd995924d3df75959fcf57e87aefac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa1fd995924d3df75959fcf57e87aefac">&#9670;&#160;</a></span>MQTTClient_message_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTClient_message_initializer&#160;&#160;&#160;{ {'M', 'Q', 'T', 'M'}, 1, 0, NULL, 0, 0, 0, 0, <a class="el" href="_m_q_t_t_properties_8h.html#a5a80e158486a414ccdfcdd7f75f23988">MQTTProperties_initializer</a> }</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a763e477a5aceb6aff279111c7693e691" name="a763e477a5aceb6aff279111c7693e691"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a763e477a5aceb6aff279111c7693e691">&#9670;&#160;</a></span>MQTTClient_createOptions_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTClient_createOptions_initializer&#160;&#160;&#160;{ {'M', 'Q', 'C', 'O'}, 0, <a class="el" href="#a75b80b01f98d5a1ffa2a4d42995a8397">MQTTVERSION_DEFAULT</a> }</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aae0811659c59f5dad0467544f91645eb" name="aae0811659c59f5dad0467544f91645eb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aae0811659c59f5dad0467544f91645eb">&#9670;&#160;</a></span>MQTTClient_willOptions_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTClient_willOptions_initializer&#160;&#160;&#160;{ {'M', 'Q', 'T', 'W'}, 1, NULL, NULL, 0, 0, {0, NULL} }</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2549ea897af26c76198284731db9e721" name="a2549ea897af26c76198284731db9e721"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2549ea897af26c76198284731db9e721">&#9670;&#160;</a></span>MQTT_SSL_VERSION_DEFAULT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTT_SSL_VERSION_DEFAULT&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a7e5da3d6f0d2b53409bbfcf6e56f3d2d" name="a7e5da3d6f0d2b53409bbfcf6e56f3d2d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7e5da3d6f0d2b53409bbfcf6e56f3d2d">&#9670;&#160;</a></span>MQTT_SSL_VERSION_TLS_1_0</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTT_SSL_VERSION_TLS_1_0&#160;&#160;&#160;1</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="abdff87efa3f2ee473a1591e10638b537" name="abdff87efa3f2ee473a1591e10638b537"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abdff87efa3f2ee473a1591e10638b537">&#9670;&#160;</a></span>MQTT_SSL_VERSION_TLS_1_1</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTT_SSL_VERSION_TLS_1_1&#160;&#160;&#160;2</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a3a94dbdeafbb73c73a068e7c2085fbab" name="a3a94dbdeafbb73c73a068e7c2085fbab"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3a94dbdeafbb73c73a068e7c2085fbab">&#9670;&#160;</a></span>MQTT_SSL_VERSION_TLS_1_2</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTT_SSL_VERSION_TLS_1_2&#160;&#160;&#160;3</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab9b2a2c6b52dbb2ac842ad99a9ce6d99" name="ab9b2a2c6b52dbb2ac842ad99a9ce6d99"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab9b2a2c6b52dbb2ac842ad99a9ce6d99">&#9670;&#160;</a></span>MQTTClient_SSLOptions_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTClient_SSLOptions_initializer&#160;&#160;&#160;{ {'M', 'Q', 'T', 'S'}, 5, NULL, NULL, NULL, NULL, NULL, 1, <a class="el" href="#a2549ea897af26c76198284731db9e721">MQTT_SSL_VERSION_DEFAULT</a>, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0 }</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aefd7c865f2641c8155b763fdf3061c25" name="aefd7c865f2641c8155b763fdf3061c25"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aefd7c865f2641c8155b763fdf3061c25">&#9670;&#160;</a></span>MQTTClient_connectOptions_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTClient_connectOptions_initializer</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">{ {<span class="charliteral">&#39;M&#39;</span>, <span class="charliteral">&#39;Q&#39;</span>, <span class="charliteral">&#39;T&#39;</span>, <span class="charliteral">&#39;C&#39;</span>}, 8, 60, 1, 1, NULL, NULL, NULL, 30, 0, NULL,\</div>
<div class="line">0, NULL, <a class="code hl_define" href="#a75b80b01f98d5a1ffa2a4d42995a8397">MQTTVERSION_DEFAULT</a>, {NULL, 0, 0}, {0, NULL}, -1, 0, NULL, NULL, NULL}</div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a75b80b01f98d5a1ffa2a4d42995a8397"><div class="ttname"><a href="#a75b80b01f98d5a1ffa2a4d42995a8397">MQTTVERSION_DEFAULT</a></div><div class="ttdeci">#define MQTTVERSION_DEFAULT</div><div class="ttdef"><b>Definition</b> MQTTClient.h:213</div></div>
</div><!-- fragment --><p>Initializer for connect options for MQTT 3.1.1 non-WebSocket connections </p>

</div>
</div>
<a id="a1f0c7608262ac9c00cb94e9c8f9fc984" name="a1f0c7608262ac9c00cb94e9c8f9fc984"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1f0c7608262ac9c00cb94e9c8f9fc984">&#9670;&#160;</a></span>MQTTClient_connectOptions_initializer5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTClient_connectOptions_initializer5</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">{ {<span class="charliteral">&#39;M&#39;</span>, <span class="charliteral">&#39;Q&#39;</span>, <span class="charliteral">&#39;T&#39;</span>, <span class="charliteral">&#39;C&#39;</span>}, 8, 60, 0, 1, NULL, NULL, NULL, 30, 0, NULL,\</div>
<div class="line">0, NULL, <a class="code hl_define" href="#af8b176fa4d5b89789767ce972338e1e3">MQTTVERSION_5</a>, {NULL, 0, 0}, {0, NULL}, -1, 1, NULL, NULL, NULL}</div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_af8b176fa4d5b89789767ce972338e1e3"><div class="ttname"><a href="#af8b176fa4d5b89789767ce972338e1e3">MQTTVERSION_5</a></div><div class="ttdeci">#define MQTTVERSION_5</div><div class="ttdef"><b>Definition</b> MQTTClient.h:225</div></div>
</div><!-- fragment --><p>Initializer for connect options for MQTT 5.0 non-WebSocket connections </p>

</div>
</div>
<a id="a0a98fda162a78ee8c8cbd7d9d39494f4" name="a0a98fda162a78ee8c8cbd7d9d39494f4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0a98fda162a78ee8c8cbd7d9d39494f4">&#9670;&#160;</a></span>MQTTClient_connectOptions_initializer_ws</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTClient_connectOptions_initializer_ws</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">{ {<span class="charliteral">&#39;M&#39;</span>, <span class="charliteral">&#39;Q&#39;</span>, <span class="charliteral">&#39;T&#39;</span>, <span class="charliteral">&#39;C&#39;</span>}, 8, 45, 1, 1, NULL, NULL, NULL, 30, 0, NULL,\</div>
<div class="line">0, NULL, <a class="code hl_define" href="#a75b80b01f98d5a1ffa2a4d42995a8397">MQTTVERSION_DEFAULT</a>, {NULL, 0, 0}, {0, NULL}, -1, 0, NULL, NULL, NULL}</div>
</div><!-- fragment --><p>Initializer for connect options for MQTT 3.1.1 WebSockets connections. The keepalive interval is set to 45 seconds to avoid webserver 60 second inactivity timeouts. </p>

</div>
</div>
<a id="a8c37c9f77f0b67e2520c8f91acf1afea" name="a8c37c9f77f0b67e2520c8f91acf1afea"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8c37c9f77f0b67e2520c8f91acf1afea">&#9670;&#160;</a></span>MQTTClient_connectOptions_initializer5_ws</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTClient_connectOptions_initializer5_ws</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">{ {<span class="charliteral">&#39;M&#39;</span>, <span class="charliteral">&#39;Q&#39;</span>, <span class="charliteral">&#39;T&#39;</span>, <span class="charliteral">&#39;C&#39;</span>}, 8, 45, 0, 1, NULL, NULL, NULL, 30, 0, NULL,\</div>
<div class="line">0, NULL, <a class="code hl_define" href="#af8b176fa4d5b89789767ce972338e1e3">MQTTVERSION_5</a>, {NULL, 0, 0}, {0, NULL}, -1, 1, NULL, NULL, NULL}</div>
</div><!-- fragment --><p>Initializer for connect options for MQTT 5.0 WebSockets connections. The keepalive interval is set to 45 seconds to avoid webserver 60 second inactivity timeouts. </p>

</div>
</div>
<a id="a17f171200136bcfa933eb50ef21531a7" name="a17f171200136bcfa933eb50ef21531a7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a17f171200136bcfa933eb50ef21531a7">&#9670;&#160;</a></span>MQTTResponse_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTResponse_initializer&#160;&#160;&#160;{1, <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969">MQTTREASONCODE_SUCCESS</a>, 0, NULL, NULL}</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Typedef Documentation</h2>
<a id="a7649e3913f9a216424d296f88a969c59" name="a7649e3913f9a216424d296f88a969c59"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7649e3913f9a216424d296f88a969c59">&#9670;&#160;</a></span>MQTTClient</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void* <a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A handle representing an MQTT client. A valid client handle is available following a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </p>

</div>
</div>
<a id="a73e49030fd8b7074aa1aa45669b7fe8d" name="a73e49030fd8b7074aa1aa45669b7fe8d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a73e49030fd8b7074aa1aa45669b7fe8d">&#9670;&#160;</a></span>MQTTClient_deliveryToken</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef int <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A value representing an MQTT message. A delivery token is returned to the client application when a message is published. The token can then be used to check that the message was successfully delivered to its destination (see <a class="el" href="#afe9c34013c3511b8ef6cd36bf703678d">MQTTClient_publish()</a>, <a class="el" href="#ace320b8a92c7087d9dd5cf242d50389d">MQTTClient_publishMessage()</a>, <a class="el" href="#abef83794d8252551ed248cde6eb845a6">MQTTClient_deliveryComplete()</a>, <a class="el" href="#a83807ec81fe8c3941e368ab329d43067">MQTTClient_waitForCompletion()</a> and <a class="el" href="#a2a617c6b0492c04a4ddea592f5e53604">MQTTClient_getPendingDeliveryTokens()</a>). </p>

</div>
</div>
<a id="a8b2beb5227708f8127b666f5a7fc41b3" name="a8b2beb5227708f8127b666f5a7fc41b3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8b2beb5227708f8127b666f5a7fc41b3">&#9670;&#160;</a></span>MQTTClient_token</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef int <a class="el" href="#a8b2beb5227708f8127b666f5a7fc41b3">MQTTClient_token</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa42130dd069e7e949bcab37b6dce64a5" name="aa42130dd069e7e949bcab37b6dce64a5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa42130dd069e7e949bcab37b6dce64a5">&#9670;&#160;</a></span>MQTTClient_messageArrived</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef int MQTTClient_messageArrived(void *context, char *topicName, int topicLen, <a class="el" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a> *message)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a callback function. The client application must provide an implementation of this function to enable asynchronous receipt of messages. The function is registered with the client library by passing it as an argument to <a class="el" href="#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks()</a>. It is called by the client library when a new message that matches a client subscription has been received from the server. This function is executed on a separate thread to the one on which the client application is running. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">context</td><td>A pointer to the <em>context</em> value originally passed to <a class="el" href="#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks()</a>, which contains any application-specific context. </td></tr>
    <tr><td class="paramname">topicName</td><td>The topic associated with the received message. </td></tr>
    <tr><td class="paramname">topicLen</td><td>The length of the topic if there are one more NULL characters embedded in <em>topicName</em>, otherwise <em>topicLen</em> is 0. If <em>topicLen</em> is 0, the value returned by <em>strlen(topicName)</em> can be trusted. If <em>topicLen</em> is greater than 0, the full topic name can be retrieved by accessing <em>topicName</em> as a byte array of length <em>topicLen</em>. </td></tr>
    <tr><td class="paramname">message</td><td>The <a class="el" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a> structure for the received message. This structure contains the message payload and attributes. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>This function must return 0 or 1 indicating whether or not the message has been safely received by the client application. <br  />
 Returning 1 indicates that the message has been successfully handled. To free the message storage, <a class="el" href="#abd8abde4f39d3e689029de27f7a98a65">MQTTClient_freeMessage</a> must be called. To free the topic name storage, <a class="el" href="#a203b545c999beb6b825ec99b6aea79ab">MQTTClient_free</a> must be called.<br  />
 Returning 0 indicates that there was a problem. In this case, the client library will reinvoke <a class="el" href="#aa42130dd069e7e949bcab37b6dce64a5">MQTTClient_messageArrived()</a> to attempt to deliver the message to the application again. Do not free the message and topic storage when returning 0, otherwise the redelivery will fail. </dd></dl>

</div>
</div>
<a id="abef83794d8252551ed248cde6eb845a6" name="abef83794d8252551ed248cde6eb845a6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abef83794d8252551ed248cde6eb845a6">&#9670;&#160;</a></span>MQTTClient_deliveryComplete</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void MQTTClient_deliveryComplete(void *context, <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> dt)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a callback function. The client application must provide an implementation of this function to enable asynchronous notification of delivery of messages. The function is registered with the client library by passing it as an argument to <a class="el" href="#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks()</a>. It is called by the client library after the client application has published a message to the server. It indicates that the necessary handshaking and acknowledgements for the requested quality of service (see <a class="el" href="struct_m_q_t_t_client__message.html#a35738099155a0e4f54050da474bab2e7">MQTTClient_message.qos</a>) have been completed. This function is executed on a separate thread to the one on which the client application is running. <b>Note:</b><a class="el" href="#abef83794d8252551ed248cde6eb845a6">MQTTClient_deliveryComplete()</a> is not called when messages are published at QoS0. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">context</td><td>A pointer to the <em>context</em> value originally passed to <a class="el" href="#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks()</a>, which contains any application-specific context. </td></tr>
    <tr><td class="paramname">dt</td><td>The <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> associated with the published message. Applications can check that all messages have been correctly published by matching the delivery tokens returned from calls to <a class="el" href="#afe9c34013c3511b8ef6cd36bf703678d">MQTTClient_publish()</a> and <a class="el" href="#ace320b8a92c7087d9dd5cf242d50389d">MQTTClient_publishMessage()</a> with the tokens passed to this callback. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a6bb253f16754e7cc81798c9fda0e36cf" name="a6bb253f16754e7cc81798c9fda0e36cf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6bb253f16754e7cc81798c9fda0e36cf">&#9670;&#160;</a></span>MQTTClient_connectionLost</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void MQTTClient_connectionLost(void *context, char *cause)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a callback function. The client application must provide an implementation of this function to enable asynchronous notification of the loss of connection to the server. The function is registered with the client library by passing it as an argument to <a class="el" href="#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks()</a>. It is called by the client library if the client loses its connection to the server. The client application must take appropriate action, such as trying to reconnect or reporting the problem. This function is executed on a separate thread to the one on which the client application is running. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">context</td><td>A pointer to the <em>context</em> value originally passed to <a class="el" href="#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks()</a>, which contains any application-specific context. </td></tr>
    <tr><td class="paramname">cause</td><td>The reason for the disconnection. Currently, <em>cause</em> is always set to NULL. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a41108d4cccb67a9d6884ebae52211c46" name="a41108d4cccb67a9d6884ebae52211c46"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a41108d4cccb67a9d6884ebae52211c46">&#9670;&#160;</a></span>MQTTClient_disconnected</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void MQTTClient_disconnected(void *context, <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *properties, enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reasonCode)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a callback function, which will be called when the a disconnect packet is received from the server. This applies to MQTT V5 and above only. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">context</td><td>A pointer to the <em>context</em> value originally passed to <a class="el" href="#a8adea083a162735d5c7592160088eea0">MQTTClient_setDisconnected()</a>, which contains any application-specific context. </td></tr>
    <tr><td class="paramname">properties</td><td>The MQTT V5 properties received with the disconnect, if any. </td></tr>
    <tr><td class="paramname">reasonCode</td><td>The MQTT V5 reason code received with the disconnect. Currently, <em>cause</em> is always set to NULL. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a6c3f51e50e2c47328eee1b0c920ed103" name="a6c3f51e50e2c47328eee1b0c920ed103"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6c3f51e50e2c47328eee1b0c920ed103">&#9670;&#160;</a></span>MQTTClient_published</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void MQTTClient_published(void *context, int dt, int packet_type, <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *properties, enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reasonCode)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a callback function, the MQTT V5 version of <a class="el" href="#abef83794d8252551ed248cde6eb845a6">MQTTClient_deliveryComplete()</a>. The client application must provide an implementation of this function to enable asynchronous notification of the completed delivery of messages. It is called by the client library after the client application has published a message to the server. It indicates that the necessary handshaking and acknowledgements for the requested quality of service (see <a class="el" href="struct_m_q_t_t_client__message.html#a35738099155a0e4f54050da474bab2e7">MQTTClient_message.qos</a>) have been completed. This function is executed on a separate thread to the one on which the client application is running. <b>Note:</b> It is not called when messages are published at QoS0. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">context</td><td>A pointer to the <em>context</em> value originally passed to <a class="el" href="#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks()</a>, which contains any application-specific context. </td></tr>
    <tr><td class="paramname">dt</td><td>The <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> associated with the published message. Applications can check that all messages have been correctly published by matching the delivery tokens returned from calls to <a class="el" href="#afe9c34013c3511b8ef6cd36bf703678d">MQTTClient_publish()</a> and <a class="el" href="#ace320b8a92c7087d9dd5cf242d50389d">MQTTClient_publishMessage()</a> with the tokens passed to this callback. </td></tr>
    <tr><td class="paramname">packet_type</td><td>the last received packet type for this completion. For QoS 1 always PUBACK. For QoS 2 could be PUBREC or PUBCOMP. </td></tr>
    <tr><td class="paramname">properties</td><td>the MQTT V5 properties returned with the last packet from the server </td></tr>
    <tr><td class="paramname">reasonCode</td><td>the reason code returned from the server </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a0d31d490adbe677902b99eca127bee56" name="a0d31d490adbe677902b99eca127bee56"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0d31d490adbe677902b99eca127bee56">&#9670;&#160;</a></span>MQTTResponse</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct MQTTResponse MQTTResponse</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>MQTT version 5.0 response information </p>

</div>
</div>
<a id="afa5758290a1162e5135bca97bbfd5774" name="afa5758290a1162e5135bca97bbfd5774"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afa5758290a1162e5135bca97bbfd5774">&#9670;&#160;</a></span>MQTTClient_traceCallback</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void MQTTClient_traceCallback(enum <a class="el" href="#aa0ae95caa9c16d152b5036b1bac2e09b">MQTTCLIENT_TRACE_LEVELS</a> level, char *message)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a callback function prototype which must be implemented if you want to receive trace information. Do not invoke any other Paho API calls in this callback function - unpredictable behavior may result. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">level</td><td>the trace level of the message returned </td></tr>
    <tr><td class="paramname">message</td><td>the trace message. This is a pointer to a static buffer which will be overwritten on each call. You must copy the data if you want to keep it for later. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Enumeration Type Documentation</h2>
<a id="aa0ae95caa9c16d152b5036b1bac2e09b" name="aa0ae95caa9c16d152b5036b1bac2e09b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa0ae95caa9c16d152b5036b1bac2e09b">&#9670;&#160;</a></span>MQTTCLIENT_TRACE_LEVELS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="#aa0ae95caa9c16d152b5036b1bac2e09b">MQTTCLIENT_TRACE_LEVELS</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="aa0ae95caa9c16d152b5036b1bac2e09ba38a4c3c4e2fc99711793ee2815aee40c" name="aa0ae95caa9c16d152b5036b1bac2e09ba38a4c3c4e2fc99711793ee2815aee40c"></a>MQTTCLIENT_TRACE_MAXIMUM&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aa0ae95caa9c16d152b5036b1bac2e09ba4bb7e7221b59e9be4515f2182c03ea99" name="aa0ae95caa9c16d152b5036b1bac2e09ba4bb7e7221b59e9be4515f2182c03ea99"></a>MQTTCLIENT_TRACE_MEDIUM&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aa0ae95caa9c16d152b5036b1bac2e09bacf029d9a231bd07e5e1a6f3bd0b6004e" name="aa0ae95caa9c16d152b5036b1bac2e09bacf029d9a231bd07e5e1a6f3bd0b6004e"></a>MQTTCLIENT_TRACE_MINIMUM&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aa0ae95caa9c16d152b5036b1bac2e09ba29f21f77cf34ab2467520d7738fd8eb1" name="aa0ae95caa9c16d152b5036b1bac2e09ba29f21f77cf34ab2467520d7738fd8eb1"></a>MQTTCLIENT_TRACE_PROTOCOL&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aa0ae95caa9c16d152b5036b1bac2e09ba6eefffc98c1ba698224ba351f12e6a91" name="aa0ae95caa9c16d152b5036b1bac2e09ba6eefffc98c1ba698224ba351f12e6a91"></a>MQTTCLIENT_TRACE_ERROR&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aa0ae95caa9c16d152b5036b1bac2e09baf060569bdbb4015cfce028937b4cfa69" name="aa0ae95caa9c16d152b5036b1bac2e09baf060569bdbb4015cfce028937b4cfa69"></a>MQTTCLIENT_TRACE_SEVERE&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aa0ae95caa9c16d152b5036b1bac2e09ba35626cc4876d074c4c21f8c4f54fdf38" name="aa0ae95caa9c16d152b5036b1bac2e09ba35626cc4876d074c4c21f8c4f54fdf38"></a>MQTTCLIENT_TRACE_FATAL&#160;</td><td class="fielddoc"></td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="a21804ede1a506d1d69a472bc30acc8ba" name="a21804ede1a506d1d69a472bc30acc8ba"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a21804ede1a506d1d69a472bc30acc8ba">&#9670;&#160;</a></span>MQTTClient_global_init()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTClient_global_init </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_client__init__options.html">MQTTClient_init_options</a> *</td>          <td class="paramname"><span class="paramname"><em>inits</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Global init of mqtt library. Call once on program start to set global behaviour. do_openssl_init - if mqtt library should initialize OpenSSL (1) or rely on the caller to do it before using the library (0) </p>

</div>
</div>
<a id="aad27d07782991a4937ebf2f39a021f83" name="aad27d07782991a4937ebf2f39a021f83"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aad27d07782991a4937ebf2f39a021f83">&#9670;&#160;</a></span>MQTTClient_setCallbacks()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_setCallbacks </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *</td>          <td class="paramname"><span class="paramname"><em>context</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="#a6bb253f16754e7cc81798c9fda0e36cf">MQTTClient_connectionLost</a> *</td>          <td class="paramname"><span class="paramname"><em>cl</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="#aa42130dd069e7e949bcab37b6dce64a5">MQTTClient_messageArrived</a> *</td>          <td class="paramname"><span class="paramname"><em>ma</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="#abef83794d8252551ed248cde6eb845a6">MQTTClient_deliveryComplete</a> *</td>          <td class="paramname"><span class="paramname"><em>dc</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function sets the callback functions for a specific client. If your client application doesn't use a particular callback, set the relevant parameter to NULL (except for message arrived, which must be given). Calling <a class="el" href="#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks()</a> puts the client into multi-threaded mode. Any necessary message acknowledgements and status communications are handled in the background without any intervention from the client application. See <a class="el" href="async.html">Asynchronous vs synchronous client applications</a> for more information.</p>
<p><b>Note:</b> The MQTT client must be disconnected when this function is called. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">context</td><td>A pointer to any application-specific context. The the <em>context</em> pointer is passed to each of the callback functions to provide access to the context information in the callback. </td></tr>
    <tr><td class="paramname">cl</td><td>A pointer to an <a class="el" href="#a6bb253f16754e7cc81798c9fda0e36cf">MQTTClient_connectionLost()</a> callback function. You can set this to NULL if your application doesn't handle disconnections. </td></tr>
    <tr><td class="paramname">ma</td><td>A pointer to an <a class="el" href="#aa42130dd069e7e949bcab37b6dce64a5">MQTTClient_messageArrived()</a> callback function. This callback function must be set when you call <a class="el" href="#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks()</a>, as otherwise there would be nowhere to deliver any incoming messages. </td></tr>
    <tr><td class="paramname">dc</td><td>A pointer to an <a class="el" href="#abef83794d8252551ed248cde6eb845a6">MQTTClient_deliveryComplete()</a> callback function. You can set this to NULL if your application publishes synchronously or if you do not want to check for successful delivery. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a> if the callbacks were correctly set, <a class="el" href="#af33a6d6c0e8a6a747bf39638e0bba36b">MQTTCLIENT_FAILURE</a> if an error occurred. </dd></dl>

</div>
</div>
<a id="a8adea083a162735d5c7592160088eea0" name="a8adea083a162735d5c7592160088eea0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8adea083a162735d5c7592160088eea0">&#9670;&#160;</a></span>MQTTClient_setDisconnected()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_setDisconnected </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *</td>          <td class="paramname"><span class="paramname"><em>context</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="#a41108d4cccb67a9d6884ebae52211c46">MQTTClient_disconnected</a> *</td>          <td class="paramname"><span class="paramname"><em>co</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sets the <a class="el" href="#a41108d4cccb67a9d6884ebae52211c46">MQTTClient_disconnected()</a> callback function for a client. This will be called if a disconnect packet is received from the server. Only valid for MQTT V5 and above. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">context</td><td>A pointer to any application-specific context. The the <em>context</em> pointer is passed to each of the callback functions to provide access to the context information in the callback. </td></tr>
    <tr><td class="paramname">co</td><td>A pointer to an <a class="el" href="#a41108d4cccb67a9d6884ebae52211c46">MQTTClient_disconnected()</a> callback function. NULL removes the callback setting. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a> if the callbacks were correctly set, <a class="el" href="#af33a6d6c0e8a6a747bf39638e0bba36b">MQTTCLIENT_FAILURE</a> if an error occurred. </dd></dl>

</div>
</div>
<a id="a9f13911351a3de6b1ebdabd4cb4116ba" name="a9f13911351a3de6b1ebdabd4cb4116ba"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9f13911351a3de6b1ebdabd4cb4116ba">&#9670;&#160;</a></span>MQTTClient_setPublished()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_setPublished </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *</td>          <td class="paramname"><span class="paramname"><em>context</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="#a6c3f51e50e2c47328eee1b0c920ed103">MQTTClient_published</a> *</td>          <td class="paramname"><span class="paramname"><em>co</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a id="a9a0518d9ca924d12c1329dbe3de5f2b6" name="a9a0518d9ca924d12c1329dbe3de5f2b6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9a0518d9ca924d12c1329dbe3de5f2b6">&#9670;&#160;</a></span>MQTTClient_create()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_create </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> *</td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *</td>          <td class="paramname"><span class="paramname"><em>serverURI</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *</td>          <td class="paramname"><span class="paramname"><em>clientId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>persistence_type</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *</td>          <td class="paramname"><span class="paramname"><em>persistence_context</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function creates an MQTT client ready for connection to the specified server and using the specified persistent storage (see <a class="el" href="struct_m_q_t_t_client__persistence.html" title="A structure containing the function pointers to a persistence implementation and the context or state...">MQTTClient_persistence</a>). See also <a class="el" href="#ae700c3f5cfea3813264ce95e7c8cf498">MQTTClient_destroy()</a>. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A pointer to an <a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle. The handle is populated with a valid client reference following a successful return from this function. </td></tr>
    <tr><td class="paramname">serverURI</td><td>A null-terminated string specifying the server to which the client will connect. It takes the form <em>protocol://host:port</em>. Currently, <em>protocol</em> must be: <br  />
 <em>tcp://</em> or <em>mqtt://</em> - Insecure TCP <br  />
 <em>ssl://</em> or <em>mqtts://</em> - Encrypted SSL/TLS <br  />
 <em>ws://</em> - Insecure websockets <br  />
 <em>wss://</em> - Secure web sockets <br  />
 The TLS enabled prefixes (ssl, mqtts, wss) are only valid if a TLS version of the library is linked with. For <em>host</em>, you can specify either an IP address or a host name. For instance, to connect to a server running on the local machines with the default MQTT port, specify <em>tcp://localhost:1883</em>. </td></tr>
    <tr><td class="paramname">clientId</td><td>The client identifier passed to the server when the client connects to it. It is a null-terminated UTF-8 encoded string. </td></tr>
    <tr><td class="paramname">persistence_type</td><td>The type of persistence to be used by the client: <br  />
 <a class="el" href="_m_q_t_t_client_persistence_8h.html#ae01e089313a65ac4661ed216b6ac00fa">MQTTCLIENT_PERSISTENCE_NONE</a>: Use in-memory persistence. If the device or system on which the client is running fails or is switched off, the current state of any in-flight messages is lost and some messages may not be delivered even at QoS1 and QoS2. <br  />
 <a class="el" href="_m_q_t_t_client_persistence_8h.html#aaa948291718a9c06369b854b0f64bc32">MQTTCLIENT_PERSISTENCE_DEFAULT</a>: Use the default (file system-based) persistence mechanism. Status about in-flight messages is held in persistent storage and provides some protection against message loss in the case of unexpected failure. <br  />
 <a class="el" href="_m_q_t_t_client_persistence_8h.html#a5dc68b8616e4041e037bad94ce07681b">MQTTCLIENT_PERSISTENCE_USER</a>: Use an application-specific persistence implementation. Using this type of persistence gives control of the persistence mechanism to the application. The application has to implement the <a class="el" href="struct_m_q_t_t_client__persistence.html" title="A structure containing the function pointers to a persistence implementation and the context or state...">MQTTClient_persistence</a> interface. </td></tr>
    <tr><td class="paramname">persistence_context</td><td>If the application uses <a class="el" href="_m_q_t_t_client_persistence_8h.html#ae01e089313a65ac4661ed216b6ac00fa">MQTTCLIENT_PERSISTENCE_NONE</a> persistence, this argument is unused and should be set to NULL. For <a class="el" href="_m_q_t_t_client_persistence_8h.html#aaa948291718a9c06369b854b0f64bc32">MQTTCLIENT_PERSISTENCE_DEFAULT</a> persistence, it should be set to the location of the persistence directory (if set to NULL, the persistence directory used is the working directory). Applications that use <a class="el" href="_m_q_t_t_client_persistence_8h.html#a5dc68b8616e4041e037bad94ce07681b">MQTTCLIENT_PERSISTENCE_USER</a> persistence set this argument to point to a valid <a class="el" href="struct_m_q_t_t_client__persistence.html" title="A structure containing the function pointers to a persistence implementation and the context or state...">MQTTClient_persistence</a> structure. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a> if the client is successfully created, otherwise an error code is returned. </dd></dl>

</div>
</div>
<a id="ade24f717a9b39d38b081e1d5e0db1661" name="ade24f717a9b39d38b081e1d5e0db1661"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ade24f717a9b39d38b081e1d5e0db1661">&#9670;&#160;</a></span>MQTTClient_createWithOptions()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_createWithOptions </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> *</td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *</td>          <td class="paramname"><span class="paramname"><em>serverURI</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *</td>          <td class="paramname"><span class="paramname"><em>clientId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>persistence_type</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *</td>          <td class="paramname"><span class="paramname"><em>persistence_context</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_client__create_options.html">MQTTClient_createOptions</a> *</td>          <td class="paramname"><span class="paramname"><em>options</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>A version of :<a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a> with additional options. This function creates an MQTT client ready for connection to the specified server and using the specified persistent storage (see <a class="el" href="struct_m_q_t_t_client__persistence.html" title="A structure containing the function pointers to a persistence implementation and the context or state...">MQTTClient_persistence</a>). See also <a class="el" href="#ae700c3f5cfea3813264ce95e7c8cf498">MQTTClient_destroy()</a>. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A pointer to an <a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle. The handle is populated with a valid client reference following a successful return from this function. </td></tr>
    <tr><td class="paramname">serverURI</td><td>A null-terminated string specifying the server to which the client will connect. It takes the form <em>protocol://host:port</em>. Currently, <em>protocol</em> must be <em>tcp</em> or <em>ssl</em>. For <em>host</em>, you can specify either an IP address or a host name. For instance, to connect to a server running on the local machines with the default MQTT port, specify <em>tcp://localhost:1883</em>. </td></tr>
    <tr><td class="paramname">clientId</td><td>The client identifier passed to the server when the client connects to it. It is a null-terminated UTF-8 encoded string. </td></tr>
    <tr><td class="paramname">persistence_type</td><td>The type of persistence to be used by the client: <br  />
 <a class="el" href="_m_q_t_t_client_persistence_8h.html#ae01e089313a65ac4661ed216b6ac00fa">MQTTCLIENT_PERSISTENCE_NONE</a>: Use in-memory persistence. If the device or system on which the client is running fails or is switched off, the current state of any in-flight messages is lost and some messages may not be delivered even at QoS1 and QoS2. <br  />
 <a class="el" href="_m_q_t_t_client_persistence_8h.html#aaa948291718a9c06369b854b0f64bc32">MQTTCLIENT_PERSISTENCE_DEFAULT</a>: Use the default (file system-based) persistence mechanism. Status about in-flight messages is held in persistent storage and provides some protection against message loss in the case of unexpected failure. <br  />
 <a class="el" href="_m_q_t_t_client_persistence_8h.html#a5dc68b8616e4041e037bad94ce07681b">MQTTCLIENT_PERSISTENCE_USER</a>: Use an application-specific persistence implementation. Using this type of persistence gives control of the persistence mechanism to the application. The application has to implement the <a class="el" href="struct_m_q_t_t_client__persistence.html" title="A structure containing the function pointers to a persistence implementation and the context or state...">MQTTClient_persistence</a> interface. </td></tr>
    <tr><td class="paramname">persistence_context</td><td>If the application uses <a class="el" href="_m_q_t_t_client_persistence_8h.html#ae01e089313a65ac4661ed216b6ac00fa">MQTTCLIENT_PERSISTENCE_NONE</a> persistence, this argument is unused and should be set to NULL. For <a class="el" href="_m_q_t_t_client_persistence_8h.html#aaa948291718a9c06369b854b0f64bc32">MQTTCLIENT_PERSISTENCE_DEFAULT</a> persistence, it should be set to the location of the persistence directory (if set to NULL, the persistence directory used is the working directory). Applications that use <a class="el" href="_m_q_t_t_client_persistence_8h.html#a5dc68b8616e4041e037bad94ce07681b">MQTTCLIENT_PERSISTENCE_USER</a> persistence set this argument to point to a valid <a class="el" href="struct_m_q_t_t_client__persistence.html" title="A structure containing the function pointers to a persistence implementation and the context or state...">MQTTClient_persistence</a> structure. </td></tr>
    <tr><td class="paramname">options</td><td>additional options for the create. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a> if the client is successfully created, otherwise an error code is returned. </dd></dl>

</div>
</div>
<a id="aef6eba956a5ff6072854a9e353487087" name="aef6eba956a5ff6072854a9e353487087"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aef6eba956a5ff6072854a9e353487087">&#9670;&#160;</a></span>MQTTClient_getVersionInfo()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_client__name_value.html">MQTTClient_nameValue</a> * MQTTClient_getVersionInfo </td>
          <td>(</td>
          <td class="paramtype">void</td>          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function returns version information about the library. no trace information will be returned. </p><dl class="section return"><dt>Returns</dt><dd>an array of strings describing the library. The last entry is a NULL pointer. </dd></dl>

</div>
</div>
<a id="aaa8ae61cd65c9dc0846df10122d7bd4e" name="aaa8ae61cd65c9dc0846df10122d7bd4e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaa8ae61cd65c9dc0846df10122d7bd4e">&#9670;&#160;</a></span>MQTTClient_connect()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_connect </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_client__connect_options.html">MQTTClient_connectOptions</a> *</td>          <td class="paramname"><span class="paramname"><em>options</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function attempts to connect a previously-created client (see <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>) to an MQTT server using the specified options. If you want to enable asynchronous message and status notifications, you must call <a class="el" href="#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks()</a> prior to <a class="el" href="#aaa8ae61cd65c9dc0846df10122d7bd4e">MQTTClient_connect()</a>. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">options</td><td>A pointer to a valid <a class="el" href="struct_m_q_t_t_client__connect_options.html">MQTTClient_connectOptions</a> structure. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a> if the client successfully connects to the server. An error code is returned if the client was unable to connect to the server. Error codes greater than 0 are returned by the MQTT protocol:<br  />
<br  />
 <b>1</b>: Connection refused: Unacceptable protocol version<br  />
 <b>2</b>: Connection refused: Identifier rejected<br  />
 <b>3</b>: Connection refused: Server unavailable<br  />
 <b>4</b>: Connection refused: Bad user name or password<br  />
 <b>5</b>: Connection refused: Not authorized<br  />
 <b>6-255</b>: Reserved for future use<br  />
 </dd></dl>

</div>
</div>
<a id="a01bd2c5f98ec5c0636a106db33f2b01b" name="a01bd2c5f98ec5c0636a106db33f2b01b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a01bd2c5f98ec5c0636a106db33f2b01b">&#9670;&#160;</a></span>MQTTResponse_free()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTResponse_free </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_response.html">MQTTResponse</a></td>          <td class="paramname"><span class="paramname"><em>response</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Frees the storage associated with the MQTT response. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">response</td><td>the response structure to be freed </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aa777f80cb3eec5610f976aff30b8c0d6" name="aa777f80cb3eec5610f976aff30b8c0d6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa777f80cb3eec5610f976aff30b8c0d6">&#9670;&#160;</a></span>MQTTClient_connect5()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_response.html">MQTTResponse</a> MQTTClient_connect5 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_client__connect_options.html">MQTTClient_connectOptions</a> *</td>          <td class="paramname"><span class="paramname"><em>options</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *</td>          <td class="paramname"><span class="paramname"><em>connectProperties</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *</td>          <td class="paramname"><span class="paramname"><em>willProperties</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Attempts to connect a previously-created client (see <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>) to an MQTT server using MQTT version 5.0 and the specified options. If you want to enable asynchronous message and status notifications, you must call <a class="el" href="#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks()</a> prior to <a class="el" href="#aaa8ae61cd65c9dc0846df10122d7bd4e">MQTTClient_connect()</a>. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">options</td><td>A pointer to a valid <a class="el" href="struct_m_q_t_t_client__connect_options.html">MQTTClient_connectOptions</a> structure. </td></tr>
    <tr><td class="paramname">connectProperties</td><td>the MQTT 5.0 connect properties to use </td></tr>
    <tr><td class="paramname">willProperties</td><td>the MQTT 5.0 properties to set on the will message </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the MQTT 5.0 response information: error codes and properties. </dd></dl>

</div>
</div>
<a id="a1e4d90c13a3c0705bc4a13bfe64e6525" name="a1e4d90c13a3c0705bc4a13bfe64e6525"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1e4d90c13a3c0705bc4a13bfe64e6525">&#9670;&#160;</a></span>MQTTClient_disconnect()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_disconnect </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>timeout</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function attempts to disconnect the client from the MQTT server. In order to allow the client time to complete handling of messages that are in-flight when this function is called, a timeout period is specified. When the timeout period has expired, the client disconnects even if there are still outstanding message acknowledgements. The next time the client connects to the same server, any QoS 1 or 2 messages which have not completed will be retried depending on the cleansession settings for both the previous and the new connection (see <a class="el" href="struct_m_q_t_t_client__connect_options.html#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">MQTTClient_connectOptions.cleansession</a> and <a class="el" href="#aaa8ae61cd65c9dc0846df10122d7bd4e">MQTTClient_connect()</a>). </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">timeout</td><td>The client delays disconnection for up to this time (in milliseconds) in order to allow in-flight message transfers to complete. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a> if the client successfully disconnects from the server. An error code is returned if the client was unable to disconnect from the server </dd></dl>

</div>
</div>
<a id="a1762c469715b7f718c4e63a427e6c13c" name="a1762c469715b7f718c4e63a427e6c13c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1762c469715b7f718c4e63a427e6c13c">&#9670;&#160;</a></span>MQTTClient_disconnect5()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_disconnect5 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>timeout</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a></td>          <td class="paramname"><span class="paramname"><em>reason</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *</td>          <td class="paramname"><span class="paramname"><em>props</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a id="a6e8231e8c47f6f67f7ebbb5dcb4c69c0" name="a6e8231e8c47f6f67f7ebbb5dcb4c69c0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6e8231e8c47f6f67f7ebbb5dcb4c69c0">&#9670;&#160;</a></span>MQTTClient_isConnected()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_isConnected </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function allows the client application to test whether or not a client is currently connected to the MQTT server. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Boolean true if the client is connected, otherwise false. </dd></dl>

</div>
</div>
<a id="a9c1c28258f0d5c6a44ff53a98618f5f3" name="a9c1c28258f0d5c6a44ff53a98618f5f3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9c1c28258f0d5c6a44ff53a98618f5f3">&#9670;&#160;</a></span>MQTTClient_subscribe()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_subscribe </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *</td>          <td class="paramname"><span class="paramname"><em>topic</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>qos</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function attempts to subscribe a client to a single topic, which may contain wildcards (see <a class="el" href="wildcard.html">Subscription wildcards</a>). This call also specifies the <a class="el" href="qos.html">Quality of service</a> requested for the subscription (see also <a class="el" href="#a92fa1c13f3db8399e042fbdbdfb692b3">MQTTClient_subscribeMany()</a>). </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">topic</td><td>The subscription topic, which may include wildcards. </td></tr>
    <tr><td class="paramname">qos</td><td>The requested quality of service for the subscription. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a> if the subscription request is successful. An error code is returned if there was a problem registering the subscription. </dd></dl>

</div>
</div>
<a id="af35ab7375435f7b6388c5ff4610dad3d" name="af35ab7375435f7b6388c5ff4610dad3d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af35ab7375435f7b6388c5ff4610dad3d">&#9670;&#160;</a></span>MQTTClient_subscribe5()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_response.html">MQTTResponse</a> MQTTClient_subscribe5 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *</td>          <td class="paramname"><span class="paramname"><em>topic</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>qos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a> *</td>          <td class="paramname"><span class="paramname"><em>opts</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *</td>          <td class="paramname"><span class="paramname"><em>props</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function attempts to subscribe an MQTT version 5.0 client to a single topic, which may contain wildcards (see <a class="el" href="wildcard.html">Subscription wildcards</a>). This call also specifies the <a class="el" href="qos.html">Quality of service</a> requested for the subscription (see also <a class="el" href="#a92fa1c13f3db8399e042fbdbdfb692b3">MQTTClient_subscribeMany()</a>). </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">topic</td><td>The subscription topic, which may include wildcards. </td></tr>
    <tr><td class="paramname">qos</td><td>The requested quality of service for the subscription. </td></tr>
    <tr><td class="paramname">opts</td><td>the MQTT 5.0 subscribe options to be used </td></tr>
    <tr><td class="paramname">props</td><td>the MQTT 5.0 properties to be used </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the MQTT 5.0 response information: error codes and properties. </dd></dl>

</div>
</div>
<a id="a92fa1c13f3db8399e042fbdbdfb692b3" name="a92fa1c13f3db8399e042fbdbdfb692b3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a92fa1c13f3db8399e042fbdbdfb692b3">&#9670;&#160;</a></span>MQTTClient_subscribeMany()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_subscribeMany </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>count</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char *const *</td>          <td class="paramname"><span class="paramname"><em>topic</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *</td>          <td class="paramname"><span class="paramname"><em>qos</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function attempts to subscribe a client to a list of topics, which may contain wildcards (see <a class="el" href="wildcard.html">Subscription wildcards</a>). This call also specifies the <a class="el" href="qos.html">Quality of service</a> requested for each topic (see also <a class="el" href="#a9c1c28258f0d5c6a44ff53a98618f5f3">MQTTClient_subscribe()</a>). </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">count</td><td>The number of topics for which the client is requesting subscriptions. </td></tr>
    <tr><td class="paramname">topic</td><td>An array (of length <em>count</em>) of pointers to topics, each of which may include wildcards. </td></tr>
    <tr><td class="paramname">qos</td><td>An array (of length <em>count</em>) of <a class="el" href="qos.html">Quality of service</a> values. qos[n] is the requested QoS for topic[n]. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a> if the subscription request is successful. An error code is returned if there was a problem registering the subscriptions. </dd></dl>

</div>
</div>
<a id="a5390c2402f135c12826ffbf6fc261f7c" name="a5390c2402f135c12826ffbf6fc261f7c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5390c2402f135c12826ffbf6fc261f7c">&#9670;&#160;</a></span>MQTTClient_subscribeMany5()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_response.html">MQTTResponse</a> MQTTClient_subscribeMany5 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>count</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char *const *</td>          <td class="paramname"><span class="paramname"><em>topic</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *</td>          <td class="paramname"><span class="paramname"><em>qos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a> *</td>          <td class="paramname"><span class="paramname"><em>opts</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *</td>          <td class="paramname"><span class="paramname"><em>props</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function attempts to subscribe an MQTT version 5.0 client to a list of topics, which may contain wildcards (see <a class="el" href="wildcard.html">Subscription wildcards</a>). This call also specifies the <a class="el" href="qos.html">Quality of service</a> requested for each topic (see also <a class="el" href="#a9c1c28258f0d5c6a44ff53a98618f5f3">MQTTClient_subscribe()</a>). </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">count</td><td>The number of topics for which the client is requesting subscriptions. </td></tr>
    <tr><td class="paramname">topic</td><td>An array (of length <em>count</em>) of pointers to topics, each of which may include wildcards. </td></tr>
    <tr><td class="paramname">qos</td><td>An array (of length <em>count</em>) of <a class="el" href="qos.html">Quality of service</a> values. qos[n] is the requested QoS for topic[n]. </td></tr>
    <tr><td class="paramname">opts</td><td>the MQTT 5.0 subscribe options to be used </td></tr>
    <tr><td class="paramname">props</td><td>the MQTT 5.0 properties to be used </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the MQTT 5.0 response information: error codes and properties. </dd></dl>

</div>
</div>
<a id="aa8731be3dbc6a25f41f037f8bbbb054b" name="aa8731be3dbc6a25f41f037f8bbbb054b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa8731be3dbc6a25f41f037f8bbbb054b">&#9670;&#160;</a></span>MQTTClient_unsubscribe()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_unsubscribe </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *</td>          <td class="paramname"><span class="paramname"><em>topic</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function attempts to remove an existing subscription made by the specified client. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">topic</td><td>The topic for the subscription to be removed, which may include wildcards (see <a class="el" href="wildcard.html">Subscription wildcards</a>). </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a> if the subscription is removed. An error code is returned if there was a problem removing the subscription. </dd></dl>

</div>
</div>
<a id="a58356c13867f18df60fd4c7ec9457c48" name="a58356c13867f18df60fd4c7ec9457c48"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a58356c13867f18df60fd4c7ec9457c48">&#9670;&#160;</a></span>MQTTClient_unsubscribe5()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_response.html">MQTTResponse</a> MQTTClient_unsubscribe5 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *</td>          <td class="paramname"><span class="paramname"><em>topic</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *</td>          <td class="paramname"><span class="paramname"><em>props</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function attempts to remove an existing subscription made by the specified client using MQTT 5.0. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">topic</td><td>The topic for the subscription to be removed, which may include wildcards (see <a class="el" href="wildcard.html">Subscription wildcards</a>). </td></tr>
    <tr><td class="paramname">props</td><td>the MQTT 5.0 properties to be used </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the MQTT 5.0 response information: error codes and properties. </dd></dl>

</div>
</div>
<a id="a50abbce720d50b9f84b97ff9fa1f546d" name="a50abbce720d50b9f84b97ff9fa1f546d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a50abbce720d50b9f84b97ff9fa1f546d">&#9670;&#160;</a></span>MQTTClient_unsubscribeMany()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_unsubscribeMany </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>count</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char *const *</td>          <td class="paramname"><span class="paramname"><em>topic</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function attempts to remove existing subscriptions to a list of topics made by the specified client. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">count</td><td>The number subscriptions to be removed. </td></tr>
    <tr><td class="paramname">topic</td><td>An array (of length <em>count</em>) of pointers to the topics of the subscriptions to be removed, each of which may include wildcards. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a> if the subscriptions are removed. An error code is returned if there was a problem removing the subscriptions. </dd></dl>

</div>
</div>
<a id="a46bdb532d2153110ccffb2f0748d1ba5" name="a46bdb532d2153110ccffb2f0748d1ba5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a46bdb532d2153110ccffb2f0748d1ba5">&#9670;&#160;</a></span>MQTTClient_unsubscribeMany5()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_response.html">MQTTResponse</a> MQTTClient_unsubscribeMany5 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>count</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char *const *</td>          <td class="paramname"><span class="paramname"><em>topic</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *</td>          <td class="paramname"><span class="paramname"><em>props</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function attempts to remove existing subscriptions to a list of topics made by the specified client using MQTT version 5.0. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">count</td><td>The number subscriptions to be removed. </td></tr>
    <tr><td class="paramname">topic</td><td>An array (of length <em>count</em>) of pointers to the topics of the subscriptions to be removed, each of which may include wildcards. </td></tr>
    <tr><td class="paramname">props</td><td>the MQTT 5.0 properties to be used </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the MQTT 5.0 response information: error codes and properties. </dd></dl>

</div>
</div>
<a id="afe9c34013c3511b8ef6cd36bf703678d" name="afe9c34013c3511b8ef6cd36bf703678d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afe9c34013c3511b8ef6cd36bf703678d">&#9670;&#160;</a></span>MQTTClient_publish()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_publish </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *</td>          <td class="paramname"><span class="paramname"><em>topicName</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>payloadlen</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const void *</td>          <td class="paramname"><span class="paramname"><em>payload</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>qos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>retained</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> *</td>          <td class="paramname"><span class="paramname"><em>dt</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function attempts to publish a message to a given topic (see also <a class="el" href="#ace320b8a92c7087d9dd5cf242d50389d">MQTTClient_publishMessage()</a>). An <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> is issued when this function returns successfully. If the client application needs to test for succesful delivery of QoS1 and QoS2 messages, this can be done either asynchronously or synchronously (see <a class="el" href="async.html">Asynchronous vs synchronous client applications</a>, <a class="el" href="#a83807ec81fe8c3941e368ab329d43067">MQTTClient_waitForCompletion</a> and <a class="el" href="#abef83794d8252551ed248cde6eb845a6">MQTTClient_deliveryComplete()</a>). </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">topicName</td><td>The topic associated with this message. </td></tr>
    <tr><td class="paramname">payloadlen</td><td>The length of the payload in bytes. </td></tr>
    <tr><td class="paramname">payload</td><td>A pointer to the byte array payload of the message. </td></tr>
    <tr><td class="paramname">qos</td><td>The <a class="el" href="qos.html">Quality of service</a> of the message. </td></tr>
    <tr><td class="paramname">retained</td><td>The retained flag for the message. </td></tr>
    <tr><td class="paramname">dt</td><td>A pointer to an <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a>. This is populated with a token representing the message when the function returns successfully. If your application does not use delivery tokens, set this argument to NULL. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a> if the message is accepted for publication. An error code is returned if there was a problem accepting the message. </dd></dl>

</div>
</div>
<a id="a8148186cc7683a6bb57f621653df51df" name="a8148186cc7683a6bb57f621653df51df"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8148186cc7683a6bb57f621653df51df">&#9670;&#160;</a></span>MQTTClient_publish5()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_response.html">MQTTResponse</a> MQTTClient_publish5 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *</td>          <td class="paramname"><span class="paramname"><em>topicName</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>payloadlen</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const void *</td>          <td class="paramname"><span class="paramname"><em>payload</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>qos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>retained</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *</td>          <td class="paramname"><span class="paramname"><em>properties</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> *</td>          <td class="paramname"><span class="paramname"><em>dt</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Attempts to publish a message to a given topic using MQTT version 5.0 (see also <a class="el" href="#a362042ce973c012bad6a1aa3b5984f5d">MQTTClient_publishMessage5()</a>). An <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> is issued when this function returns successfully. If the client application needs to test for succesful delivery of QoS1 and QoS2 messages, this can be done either asynchronously or synchronously (see <a class="el" href="async.html">Asynchronous vs synchronous client applications</a>, <a class="el" href="#a83807ec81fe8c3941e368ab329d43067">MQTTClient_waitForCompletion</a> and <a class="el" href="#abef83794d8252551ed248cde6eb845a6">MQTTClient_deliveryComplete()</a>). </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">topicName</td><td>The topic associated with this message. </td></tr>
    <tr><td class="paramname">payloadlen</td><td>The length of the payload in bytes. </td></tr>
    <tr><td class="paramname">payload</td><td>A pointer to the byte array payload of the message. </td></tr>
    <tr><td class="paramname">qos</td><td>The <a class="el" href="qos.html">Quality of service</a> of the message. </td></tr>
    <tr><td class="paramname">retained</td><td>The retained flag for the message. </td></tr>
    <tr><td class="paramname">properties</td><td>the MQTT 5.0 properties to be used </td></tr>
    <tr><td class="paramname">dt</td><td>A pointer to an <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a>. This is populated with a token representing the message when the function returns successfully. If your application does not use delivery tokens, set this argument to NULL. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the MQTT 5.0 response information: error codes and properties. </dd></dl>

</div>
</div>
<a id="ace320b8a92c7087d9dd5cf242d50389d" name="ace320b8a92c7087d9dd5cf242d50389d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ace320b8a92c7087d9dd5cf242d50389d">&#9670;&#160;</a></span>MQTTClient_publishMessage()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_publishMessage </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *</td>          <td class="paramname"><span class="paramname"><em>topicName</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a> *</td>          <td class="paramname"><span class="paramname"><em>msg</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> *</td>          <td class="paramname"><span class="paramname"><em>dt</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function attempts to publish a message to a given topic (see also <a class="el" href="#afe9c34013c3511b8ef6cd36bf703678d">MQTTClient_publish()</a>). An <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> is issued when this function returns successfully. If the client application needs to test for succesful delivery of QoS1 and QoS2 messages, this can be done either asynchronously or synchronously (see <a class="el" href="async.html">Asynchronous vs synchronous client applications</a>, <a class="el" href="#a83807ec81fe8c3941e368ab329d43067">MQTTClient_waitForCompletion</a> and <a class="el" href="#abef83794d8252551ed248cde6eb845a6">MQTTClient_deliveryComplete()</a>). </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">topicName</td><td>The topic associated with this message. </td></tr>
    <tr><td class="paramname">msg</td><td>A pointer to a valid <a class="el" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a> structure containing the payload and attributes of the message to be published. </td></tr>
    <tr><td class="paramname">dt</td><td>A pointer to an <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a>. This is populated with a token representing the message when the function returns successfully. If your application does not use delivery tokens, set this argument to NULL. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a> if the message is accepted for publication. An error code is returned if there was a problem accepting the message. </dd></dl>

</div>
</div>
<a id="a362042ce973c012bad6a1aa3b5984f5d" name="a362042ce973c012bad6a1aa3b5984f5d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a362042ce973c012bad6a1aa3b5984f5d">&#9670;&#160;</a></span>MQTTClient_publishMessage5()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_response.html">MQTTResponse</a> MQTTClient_publishMessage5 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *</td>          <td class="paramname"><span class="paramname"><em>topicName</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a> *</td>          <td class="paramname"><span class="paramname"><em>msg</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> *</td>          <td class="paramname"><span class="paramname"><em>dt</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Attempts to publish a message to the given topic using MQTT version 5.0 (see also <a class="el" href="#a8148186cc7683a6bb57f621653df51df">MQTTClient_publish5()</a>). An <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> is issued when this function returns successfully. If the client application needs to test for succesful delivery of QoS1 and QoS2 messages, this can be done either asynchronously or synchronously (see <a class="el" href="async.html">Asynchronous vs synchronous client applications</a>, <a class="el" href="#a83807ec81fe8c3941e368ab329d43067">MQTTClient_waitForCompletion</a> and <a class="el" href="#abef83794d8252551ed248cde6eb845a6">MQTTClient_deliveryComplete()</a>). </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">topicName</td><td>The topic associated with this message. </td></tr>
    <tr><td class="paramname">msg</td><td>A pointer to a valid <a class="el" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a> structure containing the payload and attributes of the message to be published. </td></tr>
    <tr><td class="paramname">dt</td><td>A pointer to an <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a>. This is populated with a token representing the message when the function returns successfully. If your application does not use delivery tokens, set this argument to NULL. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the MQTT 5.0 response information: error codes and properties. </dd></dl>

</div>
</div>
<a id="a83807ec81fe8c3941e368ab329d43067" name="a83807ec81fe8c3941e368ab329d43067"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a83807ec81fe8c3941e368ab329d43067">&#9670;&#160;</a></span>MQTTClient_waitForCompletion()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_waitForCompletion </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a></td>          <td class="paramname"><span class="paramname"><em>dt</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">unsigned long</td>          <td class="paramname"><span class="paramname"><em>timeout</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function is called by the client application to synchronize execution of the main thread with completed publication of a message. When called, <a class="el" href="#a83807ec81fe8c3941e368ab329d43067">MQTTClient_waitForCompletion()</a> blocks execution until the message has been successful delivered or the specified timeout has expired. See <a class="el" href="async.html">Asynchronous vs synchronous client applications</a>. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">dt</td><td>The <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> that represents the message being tested for successful delivery. Delivery tokens are issued by the publishing functions <a class="el" href="#afe9c34013c3511b8ef6cd36bf703678d">MQTTClient_publish()</a> and <a class="el" href="#ace320b8a92c7087d9dd5cf242d50389d">MQTTClient_publishMessage()</a>. </td></tr>
    <tr><td class="paramname">timeout</td><td>The maximum time to wait in milliseconds. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a> if the message was successfully delivered. An error code is returned if the timeout expires or there was a problem checking the token. </dd></dl>

</div>
</div>
<a id="a2a617c6b0492c04a4ddea592f5e53604" name="a2a617c6b0492c04a4ddea592f5e53604"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2a617c6b0492c04a4ddea592f5e53604">&#9670;&#160;</a></span>MQTTClient_getPendingDeliveryTokens()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_getPendingDeliveryTokens </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> **</td>          <td class="paramname"><span class="paramname"><em>tokens</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function sets a pointer to an array of delivery tokens for messages that are currently in-flight (pending completion).</p>
<p><b>Important note:</b> The memory used to hold the array of tokens is malloc()'d in this function. The client application is responsible for freeing this memory when it is no longer required. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">tokens</td><td>The address of a pointer to an <a class="el" href="#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a>. When the function returns successfully, the pointer is set to point to an array of tokens representing messages pending completion. The last member of the array is set to -1 to indicate there are no more tokens. If no tokens are pending, the pointer is set to NULL. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a> if the function returns successfully. An error code is returned if there was a problem obtaining the list of pending tokens. </dd></dl>

</div>
</div>
<a id="a8ad3d29864a9ca08202b0832e0f6678e" name="a8ad3d29864a9ca08202b0832e0f6678e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8ad3d29864a9ca08202b0832e0f6678e">&#9670;&#160;</a></span>MQTTClient_yield()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTClient_yield </td>
          <td>(</td>
          <td class="paramtype">void</td>          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>When implementing a single-threaded client, call this function periodically to allow processing of message retries and to send MQTT keepalive pings. If the application is calling <a class="el" href="#a4c2df88d00a3dadd510a8cb774739366">MQTTClient_receive()</a> regularly, then it is not necessary to call this function. </p>

</div>
</div>
<a id="a4c2df88d00a3dadd510a8cb774739366" name="a4c2df88d00a3dadd510a8cb774739366"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4c2df88d00a3dadd510a8cb774739366">&#9670;&#160;</a></span>MQTTClient_receive()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_receive </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char **</td>          <td class="paramname"><span class="paramname"><em>topicName</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *</td>          <td class="paramname"><span class="paramname"><em>topicLen</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a> **</td>          <td class="paramname"><span class="paramname"><em>message</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">unsigned long</td>          <td class="paramname"><span class="paramname"><em>timeout</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function performs a synchronous receive of incoming messages. It should be used only when the client application has not set callback methods to support asynchronous receipt of messages (see <a class="el" href="async.html">Asynchronous vs synchronous client applications</a> and <a class="el" href="#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks()</a>). Using this function allows a single-threaded client subscriber application to be written. When called, this function blocks until the next message arrives or the specified timeout expires (see also <a class="el" href="#a8ad3d29864a9ca08202b0832e0f6678e">MQTTClient_yield()</a>).</p>
<p><b>Important note:</b> The application must free() the memory allocated to the topic and the message when processing is complete (see <a class="el" href="#abd8abde4f39d3e689029de27f7a98a65">MQTTClient_freeMessage()</a>). </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">topicName</td><td>The address of a pointer to a topic. This function allocates the memory for the topic and returns it to the application by setting <em>topicName</em> to point to the topic. </td></tr>
    <tr><td class="paramname">topicLen</td><td>The length of the topic. If the return code from this function is <a class="el" href="#a29afebfce0bdf6cda1e37abc0c4b6690">MQTTCLIENT_TOPICNAME_TRUNCATED</a>, the topic contains embedded NULL characters and the full topic should be retrieved by using <em>topicLen</em>. </td></tr>
    <tr><td class="paramname">message</td><td>The address of a pointer to the received message. This function allocates the memory for the message and returns it to the application by setting <em>message</em> to point to the received message. The pointer is set to NULL if the timeout expires. </td></tr>
    <tr><td class="paramname">timeout</td><td>The length of time to wait for a message in milliseconds. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a> or <a class="el" href="#a29afebfce0bdf6cda1e37abc0c4b6690">MQTTCLIENT_TOPICNAME_TRUNCATED</a> if a message is received. <a class="el" href="#acba095704d79e5a1996389fa26203f73">MQTTCLIENT_SUCCESS</a> can also indicate that the timeout expired, in which case <em>message</em> is NULL. An error code is returned if there was a problem trying to receive a message. </dd></dl>

</div>
</div>
<a id="abd8abde4f39d3e689029de27f7a98a65" name="abd8abde4f39d3e689029de27f7a98a65"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abd8abde4f39d3e689029de27f7a98a65">&#9670;&#160;</a></span>MQTTClient_freeMessage()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTClient_freeMessage </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a> **</td>          <td class="paramname"><span class="paramname"><em>msg</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function frees memory allocated to an MQTT message, including the additional memory allocated to the message payload. The client application calls this function when the message has been fully processed. <b>Important note:</b> This function does not free the memory allocated to a message topic string. It is the responsibility of the client application to free this memory using the <a class="el" href="#a203b545c999beb6b825ec99b6aea79ab">MQTTClient_free()</a> library function. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">msg</td><td>The address of a pointer to the <a class="el" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a> structure to be freed. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a203b545c999beb6b825ec99b6aea79ab" name="a203b545c999beb6b825ec99b6aea79ab"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a203b545c999beb6b825ec99b6aea79ab">&#9670;&#160;</a></span>MQTTClient_free()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTClient_free </td>
          <td>(</td>
          <td class="paramtype">void *</td>          <td class="paramname"><span class="paramname"><em>ptr</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function frees memory allocated by the MQTT C client library, especially the topic name. This is needed on Windows when the client libary and application program have been compiled with different versions of the C compiler. It is thus good policy to always use this function when freeing any MQTT C client- allocated memory. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">ptr</td><td>The pointer to the client library storage to be freed. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a1f3ae01af021b014df147c9996156a69" name="a1f3ae01af021b014df147c9996156a69"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1f3ae01af021b014df147c9996156a69">&#9670;&#160;</a></span>MQTTClient_malloc()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void * MQTTClient_malloc </td>
          <td>(</td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>size</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function is used to allocate memory to be used or freed by the MQTT C client library, especially the data in user persistence. This is needed on Windows when the client library and application program have been compiled with different versions of the C compiler. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">size</td><td>The size of the memory to be allocated. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ae700c3f5cfea3813264ce95e7c8cf498" name="ae700c3f5cfea3813264ce95e7c8cf498"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae700c3f5cfea3813264ce95e7c8cf498">&#9670;&#160;</a></span>MQTTClient_destroy()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTClient_destroy </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> *</td>          <td class="paramname"><span class="paramname"><em>handle</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function frees the memory allocated to an MQTT client (see <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>). It should be called when the client is no longer required. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A pointer to the handle referring to the <a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> structure to be freed. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a4dfa35d29db54b10b15b8ac2d9a778be" name="a4dfa35d29db54b10b15b8ac2d9a778be"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4dfa35d29db54b10b15b8ac2d9a778be">&#9670;&#160;</a></span>MQTTClient_setTraceLevel()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTClient_setTraceLevel </td>
          <td>(</td>
          <td class="paramtype">enum <a class="el" href="#aa0ae95caa9c16d152b5036b1bac2e09b">MQTTCLIENT_TRACE_LEVELS</a></td>          <td class="paramname"><span class="paramname"><em>level</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function sets the level of trace information which will be returned in the trace callback. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">level</td><td>the trace level required </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a22870f94aa4cb1827626612f1ded7c69" name="a22870f94aa4cb1827626612f1ded7c69"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a22870f94aa4cb1827626612f1ded7c69">&#9670;&#160;</a></span>MQTTClient_setTraceCallback()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTClient_setTraceCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#afa5758290a1162e5135bca97bbfd5774">MQTTClient_traceCallback</a> *</td>          <td class="paramname"><span class="paramname"><em>callback</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This function sets the trace callback if needed. If set to NULL, no trace information will be returned. The default trace level is MQTTASYNC_TRACE_MINIMUM. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">callback</td><td>a pointer to the function which will handle the trace information </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a96067a2fb74d2a61c7e93015629548e0" name="a96067a2fb74d2a61c7e93015629548e0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a96067a2fb74d2a61c7e93015629548e0">&#9670;&#160;</a></span>MQTTClient_setCommandTimeout()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTClient_setCommandTimeout </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></td>          <td class="paramname"><span class="paramname"><em>handle</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">unsigned long</td>          <td class="paramname"><span class="paramname"><em>milliSeconds</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sets the timeout value for un/subscribe commands when waiting for the un/suback response from the server. Values less than 5000 are not allowed. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. </td></tr>
    <tr><td class="paramname">milliSeconds</td><td>the maximum number of milliseconds to wait </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>MQTTCLIENT_SUCCESS or MQTTCLIENT_FAILURE </dd></dl>

</div>
</div>
<a id="a68535b4c6d8f28b29a52569926cdeb50" name="a68535b4c6d8f28b29a52569926cdeb50"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a68535b4c6d8f28b29a52569926cdeb50">&#9670;&#160;</a></span>MQTTClient_strerror()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char * MQTTClient_strerror </td>
          <td>(</td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>code</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a pointer to the string representation of the error or NULL.</p>
<p>Do not free after use. Returns NULL if the error code is unknown. </p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:06 for Paho MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
