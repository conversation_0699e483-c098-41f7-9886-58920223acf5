<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho MQTT C Client Library: MQTTClient.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">MQTTClient.h</div></div>
</div><!--header-->
<div class="contents">
<a href="_m_q_t_t_client_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="comment">/*******************************************************************************</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span><span class="comment"> * Copyright (c) 2009, 2025 IBM Corp., Ian Craggs and others</span></div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span><span class="comment"> *</span></div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="comment"> * All rights reserved. This program and the accompanying materials</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span><span class="comment"> * are made available under the terms of the Eclipse Public License v2.0</span></div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span><span class="comment"> * and Eclipse Distribution License v1.0 which accompany this distribution.</span></div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno">    7</span><span class="comment"> *</span></div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span><span class="comment"> * The Eclipse Public License is available at</span></div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno">    9</span><span class="comment"> *    https://www.eclipse.org/legal/epl-2.0/</span></div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno">   10</span><span class="comment"> * and the Eclipse Distribution License is available at</span></div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno">   11</span><span class="comment"> *   http://www.eclipse.org/org/documents/edl-v10.php.</span></div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno">   12</span><span class="comment"> *</span></div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span><span class="comment"> * Contributors:</span></div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno">   14</span><span class="comment"> *    Ian Craggs - initial API and implementation and/or initial documentation</span></div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span><span class="comment"> *    Ian Craggs, Allan Stockdill-Mander - SSL updates</span></div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno">   16</span><span class="comment"> *    Ian Craggs - multiple server connection support</span></div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno">   17</span><span class="comment"> *    Ian Craggs - MQTT 3.1.1 support</span></div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno">   18</span><span class="comment"> *    Ian Craggs - remove const from eyecatchers #168</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno">   19</span><span class="comment"> *******************************************************************************/</span></div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno">   20</span> </div>
<div class="line"><a id="l00107" name="l00107"></a><span class="lineno">  107</span><span class="comment">/*</span></div>
<div class="line"><a id="l00109" name="l00109"></a><span class="lineno">  109</span>*/</div>
<div class="line"><a id="l00110" name="l00110"></a><span class="lineno">  110</span><span class="preprocessor">#if !defined(MQTTCLIENT_H)</span></div>
<div class="line"><a id="l00111" name="l00111"></a><span class="lineno">  111</span><span class="preprocessor">#define MQTTCLIENT_H</span></div>
<div class="line"><a id="l00112" name="l00112"></a><span class="lineno">  112</span> </div>
<div class="line"><a id="l00113" name="l00113"></a><span class="lineno">  113</span><span class="preprocessor">#if defined(__cplusplus)</span></div>
<div class="line"><a id="l00114" name="l00114"></a><span class="lineno">  114</span> <span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> {</div>
<div class="line"><a id="l00115" name="l00115"></a><span class="lineno">  115</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00116" name="l00116"></a><span class="lineno">  116</span> </div>
<div class="line"><a id="l00117" name="l00117"></a><span class="lineno">  117</span><span class="preprocessor">#include &lt;stdio.h&gt;</span></div>
<div class="line"><a id="l00118" name="l00118"></a><span class="lineno">  118</span><span class="comment">/*</span></div>
<div class="line"><a id="l00120" name="l00120"></a><span class="lineno">  120</span>*/</div>
<div class="line"><a id="l00121" name="l00121"></a><span class="lineno">  121</span> </div>
<div class="line"><a id="l00122" name="l00122"></a><span class="lineno">  122</span><span class="preprocessor">#include &quot;MQTTExportDeclarations.h&quot;</span></div>
<div class="line"><a id="l00123" name="l00123"></a><span class="lineno">  123</span> </div>
<div class="line"><a id="l00124" name="l00124"></a><span class="lineno">  124</span><span class="preprocessor">#include &quot;<a class="code" href="_m_q_t_t_properties_8h.html">MQTTProperties.h</a>&quot;</span></div>
<div class="line"><a id="l00125" name="l00125"></a><span class="lineno">  125</span><span class="preprocessor">#include &quot;<a class="code" href="_m_q_t_t_reason_codes_8h.html">MQTTReasonCodes.h</a>&quot;</span></div>
<div class="line"><a id="l00126" name="l00126"></a><span class="lineno">  126</span><span class="preprocessor">#include &quot;<a class="code" href="_m_q_t_t_subscribe_opts_8h.html">MQTTSubscribeOpts.h</a>&quot;</span></div>
<div class="line"><a id="l00127" name="l00127"></a><span class="lineno">  127</span><span class="preprocessor">#if !defined(NO_PERSISTENCE)</span></div>
<div class="line"><a id="l00128" name="l00128"></a><span class="lineno">  128</span><span class="preprocessor">#include &quot;<a class="code" href="_m_q_t_t_client_persistence_8h.html">MQTTClientPersistence.h</a>&quot;</span></div>
<div class="line"><a id="l00129" name="l00129"></a><span class="lineno">  129</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l00130" name="l00130"></a><span class="lineno">  130</span><span class="preprocessor">#define MQTTCLIENT_PERSISTENCE_NONE 1</span></div>
<div class="line"><a id="l00131" name="l00131"></a><span class="lineno">  131</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00132" name="l00132"></a><span class="lineno">  132</span> </div>
<div class="line"><a id="l00137" name="l00137"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#acba095704d79e5a1996389fa26203f73">  137</a></span><span class="preprocessor">#define MQTTCLIENT_SUCCESS 0</span></div>
<div class="line"><a id="l00142" name="l00142"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#af33a6d6c0e8a6a747bf39638e0bba36b">  142</a></span><span class="preprocessor">#define MQTTCLIENT_FAILURE -1</span></div>
<div class="line"><a id="l00143" name="l00143"></a><span class="lineno">  143</span> </div>
<div class="line"><a id="l00144" name="l00144"></a><span class="lineno">  144</span><span class="comment">/* error code -2 is MQTTCLIENT_PERSISTENCE_ERROR */</span></div>
<div class="line"><a id="l00145" name="l00145"></a><span class="lineno">  145</span> </div>
<div class="line"><a id="l00149" name="l00149"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a561d053311cb492cf7226f419ee0d516">  149</a></span><span class="preprocessor">#define MQTTCLIENT_DISCONNECTED -3</span></div>
<div class="line"><a id="l00154" name="l00154"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a8fc442fc2e9dfb422a163ab1fa02e0cb">  154</a></span><span class="preprocessor">#define MQTTCLIENT_MAX_MESSAGES_INFLIGHT -4</span></div>
<div class="line"><a id="l00158" name="l00158"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a678a4744192de9c8dca220d9965809dd">  158</a></span><span class="preprocessor">#define MQTTCLIENT_BAD_UTF8_STRING -5</span></div>
<div class="line"><a id="l00162" name="l00162"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#ac3232abd7f86bbba26faea0e2b132c3c">  162</a></span><span class="preprocessor">#define MQTTCLIENT_NULL_PARAMETER -6</span></div>
<div class="line"><a id="l00168" name="l00168"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a29afebfce0bdf6cda1e37abc0c4b6690">  168</a></span><span class="preprocessor">#define MQTTCLIENT_TOPICNAME_TRUNCATED -7</span></div>
<div class="line"><a id="l00173" name="l00173"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a747615d8064e3fe024ae5565ec63e1ce">  173</a></span><span class="preprocessor">#define MQTTCLIENT_BAD_STRUCTURE -8</span></div>
<div class="line"><a id="l00177" name="l00177"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a51cc8ca032acf4ae14f83996524b8cdc">  177</a></span><span class="preprocessor">#define MQTTCLIENT_BAD_QOS -9</span></div>
<div class="line"><a id="l00181" name="l00181"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a1c67fc83ba1a8f26236aa49b127bdb61">  181</a></span><span class="preprocessor">#define MQTTCLIENT_SSL_NOT_SUPPORTED -10</span></div>
<div class="line"><a id="l00185" name="l00185"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aab84cecd25638896eb45b8f5ffd82bf7">  185</a></span><span class="preprocessor"> #define MQTTCLIENT_BAD_MQTT_VERSION -11</span></div>
<div class="line"><a id="l00195" name="l00195"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a1d0cb25b450136f036a238546487344a">  195</a></span><span class="preprocessor">#define MQTTCLIENT_BAD_PROTOCOL -14</span></div>
<div class="line"><a id="l00199" name="l00199"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a1babaca56ffae802fa1e246a2649927e">  199</a></span><span class="preprocessor"> #define MQTTCLIENT_BAD_MQTT_OPTION -15</span></div>
<div class="line"><a id="l00203" name="l00203"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#ae9070d21de569f999a9575049cdd6da1">  203</a></span><span class="preprocessor"> #define MQTTCLIENT_WRONG_MQTT_VERSION -16</span></div>
<div class="line"><a id="l00207" name="l00207"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aacf90ba5292e25122e6fd5ec2a38efe5">  207</a></span><span class="preprocessor"> #define MQTTCLIENT_0_LEN_WILL_TOPIC -17</span></div>
<div class="line"><a id="l00208" name="l00208"></a><span class="lineno">  208</span> </div>
<div class="line"><a id="l00209" name="l00209"></a><span class="lineno">  209</span> </div>
<div class="line"><a id="l00213" name="l00213"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a75b80b01f98d5a1ffa2a4d42995a8397">  213</a></span><span class="preprocessor">#define MQTTVERSION_DEFAULT 0</span></div>
<div class="line"><a id="l00217" name="l00217"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a4603b988e76872e1f23f135d225ce2fb">  217</a></span><span class="preprocessor">#define MQTTVERSION_3_1 3</span></div>
<div class="line"><a id="l00221" name="l00221"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#ac79cc6fdeaa9e3f4ee12c3418898b1ef">  221</a></span><span class="preprocessor">#define MQTTVERSION_3_1_1 4</span></div>
<div class="line"><a id="l00225" name="l00225"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#af8b176fa4d5b89789767ce972338e1e3">  225</a></span><span class="preprocessor"> #define MQTTVERSION_5 5</span></div>
<div class="line"><a id="l00229" name="l00229"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#ade337b363b7f4bc7c1a7b2858e0380bd">  229</a></span><span class="preprocessor">#define MQTT_BAD_SUBSCRIBE 0x80</span></div>
<div class="line"><a id="l00230" name="l00230"></a><span class="lineno">  230</span> </div>
<div class="foldopen" id="foldopen00234" data-start="{" data-end="};">
<div class="line"><a id="l00234" name="l00234"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__init__options.html">  234</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l00235" name="l00235"></a><span class="lineno">  235</span>{</div>
<div class="line"><a id="l00237" name="l00237"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__init__options.html#aa5326df180cb23c59afbcab711a06479">  237</a></span>        <span class="keywordtype">char</span> struct_id[4];</div>
<div class="line"><a id="l00239" name="l00239"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__init__options.html#a0761a5e5be0383882e42924de8e51f82">  239</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__init__options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a id="l00241" name="l00241"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__init__options.html#a5929146596391e2838ef95feb89776da">  241</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__init__options.html#a5929146596391e2838ef95feb89776da">do_openssl_init</a>;</div>
<div class="line"><a id="l00242" name="l00242"></a><span class="lineno">  242</span>} <a class="code hl_struct" href="struct_m_q_t_t_client__init__options.html">MQTTClient_init_options</a>;</div>
</div>
<div class="line"><a id="l00243" name="l00243"></a><span class="lineno">  243</span> </div>
<div class="line"><a id="l00244" name="l00244"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#ac17057c8c22c0717d3adf4e040440f73">  244</a></span><span class="preprocessor">#define MQTTClient_init_options_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;G&#39;}, 0, 0 }</span></div>
<div class="line"><a id="l00245" name="l00245"></a><span class="lineno">  245</span> </div>
<div class="line"><a id="l00250" name="l00250"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a21804ede1a506d1d69a472bc30acc8ba">  250</a></span>LIBMQTT_API <span class="keywordtype">void</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a21804ede1a506d1d69a472bc30acc8ba">MQTTClient_global_init</a>(<a class="code hl_struct" href="struct_m_q_t_t_client__init__options.html">MQTTClient_init_options</a>* inits);</div>
<div class="line"><a id="l00251" name="l00251"></a><span class="lineno">  251</span> </div>
<div class="line"><a id="l00256" name="l00256"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">  256</a></span><span class="keyword">typedef</span> <span class="keywordtype">void</span>* <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a>;</div>
<div class="line"><a id="l00267" name="l00267"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a73e49030fd8b7074aa1aa45669b7fe8d">  267</a></span><span class="keyword">typedef</span> <span class="keywordtype">int</span> <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a>;</div>
<div class="line"><a id="l00268" name="l00268"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a8b2beb5227708f8127b666f5a7fc41b3">  268</a></span><span class="keyword">typedef</span> <span class="keywordtype">int</span> <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a8b2beb5227708f8127b666f5a7fc41b3">MQTTClient_token</a>;</div>
<div class="line"><a id="l00269" name="l00269"></a><span class="lineno">  269</span> </div>
<div class="foldopen" id="foldopen00276" data-start="{" data-end="};">
<div class="line"><a id="l00276" name="l00276"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__message.html">  276</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l00277" name="l00277"></a><span class="lineno">  277</span>{</div>
<div class="line"><a id="l00279" name="l00279"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__message.html#aa5326df180cb23c59afbcab711a06479">  279</a></span>        <span class="keywordtype">char</span> struct_id[4];</div>
<div class="line"><a id="l00282" name="l00282"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__message.html#a0761a5e5be0383882e42924de8e51f82">  282</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__message.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a id="l00284" name="l00284"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__message.html#aa3cb44feb3ae6d11b3a4cad2d94cb33a">  284</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__message.html#aa3cb44feb3ae6d11b3a4cad2d94cb33a">payloadlen</a>;</div>
<div class="line"><a id="l00286" name="l00286"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__message.html#a9eff55064941fb604452abb0050ea99d">  286</a></span>        <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_client__message.html#a9eff55064941fb604452abb0050ea99d">payload</a>;</div>
<div class="line"><a id="l00300" name="l00300"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__message.html#a35738099155a0e4f54050da474bab2e7">  300</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__message.html#a35738099155a0e4f54050da474bab2e7">qos</a>;</div>
<div class="line"><a id="l00319" name="l00319"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__message.html#a6a4904c112507a43e7dc8495b62cc0fc">  319</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__message.html#a6a4904c112507a43e7dc8495b62cc0fc">retained</a>;</div>
<div class="line"><a id="l00326" name="l00326"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__message.html#adc4cf3f551bb367858644559d69cfdf5">  326</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__message.html#adc4cf3f551bb367858644559d69cfdf5">dup</a>;</div>
<div class="line"><a id="l00330" name="l00330"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__message.html#a6174c42da8c55c86e7255be2848dc4ac">  330</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__message.html#a6174c42da8c55c86e7255be2848dc4ac">msgid</a>;</div>
<div class="line"><a id="l00334" name="l00334"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__message.html#a1594008402f7307e4de8fa6131656dde">  334</a></span>        <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a> <a class="code hl_variable" href="struct_m_q_t_t_client__message.html#a1594008402f7307e4de8fa6131656dde">properties</a>;</div>
<div class="line"><a id="l00335" name="l00335"></a><span class="lineno">  335</span>} <a class="code hl_struct" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a>;</div>
</div>
<div class="line"><a id="l00336" name="l00336"></a><span class="lineno">  336</span> </div>
<div class="line"><a id="l00337" name="l00337"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aa1fd995924d3df75959fcf57e87aefac">  337</a></span><span class="preprocessor">#define MQTTClient_message_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;M&#39;}, 1, 0, NULL, 0, 0, 0, 0, MQTTProperties_initializer }</span></div>
<div class="line"><a id="l00338" name="l00338"></a><span class="lineno">  338</span> </div>
<div class="line"><a id="l00369" name="l00369"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aa42130dd069e7e949bcab37b6dce64a5">  369</a></span><span class="keyword">typedef</span> <span class="keywordtype">int</span> <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#aa42130dd069e7e949bcab37b6dce64a5">MQTTClient_messageArrived</a>(<span class="keywordtype">void</span>* context, <span class="keywordtype">char</span>* topicName, <span class="keywordtype">int</span> topicLen, <a class="code hl_struct" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a>* message);</div>
<div class="line"><a id="l00370" name="l00370"></a><span class="lineno">  370</span> </div>
<div class="line"><a id="l00391" name="l00391"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#abef83794d8252551ed248cde6eb845a6">  391</a></span><span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#abef83794d8252551ed248cde6eb845a6">MQTTClient_deliveryComplete</a>(<span class="keywordtype">void</span>* context, <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> dt);</div>
<div class="line"><a id="l00392" name="l00392"></a><span class="lineno">  392</span> </div>
<div class="line"><a id="l00408" name="l00408"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a6bb253f16754e7cc81798c9fda0e36cf">  408</a></span><span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a6bb253f16754e7cc81798c9fda0e36cf">MQTTClient_connectionLost</a>(<span class="keywordtype">void</span>* context, <span class="keywordtype">char</span>* cause);</div>
<div class="line"><a id="l00409" name="l00409"></a><span class="lineno">  409</span> </div>
<div class="line"><a id="l00439" name="l00439"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aad27d07782991a4937ebf2f39a021f83">  439</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keywordtype">void</span>* context, <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a6bb253f16754e7cc81798c9fda0e36cf">MQTTClient_connectionLost</a>* cl,</div>
<div class="line"><a id="l00440" name="l00440"></a><span class="lineno">  440</span>                                                                        <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#aa42130dd069e7e949bcab37b6dce64a5">MQTTClient_messageArrived</a>* ma, <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#abef83794d8252551ed248cde6eb845a6">MQTTClient_deliveryComplete</a>* dc);</div>
<div class="line"><a id="l00441" name="l00441"></a><span class="lineno">  441</span> </div>
<div class="line"><a id="l00442" name="l00442"></a><span class="lineno">  442</span> </div>
<div class="line"><a id="l00452" name="l00452"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a41108d4cccb67a9d6884ebae52211c46">  452</a></span><span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a41108d4cccb67a9d6884ebae52211c46">MQTTClient_disconnected</a>(<span class="keywordtype">void</span>* context, <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* properties,</div>
<div class="line"><a id="l00453" name="l00453"></a><span class="lineno">  453</span>                <span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reasonCode);</div>
<div class="line"><a id="l00454" name="l00454"></a><span class="lineno">  454</span> </div>
<div class="line"><a id="l00468" name="l00468"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a8adea083a162735d5c7592160088eea0">  468</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a8adea083a162735d5c7592160088eea0">MQTTClient_setDisconnected</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keywordtype">void</span>* context, <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a41108d4cccb67a9d6884ebae52211c46">MQTTClient_disconnected</a>* co);</div>
<div class="line"><a id="l00469" name="l00469"></a><span class="lineno">  469</span> </div>
<div class="line"><a id="l00493" name="l00493"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a6c3f51e50e2c47328eee1b0c920ed103">  493</a></span><span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a6c3f51e50e2c47328eee1b0c920ed103">MQTTClient_published</a>(<span class="keywordtype">void</span>* context, <span class="keywordtype">int</span> dt, <span class="keywordtype">int</span> packet_type, <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* properties,</div>
<div class="line"><a id="l00494" name="l00494"></a><span class="lineno">  494</span>                <span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reasonCode);</div>
<div class="line"><a id="l00495" name="l00495"></a><span class="lineno">  495</span> </div>
<div class="line"><a id="l00496" name="l00496"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a9f13911351a3de6b1ebdabd4cb4116ba">  496</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a9f13911351a3de6b1ebdabd4cb4116ba">MQTTClient_setPublished</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keywordtype">void</span>* context, <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a6c3f51e50e2c47328eee1b0c920ed103">MQTTClient_published</a>* co);</div>
<div class="line"><a id="l00497" name="l00497"></a><span class="lineno">  497</span> </div>
<div class="line"><a id="l00550" name="l00550"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a9a0518d9ca924d12c1329dbe3de5f2b6">  550</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a>* handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* serverURI, <span class="keyword">const</span> <span class="keywordtype">char</span>* clientId,</div>
<div class="line"><a id="l00551" name="l00551"></a><span class="lineno">  551</span>                <span class="keywordtype">int</span> persistence_type, <span class="keywordtype">void</span>* persistence_context);</div>
<div class="line"><a id="l00552" name="l00552"></a><span class="lineno">  552</span> </div>
<div class="foldopen" id="foldopen00554" data-start="{" data-end="};">
<div class="line"><a id="l00554" name="l00554"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__create_options.html">  554</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l00555" name="l00555"></a><span class="lineno">  555</span>{</div>
<div class="line"><a id="l00557" name="l00557"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__create_options.html#aa5326df180cb23c59afbcab711a06479">  557</a></span>        <span class="keywordtype">char</span> struct_id[4];</div>
<div class="line"><a id="l00559" name="l00559"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__create_options.html#a0761a5e5be0383882e42924de8e51f82">  559</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__create_options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a id="l00565" name="l00565"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__create_options.html#a12d546fd0ccf4e1091b18e1b735c7240">  565</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__create_options.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTVersion</a>;</div>
<div class="line"><a id="l00566" name="l00566"></a><span class="lineno">  566</span>} <a class="code hl_struct" href="struct_m_q_t_t_client__create_options.html">MQTTClient_createOptions</a>;</div>
</div>
<div class="line"><a id="l00567" name="l00567"></a><span class="lineno">  567</span> </div>
<div class="line"><a id="l00568" name="l00568"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a763e477a5aceb6aff279111c7693e691">  568</a></span><span class="preprocessor">#define MQTTClient_createOptions_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;C&#39;, &#39;O&#39;}, 0, MQTTVERSION_DEFAULT }</span></div>
<div class="line"><a id="l00569" name="l00569"></a><span class="lineno">  569</span> </div>
<div class="line"><a id="l00614" name="l00614"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#ade24f717a9b39d38b081e1d5e0db1661">  614</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#ade24f717a9b39d38b081e1d5e0db1661">MQTTClient_createWithOptions</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a>* handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* serverURI, <span class="keyword">const</span> <span class="keywordtype">char</span>* clientId,</div>
<div class="line"><a id="l00615" name="l00615"></a><span class="lineno">  615</span>                <span class="keywordtype">int</span> persistence_type, <span class="keywordtype">void</span>* persistence_context, <a class="code hl_struct" href="struct_m_q_t_t_client__create_options.html">MQTTClient_createOptions</a>* options);</div>
<div class="line"><a id="l00616" name="l00616"></a><span class="lineno">  616</span> </div>
<div class="foldopen" id="foldopen00629" data-start="{" data-end="};">
<div class="line"><a id="l00629" name="l00629"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__will_options.html">  629</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l00630" name="l00630"></a><span class="lineno">  630</span>{</div>
<div class="line"><a id="l00632" name="l00632"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__will_options.html#aa5326df180cb23c59afbcab711a06479">  632</a></span>        <span class="keywordtype">char</span> struct_id[4];</div>
<div class="line"><a id="l00636" name="l00636"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__will_options.html#a0761a5e5be0383882e42924de8e51f82">  636</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__will_options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a id="l00638" name="l00638"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__will_options.html#a0e20a7b350881d05108d6342884198a5">  638</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_client__will_options.html#a0e20a7b350881d05108d6342884198a5">topicName</a>;</div>
<div class="line"><a id="l00640" name="l00640"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__will_options.html#a254bf0858da09c96a48daf64404eb4f8">  640</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_client__will_options.html#a254bf0858da09c96a48daf64404eb4f8">message</a>;</div>
<div class="line"><a id="l00644" name="l00644"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__will_options.html#a6a4904c112507a43e7dc8495b62cc0fc">  644</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__will_options.html#a6a4904c112507a43e7dc8495b62cc0fc">retained</a>;</div>
<div class="line"><a id="l00649" name="l00649"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__will_options.html#a35738099155a0e4f54050da474bab2e7">  649</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__will_options.html#a35738099155a0e4f54050da474bab2e7">qos</a>;</div>
<div class="line"><a id="l00651" name="l00651"></a><span class="lineno">  651</span>        <span class="keyword">struct</span></div>
<div class="line"><a id="l00652" name="l00652"></a><span class="lineno">  652</span>        {</div>
<div class="line"><a id="l00653" name="l00653"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__will_options.html#afed088663f8704004425cdae2120b9b3">  653</a></span>                <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__will_options.html#afed088663f8704004425cdae2120b9b3">len</a>;            </div>
<div class="line"><a id="l00654" name="l00654"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__will_options.html#a0d49d74db4c035719c3867723cf7e779">  654</a></span>                <span class="keyword">const</span> <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_client__will_options.html#a0d49d74db4c035719c3867723cf7e779">data</a>;  </div>
<div class="line"><a id="l00655" name="l00655"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__will_options.html#a0e9356b973a918c25981982fe84e35d7">  655</a></span>        } payload;</div>
<div class="line"><a id="l00656" name="l00656"></a><span class="lineno">  656</span>} <a class="code hl_struct" href="struct_m_q_t_t_client__will_options.html">MQTTClient_willOptions</a>;</div>
</div>
<div class="line"><a id="l00657" name="l00657"></a><span class="lineno">  657</span> </div>
<div class="line"><a id="l00658" name="l00658"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aae0811659c59f5dad0467544f91645eb">  658</a></span><span class="preprocessor">#define MQTTClient_willOptions_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;W&#39;}, 1, NULL, NULL, 0, 0, {0, NULL} }</span></div>
<div class="line"><a id="l00659" name="l00659"></a><span class="lineno">  659</span> </div>
<div class="line"><a id="l00660" name="l00660"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a2549ea897af26c76198284731db9e721">  660</a></span><span class="preprocessor">#define MQTT_SSL_VERSION_DEFAULT 0</span></div>
<div class="line"><a id="l00661" name="l00661"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a7e5da3d6f0d2b53409bbfcf6e56f3d2d">  661</a></span><span class="preprocessor">#define MQTT_SSL_VERSION_TLS_1_0 1</span></div>
<div class="line"><a id="l00662" name="l00662"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#abdff87efa3f2ee473a1591e10638b537">  662</a></span><span class="preprocessor">#define MQTT_SSL_VERSION_TLS_1_1 2</span></div>
<div class="line"><a id="l00663" name="l00663"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a3a94dbdeafbb73c73a068e7c2085fbab">  663</a></span><span class="preprocessor">#define MQTT_SSL_VERSION_TLS_1_2 3</span></div>
<div class="line"><a id="l00664" name="l00664"></a><span class="lineno">  664</span> </div>
<div class="foldopen" id="foldopen00677" data-start="{" data-end="};">
<div class="line"><a id="l00677" name="l00677"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html">  677</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l00678" name="l00678"></a><span class="lineno">  678</span>{</div>
<div class="line"><a id="l00680" name="l00680"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html#aa5326df180cb23c59afbcab711a06479">  680</a></span>        <span class="keywordtype">char</span> struct_id[4];</div>
<div class="line"><a id="l00681" name="l00681"></a><span class="lineno">  681</span> </div>
<div class="line"><a id="l00689" name="l00689"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html#a0761a5e5be0383882e42924de8e51f82">  689</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client___s_s_l_options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a id="l00690" name="l00690"></a><span class="lineno">  690</span> </div>
<div class="line"><a id="l00692" name="l00692"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html#a032835d4c4a1c1e19b53c330a673a6e0">  692</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_client___s_s_l_options.html#a032835d4c4a1c1e19b53c330a673a6e0">trustStore</a>;</div>
<div class="line"><a id="l00693" name="l00693"></a><span class="lineno">  693</span> </div>
<div class="line"><a id="l00697" name="l00697"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html#a32b476382955289ce427112b59f21c3e">  697</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_client___s_s_l_options.html#a32b476382955289ce427112b59f21c3e">keyStore</a>;</div>
<div class="line"><a id="l00698" name="l00698"></a><span class="lineno">  698</span> </div>
<div class="line"><a id="l00702" name="l00702"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html#a7dd436cbb916fba200595c3519f09ec4">  702</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_client___s_s_l_options.html#a7dd436cbb916fba200595c3519f09ec4">privateKey</a>;</div>
<div class="line"><a id="l00703" name="l00703"></a><span class="lineno">  703</span> </div>
<div class="line"><a id="l00705" name="l00705"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html#abb427571ba37b51f6985f1a6906ca031">  705</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_client___s_s_l_options.html#abb427571ba37b51f6985f1a6906ca031">privateKeyPassword</a>;</div>
<div class="line"><a id="l00706" name="l00706"></a><span class="lineno">  706</span> </div>
<div class="line"><a id="l00715" name="l00715"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html#aa683926d52134077f27d6dc67bda13ab">  715</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_client___s_s_l_options.html#aa683926d52134077f27d6dc67bda13ab">enabledCipherSuites</a>;</div>
<div class="line"><a id="l00716" name="l00716"></a><span class="lineno">  716</span> </div>
<div class="line"><a id="l00718" name="l00718"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html#a75f6c13b7634e15f96dd9f17db6cf0be">  718</a></span>    <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client___s_s_l_options.html#a75f6c13b7634e15f96dd9f17db6cf0be">enableServerCertAuth</a>;</div>
<div class="line"><a id="l00719" name="l00719"></a><span class="lineno">  719</span> </div>
<div class="line"><a id="l00724" name="l00724"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html#a3543ea1481b68d73cdde833280bb9c45">  724</a></span>    <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client___s_s_l_options.html#a3543ea1481b68d73cdde833280bb9c45">sslVersion</a>;</div>
<div class="line"><a id="l00725" name="l00725"></a><span class="lineno">  725</span> </div>
<div class="line"><a id="l00731" name="l00731"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html#a94900629685d5ed08f66fd2931f573ce">  731</a></span>    <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client___s_s_l_options.html#a94900629685d5ed08f66fd2931f573ce">verify</a>;</div>
<div class="line"><a id="l00732" name="l00732"></a><span class="lineno">  732</span> </div>
<div class="line"><a id="l00738" name="l00738"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html#a3078b3c824cc9753a57898072445c34d">  738</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_client___s_s_l_options.html#a3078b3c824cc9753a57898072445c34d">CApath</a>;</div>
<div class="line"><a id="l00739" name="l00739"></a><span class="lineno">  739</span> </div>
<div class="line"><a id="l00744" name="l00744"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html#a21b6ca8a73ba197e65f6a93365d39c04">  744</a></span>    int (*ssl_error_cb) (<span class="keyword">const</span> <span class="keywordtype">char</span> *str, <span class="keywordtype">size_t</span> len, <span class="keywordtype">void</span> *u);</div>
<div class="line"><a id="l00745" name="l00745"></a><span class="lineno">  745</span> </div>
<div class="line"><a id="l00750" name="l00750"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html#a189f11195f4d5a70024adffdb050885f">  750</a></span>    <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_client___s_s_l_options.html#a189f11195f4d5a70024adffdb050885f">ssl_error_context</a>;</div>
<div class="line"><a id="l00751" name="l00751"></a><span class="lineno">  751</span> </div>
<div class="line"><a id="l00757" name="l00757"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html#a94317cdaf352f9ae496976f8a30f8fee">  757</a></span>        <span class="keywordtype">unsigned</span> int (*ssl_psk_cb) (<span class="keyword">const</span> <span class="keywordtype">char</span> *hint, <span class="keywordtype">char</span> *identity, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> max_identity_len, <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> *psk, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> max_psk_len, <span class="keywordtype">void</span> *u);</div>
<div class="line"><a id="l00758" name="l00758"></a><span class="lineno">  758</span> </div>
<div class="line"><a id="l00763" name="l00763"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html#ab7f597518dd5b9db5a515081f8e0bd1f">  763</a></span>        <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_client___s_s_l_options.html#ab7f597518dd5b9db5a515081f8e0bd1f">ssl_psk_context</a>;</div>
<div class="line"><a id="l00764" name="l00764"></a><span class="lineno">  764</span> </div>
<div class="line"><a id="l00770" name="l00770"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html#a0826fcae7c2816e04772c61542c6846b">  770</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client___s_s_l_options.html#a0826fcae7c2816e04772c61542c6846b">disableDefaultTrustStore</a>;</div>
<div class="line"><a id="l00771" name="l00771"></a><span class="lineno">  771</span> </div>
<div class="line"><a id="l00779" name="l00779"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html#a4f8661600fb8bacf031150f8dcd293a5">  779</a></span>        <span class="keyword">const</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> *<a class="code hl_variable" href="struct_m_q_t_t_client___s_s_l_options.html#a4f8661600fb8bacf031150f8dcd293a5">protos</a>;</div>
<div class="line"><a id="l00780" name="l00780"></a><span class="lineno">  780</span> </div>
<div class="line"><a id="l00785" name="l00785"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client___s_s_l_options.html#a26f5d839c92f9772c2a5d05486277a42">  785</a></span>        <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client___s_s_l_options.html#a26f5d839c92f9772c2a5d05486277a42">protos_len</a>;</div>
<div class="line"><a id="l00786" name="l00786"></a><span class="lineno">  786</span>} <a class="code hl_struct" href="struct_m_q_t_t_client___s_s_l_options.html">MQTTClient_SSLOptions</a>;</div>
</div>
<div class="line"><a id="l00787" name="l00787"></a><span class="lineno">  787</span> </div>
<div class="line"><a id="l00788" name="l00788"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#ab9b2a2c6b52dbb2ac842ad99a9ce6d99">  788</a></span><span class="preprocessor">#define MQTTClient_SSLOptions_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;S&#39;}, 5, NULL, NULL, NULL, NULL, NULL, 1, MQTT_SSL_VERSION_DEFAULT, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0 }</span></div>
<div class="line"><a id="l00789" name="l00789"></a><span class="lineno">  789</span> </div>
<div class="foldopen" id="foldopen00797" data-start="{" data-end="};">
<div class="line"><a id="l00797" name="l00797"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__name_value.html">  797</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l00798" name="l00798"></a><span class="lineno">  798</span>{</div>
<div class="line"><a id="l00799" name="l00799"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__name_value.html#a8f8f80d37794cde9472343e4487ba3eb">  799</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_client__name_value.html#a8f8f80d37794cde9472343e4487ba3eb">name</a>;</div>
<div class="line"><a id="l00800" name="l00800"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__name_value.html#a8556878012feffc9e0beb86cd78f424d">  800</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_client__name_value.html#a8556878012feffc9e0beb86cd78f424d">value</a>;</div>
<div class="line"><a id="l00801" name="l00801"></a><span class="lineno">  801</span>} <a class="code hl_struct" href="struct_m_q_t_t_client__name_value.html">MQTTClient_nameValue</a>;</div>
</div>
<div class="line"><a id="l00802" name="l00802"></a><span class="lineno">  802</span> </div>
<div class="line"><a id="l00808" name="l00808"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aef6eba956a5ff6072854a9e353487087">  808</a></span>LIBMQTT_API <a class="code hl_struct" href="struct_m_q_t_t_client__name_value.html">MQTTClient_nameValue</a>* <a class="code hl_function" href="_m_q_t_t_client_8h.html#aef6eba956a5ff6072854a9e353487087">MQTTClient_getVersionInfo</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l00809" name="l00809"></a><span class="lineno">  809</span> </div>
<div class="foldopen" id="foldopen00830" data-start="{" data-end="};">
<div class="line"><a id="l00830" name="l00830"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html">  830</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l00831" name="l00831"></a><span class="lineno">  831</span>{</div>
<div class="line"><a id="l00833" name="l00833"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#aa5326df180cb23c59afbcab711a06479">  833</a></span>        <span class="keywordtype">char</span> struct_id[4];</div>
<div class="line"><a id="l00844" name="l00844"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#a0761a5e5be0383882e42924de8e51f82">  844</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a id="l00854" name="l00854"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#ac8dd0930672a9c7d71fc645aa1f0521d">  854</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#ac8dd0930672a9c7d71fc645aa1f0521d">keepAliveInterval</a>;</div>
<div class="line"><a id="l00876" name="l00876"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">  876</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">cleansession</a>;</div>
<div class="line"><a id="l00886" name="l00886"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#a9f1cdffc99659fd4e2d20e6de3c64df0">  886</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#a9f1cdffc99659fd4e2d20e6de3c64df0">reliable</a>;</div>
<div class="line"><a id="l00892" name="l00892"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#a0a880e99d47eb2efe552abe5079bdc9d">  892</a></span>        <a class="code hl_struct" href="struct_m_q_t_t_client__will_options.html">MQTTClient_willOptions</a>* <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#a0a880e99d47eb2efe552abe5079bdc9d">will</a>;</div>
<div class="line"><a id="l00898" name="l00898"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#aba2dfcdfda80edcb531a5a7115d3e043">  898</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#aba2dfcdfda80edcb531a5a7115d3e043">username</a>;</div>
<div class="line"><a id="l00904" name="l00904"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#aa4a2ebcb494493f648ae1e6975672575">  904</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#aa4a2ebcb494493f648ae1e6975672575">password</a>;</div>
<div class="line"><a id="l00908" name="l00908"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#a38c6aa24b36d981c49405db425c24db0">  908</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#a38c6aa24b36d981c49405db425c24db0">connectTimeout</a>;</div>
<div class="line"><a id="l00916" name="l00916"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#ac73f57846c42bcaa9a47e6721a957748">  916</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#ac73f57846c42bcaa9a47e6721a957748">retryInterval</a>;</div>
<div class="line"><a id="l00921" name="l00921"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#a8a0b0f0fc7c675312dc232e2458078c7">  921</a></span>        <a class="code hl_struct" href="struct_m_q_t_t_client___s_s_l_options.html">MQTTClient_SSLOptions</a>* <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#a8a0b0f0fc7c675312dc232e2458078c7">ssl</a>;</div>
<div class="line"><a id="l00925" name="l00925"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#aa82629005937abd92e97084a428cd61f">  925</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#aa82629005937abd92e97084a428cd61f">serverURIcount</a>;</div>
<div class="line"><a id="l00939" name="l00939"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#aba22d81c407fb2ba590dba476240d3e9">  939</a></span>        <span class="keywordtype">char</span>* <span class="keyword">const</span>* <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#aba22d81c407fb2ba590dba476240d3e9">serverURIs</a>;</div>
<div class="line"><a id="l00947" name="l00947"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#a12d546fd0ccf4e1091b18e1b735c7240">  947</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTVersion</a>;</div>
<div class="line"><a id="l00951" name="l00951"></a><span class="lineno">  951</span>        <span class="keyword">struct</span></div>
<div class="line"><a id="l00952" name="l00952"></a><span class="lineno">  952</span>        {</div>
<div class="line"><a id="l00953" name="l00953"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#a313446ca7679b36652722ffe53d05228">  953</a></span>                <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#a313446ca7679b36652722ffe53d05228">serverURI</a>;     </div>
<div class="line"><a id="l00954" name="l00954"></a><span class="lineno">  954</span>                <span class="keywordtype">int</span> MQTTVersion;     </div>
<div class="line"><a id="l00955" name="l00955"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#a44baf2cb9a0bbcec3ed2eace43f832d1">  955</a></span>                <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#a44baf2cb9a0bbcec3ed2eace43f832d1">sessionPresent</a>;  </div>
<div class="line"><a id="l00956" name="l00956"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#afbca347de18f7a8c57de1f16d3dadde6">  956</a></span>        } returned;</div>
<div class="line"><a id="l00960" name="l00960"></a><span class="lineno">  960</span>        <span class="keyword">struct</span></div>
<div class="line"><a id="l00961" name="l00961"></a><span class="lineno">  961</span>        {</div>
<div class="line"><a id="l00962" name="l00962"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#afed088663f8704004425cdae2120b9b3">  962</a></span>                <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#afed088663f8704004425cdae2120b9b3">len</a>;           </div>
<div class="line"><a id="l00963" name="l00963"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#a0d49d74db4c035719c3867723cf7e779">  963</a></span>                <span class="keyword">const</span> <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#a0d49d74db4c035719c3867723cf7e779">data</a>;  </div>
<div class="line"><a id="l00964" name="l00964"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#ae7280d284792990b5d8f6f29d4e0b113">  964</a></span>        } binarypwd;</div>
<div class="line"><a id="l00968" name="l00968"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#ae3f99bf4663ab7b9e9259feeba41fab2">  968</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#ae3f99bf4663ab7b9e9259feeba41fab2">maxInflightMessages</a>;</div>
<div class="line"><a id="l00969" name="l00969"></a><span class="lineno">  969</span>        <span class="comment">/*</span></div>
<div class="line"><a id="l00970" name="l00970"></a><span class="lineno">  970</span><span class="comment">         * MQTT V5 clean start flag.  Only clears state at the beginning of the session.</span></div>
<div class="line"><a id="l00971" name="l00971"></a><span class="lineno">  971</span><span class="comment">         */</span></div>
<div class="line"><a id="l00972" name="l00972"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#acdcb75a5d5981da027bce83849140f7b">  972</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#acdcb75a5d5981da027bce83849140f7b">cleanstart</a>;</div>
<div class="line"><a id="l00976" name="l00976"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#a018eec60631f40c01e6dcb727bffd33f">  976</a></span>        <span class="keyword">const</span> <a class="code hl_struct" href="struct_m_q_t_t_client__name_value.html">MQTTClient_nameValue</a>* <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#a018eec60631f40c01e6dcb727bffd33f">httpHeaders</a>;</div>
<div class="line"><a id="l00982" name="l00982"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#add124780ab2de397a96780576c2f112c">  982</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#add124780ab2de397a96780576c2f112c">httpProxy</a>;</div>
<div class="line"><a id="l00986" name="l00986"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_client__connect_options.html#a388b78d8a75658928238f700f207ad92">  986</a></span>        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_m_q_t_t_client__connect_options.html#a388b78d8a75658928238f700f207ad92">httpsProxy</a>;</div>
<div class="line"><a id="l00987" name="l00987"></a><span class="lineno">  987</span>} <a class="code hl_struct" href="struct_m_q_t_t_client__connect_options.html">MQTTClient_connectOptions</a>;</div>
</div>
<div class="line"><a id="l00988" name="l00988"></a><span class="lineno">  988</span> </div>
<div class="foldopen" id="foldopen00990" data-start="" data-end="">
<div class="line"><a id="l00990" name="l00990"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aefd7c865f2641c8155b763fdf3061c25">  990</a></span><span class="preprocessor">#define MQTTClient_connectOptions_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;C&#39;}, 8, 60, 1, 1, NULL, NULL, NULL, 30, 0, NULL,\</span></div>
<div class="line"><a id="l00991" name="l00991"></a><span class="lineno">  991</span><span class="preprocessor">0, NULL, MQTTVERSION_DEFAULT, {NULL, 0, 0}, {0, NULL}, -1, 0, NULL, NULL, NULL}</span></div>
</div>
<div class="line"><a id="l00992" name="l00992"></a><span class="lineno">  992</span> </div>
<div class="foldopen" id="foldopen00994" data-start="" data-end="">
<div class="line"><a id="l00994" name="l00994"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a1f0c7608262ac9c00cb94e9c8f9fc984">  994</a></span><span class="preprocessor">#define MQTTClient_connectOptions_initializer5 { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;C&#39;}, 8, 60, 0, 1, NULL, NULL, NULL, 30, 0, NULL,\</span></div>
<div class="line"><a id="l00995" name="l00995"></a><span class="lineno">  995</span><span class="preprocessor">0, NULL, MQTTVERSION_5, {NULL, 0, 0}, {0, NULL}, -1, 1, NULL, NULL, NULL}</span></div>
</div>
<div class="line"><a id="l00996" name="l00996"></a><span class="lineno">  996</span> </div>
<div class="foldopen" id="foldopen01000" data-start="" data-end="">
<div class="line"><a id="l01000" name="l01000"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a0a98fda162a78ee8c8cbd7d9d39494f4"> 1000</a></span><span class="preprocessor">#define MQTTClient_connectOptions_initializer_ws { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;C&#39;}, 8, 45, 1, 1, NULL, NULL, NULL, 30, 0, NULL,\</span></div>
<div class="line"><a id="l01001" name="l01001"></a><span class="lineno"> 1001</span><span class="preprocessor">0, NULL, MQTTVERSION_DEFAULT, {NULL, 0, 0}, {0, NULL}, -1, 0, NULL, NULL, NULL}</span></div>
</div>
<div class="line"><a id="l01002" name="l01002"></a><span class="lineno"> 1002</span> </div>
<div class="foldopen" id="foldopen01006" data-start="" data-end="">
<div class="line"><a id="l01006" name="l01006"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a8c37c9f77f0b67e2520c8f91acf1afea"> 1006</a></span><span class="preprocessor">#define MQTTClient_connectOptions_initializer5_ws { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;C&#39;}, 8, 45, 0, 1, NULL, NULL, NULL, 30, 0, NULL,\</span></div>
<div class="line"><a id="l01007" name="l01007"></a><span class="lineno"> 1007</span><span class="preprocessor">0, NULL, MQTTVERSION_5, {NULL, 0, 0}, {0, NULL}, -1, 1, NULL, NULL, NULL}</span></div>
</div>
<div class="line"><a id="l01008" name="l01008"></a><span class="lineno"> 1008</span> </div>
<div class="line"><a id="l01029" name="l01029"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aaa8ae61cd65c9dc0846df10122d7bd4e"> 1029</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#aaa8ae61cd65c9dc0846df10122d7bd4e">MQTTClient_connect</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <a class="code hl_struct" href="struct_m_q_t_t_client__connect_options.html">MQTTClient_connectOptions</a>* options);</div>
<div class="line"><a id="l01030" name="l01030"></a><span class="lineno"> 1030</span> </div>
<div class="foldopen" id="foldopen01032" data-start="{" data-end="};">
<div class="line"><a id="l01032" name="l01032"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_response.html"> 1032</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="struct_m_q_t_t_response.html">MQTTResponse</a></div>
<div class="line"><a id="l01033" name="l01033"></a><span class="lineno"> 1033</span>{</div>
<div class="line"><a id="l01034" name="l01034"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_response.html#aad880fc4455c253781e8968f2239d56f"> 1034</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_response.html#aad880fc4455c253781e8968f2239d56f">version</a>;                        <span class="comment">/* the version number of this structure */</span></div>
<div class="line"><a id="l01035" name="l01035"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_response.html#a580d8a8ecb285f5a86c2a3865438f8ee"> 1035</a></span>        <span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> <a class="code hl_variable" href="struct_m_q_t_t_response.html#a580d8a8ecb285f5a86c2a3865438f8ee">reasonCode</a>;    <span class="comment">/* the MQTT 5.0 reason code returned */</span></div>
<div class="line"><a id="l01036" name="l01036"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_response.html#ac97316626bd4faa6b71277c221275f4b"> 1036</a></span>        <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_m_q_t_t_response.html#ac97316626bd4faa6b71277c221275f4b">reasonCodeCount</a>;                <span class="comment">/* the number of reason codes.  Used for subscribeMany5 and unsubscribeMany5 */</span></div>
<div class="line"><a id="l01037" name="l01037"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_response.html#a2199c9d905dbfa279895cf8123c10f4f"> 1037</a></span>        <span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a>* <a class="code hl_variable" href="struct_m_q_t_t_response.html#a2199c9d905dbfa279895cf8123c10f4f">reasonCodes</a>;  <span class="comment">/* a list of reason codes.  Used for subscribeMany5 and unsubscribeMany5 */</span></div>
<div class="line"><a id="l01038" name="l01038"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_response.html#a72e9294467b8329a78bc840fe6c5b230"> 1038</a></span>        <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* <a class="code hl_variable" href="struct_m_q_t_t_response.html#a72e9294467b8329a78bc840fe6c5b230">properties</a>;         <span class="comment">/* optionally, the MQTT 5.0 properties returned */</span></div>
<div class="line"><a id="l01039" name="l01039"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a0d31d490adbe677902b99eca127bee56"> 1039</a></span>} <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a0d31d490adbe677902b99eca127bee56">MQTTResponse</a>;</div>
</div>
<div class="line"><a id="l01040" name="l01040"></a><span class="lineno"> 1040</span> </div>
<div class="line"><a id="l01041" name="l01041"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a17f171200136bcfa933eb50ef21531a7"> 1041</a></span><span class="preprocessor">#define MQTTResponse_initializer {1, MQTTREASONCODE_SUCCESS, 0, NULL, NULL}</span></div>
<div class="line"><a id="l01042" name="l01042"></a><span class="lineno"> 1042</span> </div>
<div class="line"><a id="l01047" name="l01047"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a01bd2c5f98ec5c0636a106db33f2b01b"> 1047</a></span>LIBMQTT_API <span class="keywordtype">void</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a01bd2c5f98ec5c0636a106db33f2b01b">MQTTResponse_free</a>(<a class="code hl_struct" href="struct_m_q_t_t_response.html">MQTTResponse</a> response);</div>
<div class="line"><a id="l01048" name="l01048"></a><span class="lineno"> 1048</span> </div>
<div class="line"><a id="l01062" name="l01062"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aa777f80cb3eec5610f976aff30b8c0d6"> 1062</a></span>LIBMQTT_API <a class="code hl_struct" href="struct_m_q_t_t_response.html">MQTTResponse</a> <a class="code hl_function" href="_m_q_t_t_client_8h.html#aa777f80cb3eec5610f976aff30b8c0d6">MQTTClient_connect5</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <a class="code hl_struct" href="struct_m_q_t_t_client__connect_options.html">MQTTClient_connectOptions</a>* options,</div>
<div class="line"><a id="l01063" name="l01063"></a><span class="lineno"> 1063</span>                <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* connectProperties, <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* willProperties);</div>
<div class="line"><a id="l01064" name="l01064"></a><span class="lineno"> 1064</span> </div>
<div class="line"><a id="l01083" name="l01083"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a1e4d90c13a3c0705bc4a13bfe64e6525"> 1083</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a1e4d90c13a3c0705bc4a13bfe64e6525">MQTTClient_disconnect</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keywordtype">int</span> timeout);</div>
<div class="line"><a id="l01084" name="l01084"></a><span class="lineno"> 1084</span> </div>
<div class="line"><a id="l01085" name="l01085"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a1762c469715b7f718c4e63a427e6c13c"> 1085</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a1762c469715b7f718c4e63a427e6c13c">MQTTClient_disconnect5</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keywordtype">int</span> timeout, <span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reason, <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* props);</div>
<div class="line"><a id="l01086" name="l01086"></a><span class="lineno"> 1086</span> </div>
<div class="line"><a id="l01094" name="l01094"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a6e8231e8c47f6f67f7ebbb5dcb4c69c0"> 1094</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a6e8231e8c47f6f67f7ebbb5dcb4c69c0">MQTTClient_isConnected</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle);</div>
<div class="line"><a id="l01095" name="l01095"></a><span class="lineno"> 1095</span> </div>
<div class="line"><a id="l01096" name="l01096"></a><span class="lineno"> 1096</span> </div>
<div class="line"><a id="l01097" name="l01097"></a><span class="lineno"> 1097</span><span class="comment">/* Subscribe is synchronous.  QoS list parameter is changed on return to granted QoSs.</span></div>
<div class="line"><a id="l01098" name="l01098"></a><span class="lineno"> 1098</span><span class="comment">   Returns return code, MQTTCLIENT_SUCCESS == success, non-zero some sort of error (TBD) */</span></div>
<div class="line"><a id="l01099" name="l01099"></a><span class="lineno"> 1099</span> </div>
<div class="line"><a id="l01113" name="l01113"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a9c1c28258f0d5c6a44ff53a98618f5f3"> 1113</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a9c1c28258f0d5c6a44ff53a98618f5f3">MQTTClient_subscribe</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* topic, <span class="keywordtype">int</span> qos);</div>
<div class="line"><a id="l01114" name="l01114"></a><span class="lineno"> 1114</span> </div>
<div class="line"><a id="l01128" name="l01128"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#af35ab7375435f7b6388c5ff4610dad3d"> 1128</a></span>LIBMQTT_API <a class="code hl_struct" href="struct_m_q_t_t_response.html">MQTTResponse</a> <a class="code hl_function" href="_m_q_t_t_client_8h.html#af35ab7375435f7b6388c5ff4610dad3d">MQTTClient_subscribe5</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* topic, <span class="keywordtype">int</span> qos,</div>
<div class="line"><a id="l01129" name="l01129"></a><span class="lineno"> 1129</span>                <a class="code hl_struct" href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a>* opts, <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* props);</div>
<div class="line"><a id="l01130" name="l01130"></a><span class="lineno"> 1130</span> </div>
<div class="line"><a id="l01147" name="l01147"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a92fa1c13f3db8399e042fbdbdfb692b3"> 1147</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a92fa1c13f3db8399e042fbdbdfb692b3">MQTTClient_subscribeMany</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keywordtype">int</span> count, <span class="keywordtype">char</span>* <span class="keyword">const</span>* topic, <span class="keywordtype">int</span>* qos);</div>
<div class="line"><a id="l01148" name="l01148"></a><span class="lineno"> 1148</span> </div>
<div class="line"><a id="l01165" name="l01165"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a5390c2402f135c12826ffbf6fc261f7c"> 1165</a></span>LIBMQTT_API <a class="code hl_struct" href="struct_m_q_t_t_response.html">MQTTResponse</a> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a5390c2402f135c12826ffbf6fc261f7c">MQTTClient_subscribeMany5</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keywordtype">int</span> count, <span class="keywordtype">char</span>* <span class="keyword">const</span>* topic,</div>
<div class="line"><a id="l01166" name="l01166"></a><span class="lineno"> 1166</span>                <span class="keywordtype">int</span>* qos, <a class="code hl_struct" href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a>* opts, <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* props);</div>
<div class="line"><a id="l01167" name="l01167"></a><span class="lineno"> 1167</span> </div>
<div class="line"><a id="l01179" name="l01179"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aa8731be3dbc6a25f41f037f8bbbb054b"> 1179</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#aa8731be3dbc6a25f41f037f8bbbb054b">MQTTClient_unsubscribe</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* topic);</div>
<div class="line"><a id="l01180" name="l01180"></a><span class="lineno"> 1180</span> </div>
<div class="line"><a id="l01191" name="l01191"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a58356c13867f18df60fd4c7ec9457c48"> 1191</a></span>LIBMQTT_API <a class="code hl_struct" href="struct_m_q_t_t_response.html">MQTTResponse</a> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a58356c13867f18df60fd4c7ec9457c48">MQTTClient_unsubscribe5</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* topic, <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* props);</div>
<div class="line"><a id="l01192" name="l01192"></a><span class="lineno"> 1192</span> </div>
<div class="line"><a id="l01204" name="l01204"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a50abbce720d50b9f84b97ff9fa1f546d"> 1204</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a50abbce720d50b9f84b97ff9fa1f546d">MQTTClient_unsubscribeMany</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keywordtype">int</span> count, <span class="keywordtype">char</span>* <span class="keyword">const</span>* topic);</div>
<div class="line"><a id="l01205" name="l01205"></a><span class="lineno"> 1205</span> </div>
<div class="line"><a id="l01217" name="l01217"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a46bdb532d2153110ccffb2f0748d1ba5"> 1217</a></span>LIBMQTT_API <a class="code hl_struct" href="struct_m_q_t_t_response.html">MQTTResponse</a> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a46bdb532d2153110ccffb2f0748d1ba5">MQTTClient_unsubscribeMany5</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keywordtype">int</span> count, <span class="keywordtype">char</span>* <span class="keyword">const</span>* topic, <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* props);</div>
<div class="line"><a id="l01218" name="l01218"></a><span class="lineno"> 1218</span> </div>
<div class="line"><a id="l01240" name="l01240"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#afe9c34013c3511b8ef6cd36bf703678d"> 1240</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#afe9c34013c3511b8ef6cd36bf703678d">MQTTClient_publish</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* topicName, <span class="keywordtype">int</span> payloadlen, <span class="keyword">const</span> <span class="keywordtype">void</span>* payload, <span class="keywordtype">int</span> qos, <span class="keywordtype">int</span> retained,</div>
<div class="line"><a id="l01241" name="l01241"></a><span class="lineno"> 1241</span>                <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a>* dt);</div>
<div class="line"><a id="l01242" name="l01242"></a><span class="lineno"> 1242</span> </div>
<div class="line"><a id="l01264" name="l01264"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a8148186cc7683a6bb57f621653df51df"> 1264</a></span>LIBMQTT_API <a class="code hl_struct" href="struct_m_q_t_t_response.html">MQTTResponse</a> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a8148186cc7683a6bb57f621653df51df">MQTTClient_publish5</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* topicName, <span class="keywordtype">int</span> payloadlen, <span class="keyword">const</span> <span class="keywordtype">void</span>* payload,</div>
<div class="line"><a id="l01265" name="l01265"></a><span class="lineno"> 1265</span>                <span class="keywordtype">int</span> qos, <span class="keywordtype">int</span> retained, <a class="code hl_struct" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* properties, <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a>* dt);</div>
<div class="line"><a id="l01285" name="l01285"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#ace320b8a92c7087d9dd5cf242d50389d"> 1285</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#ace320b8a92c7087d9dd5cf242d50389d">MQTTClient_publishMessage</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* topicName, <a class="code hl_struct" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a>* msg, <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a>* dt);</div>
<div class="line"><a id="l01286" name="l01286"></a><span class="lineno"> 1286</span> </div>
<div class="line"><a id="l01287" name="l01287"></a><span class="lineno"> 1287</span> </div>
<div class="line"><a id="l01307" name="l01307"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a362042ce973c012bad6a1aa3b5984f5d"> 1307</a></span>LIBMQTT_API <a class="code hl_struct" href="struct_m_q_t_t_response.html">MQTTResponse</a> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a362042ce973c012bad6a1aa3b5984f5d">MQTTClient_publishMessage5</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* topicName, <a class="code hl_struct" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a>* msg,</div>
<div class="line"><a id="l01308" name="l01308"></a><span class="lineno"> 1308</span>                <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a>* dt);</div>
<div class="line"><a id="l01309" name="l01309"></a><span class="lineno"> 1309</span> </div>
<div class="line"><a id="l01325" name="l01325"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a83807ec81fe8c3941e368ab329d43067"> 1325</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a83807ec81fe8c3941e368ab329d43067">MQTTClient_waitForCompletion</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> dt, <span class="keywordtype">unsigned</span> <span class="keywordtype">long</span> timeout);</div>
<div class="line"><a id="l01326" name="l01326"></a><span class="lineno"> 1326</span> </div>
<div class="line"><a id="l01327" name="l01327"></a><span class="lineno"> 1327</span> </div>
<div class="line"><a id="l01346" name="l01346"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a2a617c6b0492c04a4ddea592f5e53604"> 1346</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a2a617c6b0492c04a4ddea592f5e53604">MQTTClient_getPendingDeliveryTokens</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a> **tokens);</div>
<div class="line"><a id="l01347" name="l01347"></a><span class="lineno"> 1347</span> </div>
<div class="line"><a id="l01354" name="l01354"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a8ad3d29864a9ca08202b0832e0f6678e"> 1354</a></span>LIBMQTT_API <span class="keywordtype">void</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a8ad3d29864a9ca08202b0832e0f6678e">MQTTClient_yield</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l01355" name="l01355"></a><span class="lineno"> 1355</span> </div>
<div class="line"><a id="l01387" name="l01387"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a4c2df88d00a3dadd510a8cb774739366"> 1387</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a4c2df88d00a3dadd510a8cb774739366">MQTTClient_receive</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keywordtype">char</span>** topicName, <span class="keywordtype">int</span>* topicLen, <a class="code hl_struct" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a>** message,</div>
<div class="line"><a id="l01388" name="l01388"></a><span class="lineno"> 1388</span>                <span class="keywordtype">unsigned</span> <span class="keywordtype">long</span> timeout);</div>
<div class="line"><a id="l01389" name="l01389"></a><span class="lineno"> 1389</span> </div>
<div class="line"><a id="l01400" name="l01400"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#abd8abde4f39d3e689029de27f7a98a65"> 1400</a></span>LIBMQTT_API <span class="keywordtype">void</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#abd8abde4f39d3e689029de27f7a98a65">MQTTClient_freeMessage</a>(<a class="code hl_struct" href="struct_m_q_t_t_client__message.html">MQTTClient_message</a>** msg);</div>
<div class="line"><a id="l01401" name="l01401"></a><span class="lineno"> 1401</span> </div>
<div class="line"><a id="l01410" name="l01410"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a203b545c999beb6b825ec99b6aea79ab"> 1410</a></span>LIBMQTT_API <span class="keywordtype">void</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a203b545c999beb6b825ec99b6aea79ab">MQTTClient_free</a>(<span class="keywordtype">void</span>* ptr);</div>
<div class="line"><a id="l01411" name="l01411"></a><span class="lineno"> 1411</span> </div>
<div class="line"><a id="l01418" name="l01418"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a1f3ae01af021b014df147c9996156a69"> 1418</a></span>LIBMQTT_API <span class="keywordtype">void</span>* <a class="code hl_function" href="_m_q_t_t_client_8h.html#a1f3ae01af021b014df147c9996156a69">MQTTClient_malloc</a>(<span class="keywordtype">size_t</span> size);</div>
<div class="line"><a id="l01419" name="l01419"></a><span class="lineno"> 1419</span> </div>
<div class="line"><a id="l01427" name="l01427"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#ae700c3f5cfea3813264ce95e7c8cf498"> 1427</a></span>LIBMQTT_API <span class="keywordtype">void</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#ae700c3f5cfea3813264ce95e7c8cf498">MQTTClient_destroy</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a>* handle);</div>
<div class="line"><a id="l01428" name="l01428"></a><span class="lineno"> 1428</span> </div>
<div class="line"><a id="l01429" name="l01429"></a><span class="lineno"> 1429</span> </div>
<div class="foldopen" id="foldopen01430" data-start="{" data-end="};">
<div class="line"><a id="l01430" name="l01430"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09b"> 1430</a></span><span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09b">MQTTCLIENT_TRACE_LEVELS</a></div>
<div class="line"><a id="l01431" name="l01431"></a><span class="lineno"> 1431</span>{</div>
<div class="line"><a id="l01432" name="l01432"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba38a4c3c4e2fc99711793ee2815aee40c"> 1432</a></span>        <a class="code hl_enumvalue" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba38a4c3c4e2fc99711793ee2815aee40c">MQTTCLIENT_TRACE_MAXIMUM</a> = 1,</div>
<div class="line"><a id="l01433" name="l01433"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba4bb7e7221b59e9be4515f2182c03ea99"> 1433</a></span>        <a class="code hl_enumvalue" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba4bb7e7221b59e9be4515f2182c03ea99">MQTTCLIENT_TRACE_MEDIUM</a>,</div>
<div class="line"><a id="l01434" name="l01434"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09bacf029d9a231bd07e5e1a6f3bd0b6004e"> 1434</a></span>        <a class="code hl_enumvalue" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09bacf029d9a231bd07e5e1a6f3bd0b6004e">MQTTCLIENT_TRACE_MINIMUM</a>,</div>
<div class="line"><a id="l01435" name="l01435"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba29f21f77cf34ab2467520d7738fd8eb1"> 1435</a></span>        <a class="code hl_enumvalue" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba29f21f77cf34ab2467520d7738fd8eb1">MQTTCLIENT_TRACE_PROTOCOL</a>,</div>
<div class="line"><a id="l01436" name="l01436"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba6eefffc98c1ba698224ba351f12e6a91"> 1436</a></span>        <a class="code hl_enumvalue" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba6eefffc98c1ba698224ba351f12e6a91">MQTTCLIENT_TRACE_ERROR</a>,</div>
<div class="line"><a id="l01437" name="l01437"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09baf060569bdbb4015cfce028937b4cfa69"> 1437</a></span>        <a class="code hl_enumvalue" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09baf060569bdbb4015cfce028937b4cfa69">MQTTCLIENT_TRACE_SEVERE</a>,</div>
<div class="line"><a id="l01438" name="l01438"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba35626cc4876d074c4c21f8c4f54fdf38"> 1438</a></span>        <a class="code hl_enumvalue" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba35626cc4876d074c4c21f8c4f54fdf38">MQTTCLIENT_TRACE_FATAL</a>,</div>
<div class="line"><a id="l01439" name="l01439"></a><span class="lineno"> 1439</span>};</div>
</div>
<div class="line"><a id="l01440" name="l01440"></a><span class="lineno"> 1440</span> </div>
<div class="line"><a id="l01441" name="l01441"></a><span class="lineno"> 1441</span> </div>
<div class="line"><a id="l01447" name="l01447"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a4dfa35d29db54b10b15b8ac2d9a778be"> 1447</a></span>LIBMQTT_API <span class="keywordtype">void</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a4dfa35d29db54b10b15b8ac2d9a778be">MQTTClient_setTraceLevel</a>(<span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09b">MQTTCLIENT_TRACE_LEVELS</a> level);</div>
<div class="line"><a id="l01448" name="l01448"></a><span class="lineno"> 1448</span> </div>
<div class="line"><a id="l01449" name="l01449"></a><span class="lineno"> 1449</span> </div>
<div class="line"><a id="l01459" name="l01459"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#afa5758290a1162e5135bca97bbfd5774"> 1459</a></span><span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code hl_typedef" href="_m_q_t_t_client_8h.html#afa5758290a1162e5135bca97bbfd5774">MQTTClient_traceCallback</a>(<span class="keyword">enum</span> <a class="code hl_enumeration" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09b">MQTTCLIENT_TRACE_LEVELS</a> level, <span class="keywordtype">char</span>* message);</div>
<div class="line"><a id="l01460" name="l01460"></a><span class="lineno"> 1460</span> </div>
<div class="line"><a id="l01467" name="l01467"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a22870f94aa4cb1827626612f1ded7c69"> 1467</a></span>LIBMQTT_API <span class="keywordtype">void</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a22870f94aa4cb1827626612f1ded7c69">MQTTClient_setTraceCallback</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#afa5758290a1162e5135bca97bbfd5774">MQTTClient_traceCallback</a>* callback);</div>
<div class="line"><a id="l01468" name="l01468"></a><span class="lineno"> 1468</span> </div>
<div class="line"><a id="l01476" name="l01476"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a96067a2fb74d2a61c7e93015629548e0"> 1476</a></span>LIBMQTT_API <span class="keywordtype">int</span> <a class="code hl_function" href="_m_q_t_t_client_8h.html#a96067a2fb74d2a61c7e93015629548e0">MQTTClient_setCommandTimeout</a>(<a class="code hl_typedef" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a> handle, <span class="keywordtype">unsigned</span> <span class="keywordtype">long</span> milliSeconds);</div>
<div class="line"><a id="l01477" name="l01477"></a><span class="lineno"> 1477</span> </div>
<div class="line"><a id="l01483" name="l01483"></a><span class="lineno"><a class="line" href="_m_q_t_t_client_8h.html#a68535b4c6d8f28b29a52569926cdeb50"> 1483</a></span>LIBMQTT_API <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_function" href="_m_q_t_t_client_8h.html#a68535b4c6d8f28b29a52569926cdeb50">MQTTClient_strerror</a>(<span class="keywordtype">int</span> code);</div>
<div class="line"><a id="l01484" name="l01484"></a><span class="lineno"> 1484</span> </div>
<div class="line"><a id="l01485" name="l01485"></a><span class="lineno"> 1485</span><span class="preprocessor">#if defined(__cplusplus)</span></div>
<div class="line"><a id="l01486" name="l01486"></a><span class="lineno"> 1486</span>     }</div>
<div class="line"><a id="l01487" name="l01487"></a><span class="lineno"> 1487</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01488" name="l01488"></a><span class="lineno"> 1488</span> </div>
<div class="line"><a id="l01489" name="l01489"></a><span class="lineno"> 1489</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01490" name="l01490"></a><span class="lineno"> 1490</span> </div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a01bd2c5f98ec5c0636a106db33f2b01b"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a01bd2c5f98ec5c0636a106db33f2b01b">MQTTResponse_free</a></div><div class="ttdeci">void MQTTResponse_free(MQTTResponse response)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a0d31d490adbe677902b99eca127bee56"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a0d31d490adbe677902b99eca127bee56">MQTTResponse</a></div><div class="ttdeci">struct MQTTResponse MQTTResponse</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a1762c469715b7f718c4e63a427e6c13c"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a1762c469715b7f718c4e63a427e6c13c">MQTTClient_disconnect5</a></div><div class="ttdeci">int MQTTClient_disconnect5(MQTTClient handle, int timeout, enum MQTTReasonCodes reason, MQTTProperties *props)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a1e4d90c13a3c0705bc4a13bfe64e6525"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a1e4d90c13a3c0705bc4a13bfe64e6525">MQTTClient_disconnect</a></div><div class="ttdeci">int MQTTClient_disconnect(MQTTClient handle, int timeout)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a1f3ae01af021b014df147c9996156a69"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a1f3ae01af021b014df147c9996156a69">MQTTClient_malloc</a></div><div class="ttdeci">void * MQTTClient_malloc(size_t size)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a203b545c999beb6b825ec99b6aea79ab"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a203b545c999beb6b825ec99b6aea79ab">MQTTClient_free</a></div><div class="ttdeci">void MQTTClient_free(void *ptr)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a21804ede1a506d1d69a472bc30acc8ba"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a21804ede1a506d1d69a472bc30acc8ba">MQTTClient_global_init</a></div><div class="ttdeci">void MQTTClient_global_init(MQTTClient_init_options *inits)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a22870f94aa4cb1827626612f1ded7c69"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a22870f94aa4cb1827626612f1ded7c69">MQTTClient_setTraceCallback</a></div><div class="ttdeci">void MQTTClient_setTraceCallback(MQTTClient_traceCallback *callback)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a2a617c6b0492c04a4ddea592f5e53604"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a2a617c6b0492c04a4ddea592f5e53604">MQTTClient_getPendingDeliveryTokens</a></div><div class="ttdeci">int MQTTClient_getPendingDeliveryTokens(MQTTClient handle, MQTTClient_deliveryToken **tokens)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a362042ce973c012bad6a1aa3b5984f5d"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a362042ce973c012bad6a1aa3b5984f5d">MQTTClient_publishMessage5</a></div><div class="ttdeci">MQTTResponse MQTTClient_publishMessage5(MQTTClient handle, const char *topicName, MQTTClient_message *msg, MQTTClient_deliveryToken *dt)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a41108d4cccb67a9d6884ebae52211c46"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a41108d4cccb67a9d6884ebae52211c46">MQTTClient_disconnected</a></div><div class="ttdeci">void MQTTClient_disconnected(void *context, MQTTProperties *properties, enum MQTTReasonCodes reasonCode)</div><div class="ttdef"><b>Definition</b> MQTTClient.h:452</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a46bdb532d2153110ccffb2f0748d1ba5"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a46bdb532d2153110ccffb2f0748d1ba5">MQTTClient_unsubscribeMany5</a></div><div class="ttdeci">MQTTResponse MQTTClient_unsubscribeMany5(MQTTClient handle, int count, char *const *topic, MQTTProperties *props)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a4c2df88d00a3dadd510a8cb774739366"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a4c2df88d00a3dadd510a8cb774739366">MQTTClient_receive</a></div><div class="ttdeci">int MQTTClient_receive(MQTTClient handle, char **topicName, int *topicLen, MQTTClient_message **message, unsigned long timeout)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a4dfa35d29db54b10b15b8ac2d9a778be"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a4dfa35d29db54b10b15b8ac2d9a778be">MQTTClient_setTraceLevel</a></div><div class="ttdeci">void MQTTClient_setTraceLevel(enum MQTTCLIENT_TRACE_LEVELS level)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a50abbce720d50b9f84b97ff9fa1f546d"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a50abbce720d50b9f84b97ff9fa1f546d">MQTTClient_unsubscribeMany</a></div><div class="ttdeci">int MQTTClient_unsubscribeMany(MQTTClient handle, int count, char *const *topic)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a5390c2402f135c12826ffbf6fc261f7c"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a5390c2402f135c12826ffbf6fc261f7c">MQTTClient_subscribeMany5</a></div><div class="ttdeci">MQTTResponse MQTTClient_subscribeMany5(MQTTClient handle, int count, char *const *topic, int *qos, MQTTSubscribe_options *opts, MQTTProperties *props)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a58356c13867f18df60fd4c7ec9457c48"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a58356c13867f18df60fd4c7ec9457c48">MQTTClient_unsubscribe5</a></div><div class="ttdeci">MQTTResponse MQTTClient_unsubscribe5(MQTTClient handle, const char *topic, MQTTProperties *props)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a68535b4c6d8f28b29a52569926cdeb50"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a68535b4c6d8f28b29a52569926cdeb50">MQTTClient_strerror</a></div><div class="ttdeci">const char * MQTTClient_strerror(int code)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a6bb253f16754e7cc81798c9fda0e36cf"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a6bb253f16754e7cc81798c9fda0e36cf">MQTTClient_connectionLost</a></div><div class="ttdeci">void MQTTClient_connectionLost(void *context, char *cause)</div><div class="ttdef"><b>Definition</b> MQTTClient.h:408</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a6c3f51e50e2c47328eee1b0c920ed103"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a6c3f51e50e2c47328eee1b0c920ed103">MQTTClient_published</a></div><div class="ttdeci">void MQTTClient_published(void *context, int dt, int packet_type, MQTTProperties *properties, enum MQTTReasonCodes reasonCode)</div><div class="ttdef"><b>Definition</b> MQTTClient.h:493</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a6e8231e8c47f6f67f7ebbb5dcb4c69c0"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a6e8231e8c47f6f67f7ebbb5dcb4c69c0">MQTTClient_isConnected</a></div><div class="ttdeci">int MQTTClient_isConnected(MQTTClient handle)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a73e49030fd8b7074aa1aa45669b7fe8d"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient_deliveryToken</a></div><div class="ttdeci">int MQTTClient_deliveryToken</div><div class="ttdef"><b>Definition</b> MQTTClient.h:267</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a7649e3913f9a216424d296f88a969c59"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient</a></div><div class="ttdeci">void * MQTTClient</div><div class="ttdef"><b>Definition</b> MQTTClient.h:256</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a8148186cc7683a6bb57f621653df51df"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a8148186cc7683a6bb57f621653df51df">MQTTClient_publish5</a></div><div class="ttdeci">MQTTResponse MQTTClient_publish5(MQTTClient handle, const char *topicName, int payloadlen, const void *payload, int qos, int retained, MQTTProperties *properties, MQTTClient_deliveryToken *dt)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a83807ec81fe8c3941e368ab329d43067"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a83807ec81fe8c3941e368ab329d43067">MQTTClient_waitForCompletion</a></div><div class="ttdeci">int MQTTClient_waitForCompletion(MQTTClient handle, MQTTClient_deliveryToken dt, unsigned long timeout)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a8ad3d29864a9ca08202b0832e0f6678e"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a8ad3d29864a9ca08202b0832e0f6678e">MQTTClient_yield</a></div><div class="ttdeci">void MQTTClient_yield(void)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a8adea083a162735d5c7592160088eea0"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a8adea083a162735d5c7592160088eea0">MQTTClient_setDisconnected</a></div><div class="ttdeci">int MQTTClient_setDisconnected(MQTTClient handle, void *context, MQTTClient_disconnected *co)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a8b2beb5227708f8127b666f5a7fc41b3"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a8b2beb5227708f8127b666f5a7fc41b3">MQTTClient_token</a></div><div class="ttdeci">int MQTTClient_token</div><div class="ttdef"><b>Definition</b> MQTTClient.h:268</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a92fa1c13f3db8399e042fbdbdfb692b3"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a92fa1c13f3db8399e042fbdbdfb692b3">MQTTClient_subscribeMany</a></div><div class="ttdeci">int MQTTClient_subscribeMany(MQTTClient handle, int count, char *const *topic, int *qos)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a96067a2fb74d2a61c7e93015629548e0"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a96067a2fb74d2a61c7e93015629548e0">MQTTClient_setCommandTimeout</a></div><div class="ttdeci">int MQTTClient_setCommandTimeout(MQTTClient handle, unsigned long milliSeconds)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a9a0518d9ca924d12c1329dbe3de5f2b6"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create</a></div><div class="ttdeci">int MQTTClient_create(MQTTClient *handle, const char *serverURI, const char *clientId, int persistence_type, void *persistence_context)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a9c1c28258f0d5c6a44ff53a98618f5f3"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a9c1c28258f0d5c6a44ff53a98618f5f3">MQTTClient_subscribe</a></div><div class="ttdeci">int MQTTClient_subscribe(MQTTClient handle, const char *topic, int qos)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_a9f13911351a3de6b1ebdabd4cb4116ba"><div class="ttname"><a href="_m_q_t_t_client_8h.html#a9f13911351a3de6b1ebdabd4cb4116ba">MQTTClient_setPublished</a></div><div class="ttdeci">int MQTTClient_setPublished(MQTTClient handle, void *context, MQTTClient_published *co)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aa0ae95caa9c16d152b5036b1bac2e09b"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09b">MQTTCLIENT_TRACE_LEVELS</a></div><div class="ttdeci">MQTTCLIENT_TRACE_LEVELS</div><div class="ttdef"><b>Definition</b> MQTTClient.h:1431</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aa0ae95caa9c16d152b5036b1bac2e09ba29f21f77cf34ab2467520d7738fd8eb1"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba29f21f77cf34ab2467520d7738fd8eb1">MQTTCLIENT_TRACE_PROTOCOL</a></div><div class="ttdeci">@ MQTTCLIENT_TRACE_PROTOCOL</div><div class="ttdef"><b>Definition</b> MQTTClient.h:1435</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aa0ae95caa9c16d152b5036b1bac2e09ba35626cc4876d074c4c21f8c4f54fdf38"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba35626cc4876d074c4c21f8c4f54fdf38">MQTTCLIENT_TRACE_FATAL</a></div><div class="ttdeci">@ MQTTCLIENT_TRACE_FATAL</div><div class="ttdef"><b>Definition</b> MQTTClient.h:1438</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aa0ae95caa9c16d152b5036b1bac2e09ba38a4c3c4e2fc99711793ee2815aee40c"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba38a4c3c4e2fc99711793ee2815aee40c">MQTTCLIENT_TRACE_MAXIMUM</a></div><div class="ttdeci">@ MQTTCLIENT_TRACE_MAXIMUM</div><div class="ttdef"><b>Definition</b> MQTTClient.h:1432</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aa0ae95caa9c16d152b5036b1bac2e09ba4bb7e7221b59e9be4515f2182c03ea99"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba4bb7e7221b59e9be4515f2182c03ea99">MQTTCLIENT_TRACE_MEDIUM</a></div><div class="ttdeci">@ MQTTCLIENT_TRACE_MEDIUM</div><div class="ttdef"><b>Definition</b> MQTTClient.h:1433</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aa0ae95caa9c16d152b5036b1bac2e09ba6eefffc98c1ba698224ba351f12e6a91"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba6eefffc98c1ba698224ba351f12e6a91">MQTTCLIENT_TRACE_ERROR</a></div><div class="ttdeci">@ MQTTCLIENT_TRACE_ERROR</div><div class="ttdef"><b>Definition</b> MQTTClient.h:1436</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aa0ae95caa9c16d152b5036b1bac2e09bacf029d9a231bd07e5e1a6f3bd0b6004e"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09bacf029d9a231bd07e5e1a6f3bd0b6004e">MQTTCLIENT_TRACE_MINIMUM</a></div><div class="ttdeci">@ MQTTCLIENT_TRACE_MINIMUM</div><div class="ttdef"><b>Definition</b> MQTTClient.h:1434</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aa0ae95caa9c16d152b5036b1bac2e09baf060569bdbb4015cfce028937b4cfa69"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09baf060569bdbb4015cfce028937b4cfa69">MQTTCLIENT_TRACE_SEVERE</a></div><div class="ttdeci">@ MQTTCLIENT_TRACE_SEVERE</div><div class="ttdef"><b>Definition</b> MQTTClient.h:1437</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aa42130dd069e7e949bcab37b6dce64a5"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aa42130dd069e7e949bcab37b6dce64a5">MQTTClient_messageArrived</a></div><div class="ttdeci">int MQTTClient_messageArrived(void *context, char *topicName, int topicLen, MQTTClient_message *message)</div><div class="ttdef"><b>Definition</b> MQTTClient.h:369</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aa777f80cb3eec5610f976aff30b8c0d6"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aa777f80cb3eec5610f976aff30b8c0d6">MQTTClient_connect5</a></div><div class="ttdeci">MQTTResponse MQTTClient_connect5(MQTTClient handle, MQTTClient_connectOptions *options, MQTTProperties *connectProperties, MQTTProperties *willProperties)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aa8731be3dbc6a25f41f037f8bbbb054b"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aa8731be3dbc6a25f41f037f8bbbb054b">MQTTClient_unsubscribe</a></div><div class="ttdeci">int MQTTClient_unsubscribe(MQTTClient handle, const char *topic)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aaa8ae61cd65c9dc0846df10122d7bd4e"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aaa8ae61cd65c9dc0846df10122d7bd4e">MQTTClient_connect</a></div><div class="ttdeci">int MQTTClient_connect(MQTTClient handle, MQTTClient_connectOptions *options)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aad27d07782991a4937ebf2f39a021f83"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aad27d07782991a4937ebf2f39a021f83">MQTTClient_setCallbacks</a></div><div class="ttdeci">int MQTTClient_setCallbacks(MQTTClient handle, void *context, MQTTClient_connectionLost *cl, MQTTClient_messageArrived *ma, MQTTClient_deliveryComplete *dc)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_abd8abde4f39d3e689029de27f7a98a65"><div class="ttname"><a href="_m_q_t_t_client_8h.html#abd8abde4f39d3e689029de27f7a98a65">MQTTClient_freeMessage</a></div><div class="ttdeci">void MQTTClient_freeMessage(MQTTClient_message **msg)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_abef83794d8252551ed248cde6eb845a6"><div class="ttname"><a href="_m_q_t_t_client_8h.html#abef83794d8252551ed248cde6eb845a6">MQTTClient_deliveryComplete</a></div><div class="ttdeci">void MQTTClient_deliveryComplete(void *context, MQTTClient_deliveryToken dt)</div><div class="ttdef"><b>Definition</b> MQTTClient.h:391</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_ace320b8a92c7087d9dd5cf242d50389d"><div class="ttname"><a href="_m_q_t_t_client_8h.html#ace320b8a92c7087d9dd5cf242d50389d">MQTTClient_publishMessage</a></div><div class="ttdeci">int MQTTClient_publishMessage(MQTTClient handle, const char *topicName, MQTTClient_message *msg, MQTTClient_deliveryToken *dt)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_ade24f717a9b39d38b081e1d5e0db1661"><div class="ttname"><a href="_m_q_t_t_client_8h.html#ade24f717a9b39d38b081e1d5e0db1661">MQTTClient_createWithOptions</a></div><div class="ttdeci">int MQTTClient_createWithOptions(MQTTClient *handle, const char *serverURI, const char *clientId, int persistence_type, void *persistence_context, MQTTClient_createOptions *options)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_ae700c3f5cfea3813264ce95e7c8cf498"><div class="ttname"><a href="_m_q_t_t_client_8h.html#ae700c3f5cfea3813264ce95e7c8cf498">MQTTClient_destroy</a></div><div class="ttdeci">void MQTTClient_destroy(MQTTClient *handle)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aef6eba956a5ff6072854a9e353487087"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aef6eba956a5ff6072854a9e353487087">MQTTClient_getVersionInfo</a></div><div class="ttdeci">MQTTClient_nameValue * MQTTClient_getVersionInfo(void)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_af35ab7375435f7b6388c5ff4610dad3d"><div class="ttname"><a href="_m_q_t_t_client_8h.html#af35ab7375435f7b6388c5ff4610dad3d">MQTTClient_subscribe5</a></div><div class="ttdeci">MQTTResponse MQTTClient_subscribe5(MQTTClient handle, const char *topic, int qos, MQTTSubscribe_options *opts, MQTTProperties *props)</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_afa5758290a1162e5135bca97bbfd5774"><div class="ttname"><a href="_m_q_t_t_client_8h.html#afa5758290a1162e5135bca97bbfd5774">MQTTClient_traceCallback</a></div><div class="ttdeci">void MQTTClient_traceCallback(enum MQTTCLIENT_TRACE_LEVELS level, char *message)</div><div class="ttdef"><b>Definition</b> MQTTClient.h:1459</div></div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_afe9c34013c3511b8ef6cd36bf703678d"><div class="ttname"><a href="_m_q_t_t_client_8h.html#afe9c34013c3511b8ef6cd36bf703678d">MQTTClient_publish</a></div><div class="ttdeci">int MQTTClient_publish(MQTTClient handle, const char *topicName, int payloadlen, const void *payload, int qos, int retained, MQTTClient_deliveryToken *dt)</div></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html">MQTTClientPersistence.h</a></div><div class="ttdoc">This structure represents a persistent data store, used to store outbound and inbound messages,...</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html"><div class="ttname"><a href="_m_q_t_t_properties_8h.html">MQTTProperties.h</a></div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html">MQTTReasonCodes.h</a></div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a></div><div class="ttdeci">MQTTReasonCodes</div><div class="ttdef"><b>Definition</b> MQTTReasonCodes.h:23</div></div>
<div class="ttc" id="a_m_q_t_t_subscribe_opts_8h_html"><div class="ttname"><a href="_m_q_t_t_subscribe_opts_8h.html">MQTTSubscribeOpts.h</a></div></div>
<div class="ttc" id="astruct_m_q_t_t_client___s_s_l_options_html"><div class="ttname"><a href="struct_m_q_t_t_client___s_s_l_options.html">MQTTClient_SSLOptions</a></div><div class="ttdef"><b>Definition</b> MQTTClient.h:678</div></div>
<div class="ttc" id="astruct_m_q_t_t_client___s_s_l_options_html_a032835d4c4a1c1e19b53c330a673a6e0"><div class="ttname"><a href="struct_m_q_t_t_client___s_s_l_options.html#a032835d4c4a1c1e19b53c330a673a6e0">MQTTClient_SSLOptions::trustStore</a></div><div class="ttdeci">const char * trustStore</div><div class="ttdef"><b>Definition</b> MQTTClient.h:692</div></div>
<div class="ttc" id="astruct_m_q_t_t_client___s_s_l_options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_client___s_s_l_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTClient_SSLOptions::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition</b> MQTTClient.h:689</div></div>
<div class="ttc" id="astruct_m_q_t_t_client___s_s_l_options_html_a0826fcae7c2816e04772c61542c6846b"><div class="ttname"><a href="struct_m_q_t_t_client___s_s_l_options.html#a0826fcae7c2816e04772c61542c6846b">MQTTClient_SSLOptions::disableDefaultTrustStore</a></div><div class="ttdeci">int disableDefaultTrustStore</div><div class="ttdef"><b>Definition</b> MQTTClient.h:770</div></div>
<div class="ttc" id="astruct_m_q_t_t_client___s_s_l_options_html_a189f11195f4d5a70024adffdb050885f"><div class="ttname"><a href="struct_m_q_t_t_client___s_s_l_options.html#a189f11195f4d5a70024adffdb050885f">MQTTClient_SSLOptions::ssl_error_context</a></div><div class="ttdeci">void * ssl_error_context</div><div class="ttdef"><b>Definition</b> MQTTClient.h:750</div></div>
<div class="ttc" id="astruct_m_q_t_t_client___s_s_l_options_html_a26f5d839c92f9772c2a5d05486277a42"><div class="ttname"><a href="struct_m_q_t_t_client___s_s_l_options.html#a26f5d839c92f9772c2a5d05486277a42">MQTTClient_SSLOptions::protos_len</a></div><div class="ttdeci">unsigned int protos_len</div><div class="ttdef"><b>Definition</b> MQTTClient.h:785</div></div>
<div class="ttc" id="astruct_m_q_t_t_client___s_s_l_options_html_a3078b3c824cc9753a57898072445c34d"><div class="ttname"><a href="struct_m_q_t_t_client___s_s_l_options.html#a3078b3c824cc9753a57898072445c34d">MQTTClient_SSLOptions::CApath</a></div><div class="ttdeci">const char * CApath</div><div class="ttdef"><b>Definition</b> MQTTClient.h:738</div></div>
<div class="ttc" id="astruct_m_q_t_t_client___s_s_l_options_html_a32b476382955289ce427112b59f21c3e"><div class="ttname"><a href="struct_m_q_t_t_client___s_s_l_options.html#a32b476382955289ce427112b59f21c3e">MQTTClient_SSLOptions::keyStore</a></div><div class="ttdeci">const char * keyStore</div><div class="ttdef"><b>Definition</b> MQTTClient.h:697</div></div>
<div class="ttc" id="astruct_m_q_t_t_client___s_s_l_options_html_a3543ea1481b68d73cdde833280bb9c45"><div class="ttname"><a href="struct_m_q_t_t_client___s_s_l_options.html#a3543ea1481b68d73cdde833280bb9c45">MQTTClient_SSLOptions::sslVersion</a></div><div class="ttdeci">int sslVersion</div><div class="ttdef"><b>Definition</b> MQTTClient.h:724</div></div>
<div class="ttc" id="astruct_m_q_t_t_client___s_s_l_options_html_a4f8661600fb8bacf031150f8dcd293a5"><div class="ttname"><a href="struct_m_q_t_t_client___s_s_l_options.html#a4f8661600fb8bacf031150f8dcd293a5">MQTTClient_SSLOptions::protos</a></div><div class="ttdeci">const unsigned char * protos</div><div class="ttdef"><b>Definition</b> MQTTClient.h:779</div></div>
<div class="ttc" id="astruct_m_q_t_t_client___s_s_l_options_html_a75f6c13b7634e15f96dd9f17db6cf0be"><div class="ttname"><a href="struct_m_q_t_t_client___s_s_l_options.html#a75f6c13b7634e15f96dd9f17db6cf0be">MQTTClient_SSLOptions::enableServerCertAuth</a></div><div class="ttdeci">int enableServerCertAuth</div><div class="ttdef"><b>Definition</b> MQTTClient.h:718</div></div>
<div class="ttc" id="astruct_m_q_t_t_client___s_s_l_options_html_a7dd436cbb916fba200595c3519f09ec4"><div class="ttname"><a href="struct_m_q_t_t_client___s_s_l_options.html#a7dd436cbb916fba200595c3519f09ec4">MQTTClient_SSLOptions::privateKey</a></div><div class="ttdeci">const char * privateKey</div><div class="ttdef"><b>Definition</b> MQTTClient.h:702</div></div>
<div class="ttc" id="astruct_m_q_t_t_client___s_s_l_options_html_a94900629685d5ed08f66fd2931f573ce"><div class="ttname"><a href="struct_m_q_t_t_client___s_s_l_options.html#a94900629685d5ed08f66fd2931f573ce">MQTTClient_SSLOptions::verify</a></div><div class="ttdeci">int verify</div><div class="ttdef"><b>Definition</b> MQTTClient.h:731</div></div>
<div class="ttc" id="astruct_m_q_t_t_client___s_s_l_options_html_aa683926d52134077f27d6dc67bda13ab"><div class="ttname"><a href="struct_m_q_t_t_client___s_s_l_options.html#aa683926d52134077f27d6dc67bda13ab">MQTTClient_SSLOptions::enabledCipherSuites</a></div><div class="ttdeci">const char * enabledCipherSuites</div><div class="ttdef"><b>Definition</b> MQTTClient.h:715</div></div>
<div class="ttc" id="astruct_m_q_t_t_client___s_s_l_options_html_ab7f597518dd5b9db5a515081f8e0bd1f"><div class="ttname"><a href="struct_m_q_t_t_client___s_s_l_options.html#ab7f597518dd5b9db5a515081f8e0bd1f">MQTTClient_SSLOptions::ssl_psk_context</a></div><div class="ttdeci">void * ssl_psk_context</div><div class="ttdef"><b>Definition</b> MQTTClient.h:763</div></div>
<div class="ttc" id="astruct_m_q_t_t_client___s_s_l_options_html_abb427571ba37b51f6985f1a6906ca031"><div class="ttname"><a href="struct_m_q_t_t_client___s_s_l_options.html#abb427571ba37b51f6985f1a6906ca031">MQTTClient_SSLOptions::privateKeyPassword</a></div><div class="ttdeci">const char * privateKeyPassword</div><div class="ttdef"><b>Definition</b> MQTTClient.h:705</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html">MQTTClient_connectOptions</a></div><div class="ttdef"><b>Definition</b> MQTTClient.h:831</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_a018eec60631f40c01e6dcb727bffd33f"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#a018eec60631f40c01e6dcb727bffd33f">MQTTClient_connectOptions::httpHeaders</a></div><div class="ttdeci">const MQTTClient_nameValue * httpHeaders</div><div class="ttdef"><b>Definition</b> MQTTClient.h:976</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_a036c36a2a4d3a3ffae9ab4dd8b3e7f7b"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">MQTTClient_connectOptions::cleansession</a></div><div class="ttdeci">int cleansession</div><div class="ttdef"><b>Definition</b> MQTTClient.h:876</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTClient_connectOptions::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition</b> MQTTClient.h:844</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_a0a880e99d47eb2efe552abe5079bdc9d"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#a0a880e99d47eb2efe552abe5079bdc9d">MQTTClient_connectOptions::will</a></div><div class="ttdeci">MQTTClient_willOptions * will</div><div class="ttdef"><b>Definition</b> MQTTClient.h:892</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_a0d49d74db4c035719c3867723cf7e779"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#a0d49d74db4c035719c3867723cf7e779">MQTTClient_connectOptions::data</a></div><div class="ttdeci">const void * data</div><div class="ttdef"><b>Definition</b> MQTTClient.h:963</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_a12d546fd0ccf4e1091b18e1b735c7240"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTClient_connectOptions::MQTTVersion</a></div><div class="ttdeci">int MQTTVersion</div><div class="ttdef"><b>Definition</b> MQTTClient.h:947</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_a313446ca7679b36652722ffe53d05228"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#a313446ca7679b36652722ffe53d05228">MQTTClient_connectOptions::serverURI</a></div><div class="ttdeci">const char * serverURI</div><div class="ttdef"><b>Definition</b> MQTTClient.h:953</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_a388b78d8a75658928238f700f207ad92"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#a388b78d8a75658928238f700f207ad92">MQTTClient_connectOptions::httpsProxy</a></div><div class="ttdeci">const char * httpsProxy</div><div class="ttdef"><b>Definition</b> MQTTClient.h:986</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_a38c6aa24b36d981c49405db425c24db0"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#a38c6aa24b36d981c49405db425c24db0">MQTTClient_connectOptions::connectTimeout</a></div><div class="ttdeci">int connectTimeout</div><div class="ttdef"><b>Definition</b> MQTTClient.h:908</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_a44baf2cb9a0bbcec3ed2eace43f832d1"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#a44baf2cb9a0bbcec3ed2eace43f832d1">MQTTClient_connectOptions::sessionPresent</a></div><div class="ttdeci">int sessionPresent</div><div class="ttdef"><b>Definition</b> MQTTClient.h:955</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_a8a0b0f0fc7c675312dc232e2458078c7"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#a8a0b0f0fc7c675312dc232e2458078c7">MQTTClient_connectOptions::ssl</a></div><div class="ttdeci">MQTTClient_SSLOptions * ssl</div><div class="ttdef"><b>Definition</b> MQTTClient.h:921</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_a9f1cdffc99659fd4e2d20e6de3c64df0"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#a9f1cdffc99659fd4e2d20e6de3c64df0">MQTTClient_connectOptions::reliable</a></div><div class="ttdeci">int reliable</div><div class="ttdef"><b>Definition</b> MQTTClient.h:886</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_aa4a2ebcb494493f648ae1e6975672575"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#aa4a2ebcb494493f648ae1e6975672575">MQTTClient_connectOptions::password</a></div><div class="ttdeci">const char * password</div><div class="ttdef"><b>Definition</b> MQTTClient.h:904</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_aa82629005937abd92e97084a428cd61f"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#aa82629005937abd92e97084a428cd61f">MQTTClient_connectOptions::serverURIcount</a></div><div class="ttdeci">int serverURIcount</div><div class="ttdef"><b>Definition</b> MQTTClient.h:925</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_aba22d81c407fb2ba590dba476240d3e9"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#aba22d81c407fb2ba590dba476240d3e9">MQTTClient_connectOptions::serverURIs</a></div><div class="ttdeci">char *const  * serverURIs</div><div class="ttdef"><b>Definition</b> MQTTClient.h:939</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_aba2dfcdfda80edcb531a5a7115d3e043"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#aba2dfcdfda80edcb531a5a7115d3e043">MQTTClient_connectOptions::username</a></div><div class="ttdeci">const char * username</div><div class="ttdef"><b>Definition</b> MQTTClient.h:898</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_ac73f57846c42bcaa9a47e6721a957748"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#ac73f57846c42bcaa9a47e6721a957748">MQTTClient_connectOptions::retryInterval</a></div><div class="ttdeci">int retryInterval</div><div class="ttdef"><b>Definition</b> MQTTClient.h:916</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_ac8dd0930672a9c7d71fc645aa1f0521d"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#ac8dd0930672a9c7d71fc645aa1f0521d">MQTTClient_connectOptions::keepAliveInterval</a></div><div class="ttdeci">int keepAliveInterval</div><div class="ttdef"><b>Definition</b> MQTTClient.h:854</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_acdcb75a5d5981da027bce83849140f7b"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#acdcb75a5d5981da027bce83849140f7b">MQTTClient_connectOptions::cleanstart</a></div><div class="ttdeci">int cleanstart</div><div class="ttdef"><b>Definition</b> MQTTClient.h:972</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_add124780ab2de397a96780576c2f112c"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#add124780ab2de397a96780576c2f112c">MQTTClient_connectOptions::httpProxy</a></div><div class="ttdeci">const char * httpProxy</div><div class="ttdef"><b>Definition</b> MQTTClient.h:982</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_ae3f99bf4663ab7b9e9259feeba41fab2"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#ae3f99bf4663ab7b9e9259feeba41fab2">MQTTClient_connectOptions::maxInflightMessages</a></div><div class="ttdeci">int maxInflightMessages</div><div class="ttdef"><b>Definition</b> MQTTClient.h:968</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__connect_options_html_afed088663f8704004425cdae2120b9b3"><div class="ttname"><a href="struct_m_q_t_t_client__connect_options.html#afed088663f8704004425cdae2120b9b3">MQTTClient_connectOptions::len</a></div><div class="ttdeci">int len</div><div class="ttdef"><b>Definition</b> MQTTClient.h:962</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__create_options_html"><div class="ttname"><a href="struct_m_q_t_t_client__create_options.html">MQTTClient_createOptions</a></div><div class="ttdef"><b>Definition</b> MQTTClient.h:555</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__create_options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_client__create_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTClient_createOptions::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition</b> MQTTClient.h:559</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__create_options_html_a12d546fd0ccf4e1091b18e1b735c7240"><div class="ttname"><a href="struct_m_q_t_t_client__create_options.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTClient_createOptions::MQTTVersion</a></div><div class="ttdeci">int MQTTVersion</div><div class="ttdef"><b>Definition</b> MQTTClient.h:565</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__init__options_html"><div class="ttname"><a href="struct_m_q_t_t_client__init__options.html">MQTTClient_init_options</a></div><div class="ttdef"><b>Definition</b> MQTTClient.h:235</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__init__options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_client__init__options.html#a0761a5e5be0383882e42924de8e51f82">MQTTClient_init_options::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition</b> MQTTClient.h:239</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__init__options_html_a5929146596391e2838ef95feb89776da"><div class="ttname"><a href="struct_m_q_t_t_client__init__options.html#a5929146596391e2838ef95feb89776da">MQTTClient_init_options::do_openssl_init</a></div><div class="ttdeci">int do_openssl_init</div><div class="ttdef"><b>Definition</b> MQTTClient.h:241</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__message_html"><div class="ttname"><a href="struct_m_q_t_t_client__message.html">MQTTClient_message</a></div><div class="ttdef"><b>Definition</b> MQTTClient.h:277</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__message_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_client__message.html#a0761a5e5be0383882e42924de8e51f82">MQTTClient_message::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition</b> MQTTClient.h:282</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__message_html_a1594008402f7307e4de8fa6131656dde"><div class="ttname"><a href="struct_m_q_t_t_client__message.html#a1594008402f7307e4de8fa6131656dde">MQTTClient_message::properties</a></div><div class="ttdeci">MQTTProperties properties</div><div class="ttdef"><b>Definition</b> MQTTClient.h:334</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__message_html_a35738099155a0e4f54050da474bab2e7"><div class="ttname"><a href="struct_m_q_t_t_client__message.html#a35738099155a0e4f54050da474bab2e7">MQTTClient_message::qos</a></div><div class="ttdeci">int qos</div><div class="ttdef"><b>Definition</b> MQTTClient.h:300</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__message_html_a6174c42da8c55c86e7255be2848dc4ac"><div class="ttname"><a href="struct_m_q_t_t_client__message.html#a6174c42da8c55c86e7255be2848dc4ac">MQTTClient_message::msgid</a></div><div class="ttdeci">int msgid</div><div class="ttdef"><b>Definition</b> MQTTClient.h:330</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__message_html_a6a4904c112507a43e7dc8495b62cc0fc"><div class="ttname"><a href="struct_m_q_t_t_client__message.html#a6a4904c112507a43e7dc8495b62cc0fc">MQTTClient_message::retained</a></div><div class="ttdeci">int retained</div><div class="ttdef"><b>Definition</b> MQTTClient.h:319</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__message_html_a9eff55064941fb604452abb0050ea99d"><div class="ttname"><a href="struct_m_q_t_t_client__message.html#a9eff55064941fb604452abb0050ea99d">MQTTClient_message::payload</a></div><div class="ttdeci">void * payload</div><div class="ttdef"><b>Definition</b> MQTTClient.h:286</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__message_html_aa3cb44feb3ae6d11b3a4cad2d94cb33a"><div class="ttname"><a href="struct_m_q_t_t_client__message.html#aa3cb44feb3ae6d11b3a4cad2d94cb33a">MQTTClient_message::payloadlen</a></div><div class="ttdeci">int payloadlen</div><div class="ttdef"><b>Definition</b> MQTTClient.h:284</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__message_html_adc4cf3f551bb367858644559d69cfdf5"><div class="ttname"><a href="struct_m_q_t_t_client__message.html#adc4cf3f551bb367858644559d69cfdf5">MQTTClient_message::dup</a></div><div class="ttdeci">int dup</div><div class="ttdef"><b>Definition</b> MQTTClient.h:326</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__name_value_html"><div class="ttname"><a href="struct_m_q_t_t_client__name_value.html">MQTTClient_nameValue</a></div><div class="ttdef"><b>Definition</b> MQTTClient.h:798</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__name_value_html_a8556878012feffc9e0beb86cd78f424d"><div class="ttname"><a href="struct_m_q_t_t_client__name_value.html#a8556878012feffc9e0beb86cd78f424d">MQTTClient_nameValue::value</a></div><div class="ttdeci">const char * value</div><div class="ttdef"><b>Definition</b> MQTTClient.h:800</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__name_value_html_a8f8f80d37794cde9472343e4487ba3eb"><div class="ttname"><a href="struct_m_q_t_t_client__name_value.html#a8f8f80d37794cde9472343e4487ba3eb">MQTTClient_nameValue::name</a></div><div class="ttdeci">const char * name</div><div class="ttdef"><b>Definition</b> MQTTClient.h:799</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__will_options_html"><div class="ttname"><a href="struct_m_q_t_t_client__will_options.html">MQTTClient_willOptions</a></div><div class="ttdef"><b>Definition</b> MQTTClient.h:630</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__will_options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_client__will_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTClient_willOptions::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition</b> MQTTClient.h:636</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__will_options_html_a0d49d74db4c035719c3867723cf7e779"><div class="ttname"><a href="struct_m_q_t_t_client__will_options.html#a0d49d74db4c035719c3867723cf7e779">MQTTClient_willOptions::data</a></div><div class="ttdeci">const void * data</div><div class="ttdef"><b>Definition</b> MQTTClient.h:654</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__will_options_html_a0e20a7b350881d05108d6342884198a5"><div class="ttname"><a href="struct_m_q_t_t_client__will_options.html#a0e20a7b350881d05108d6342884198a5">MQTTClient_willOptions::topicName</a></div><div class="ttdeci">const char * topicName</div><div class="ttdef"><b>Definition</b> MQTTClient.h:638</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__will_options_html_a254bf0858da09c96a48daf64404eb4f8"><div class="ttname"><a href="struct_m_q_t_t_client__will_options.html#a254bf0858da09c96a48daf64404eb4f8">MQTTClient_willOptions::message</a></div><div class="ttdeci">const char * message</div><div class="ttdef"><b>Definition</b> MQTTClient.h:640</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__will_options_html_a35738099155a0e4f54050da474bab2e7"><div class="ttname"><a href="struct_m_q_t_t_client__will_options.html#a35738099155a0e4f54050da474bab2e7">MQTTClient_willOptions::qos</a></div><div class="ttdeci">int qos</div><div class="ttdef"><b>Definition</b> MQTTClient.h:649</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__will_options_html_a6a4904c112507a43e7dc8495b62cc0fc"><div class="ttname"><a href="struct_m_q_t_t_client__will_options.html#a6a4904c112507a43e7dc8495b62cc0fc">MQTTClient_willOptions::retained</a></div><div class="ttdeci">int retained</div><div class="ttdef"><b>Definition</b> MQTTClient.h:644</div></div>
<div class="ttc" id="astruct_m_q_t_t_client__will_options_html_afed088663f8704004425cdae2120b9b3"><div class="ttname"><a href="struct_m_q_t_t_client__will_options.html#afed088663f8704004425cdae2120b9b3">MQTTClient_willOptions::len</a></div><div class="ttdeci">int len</div><div class="ttdef"><b>Definition</b> MQTTClient.h:653</div></div>
<div class="ttc" id="astruct_m_q_t_t_properties_html"><div class="ttname"><a href="struct_m_q_t_t_properties.html">MQTTProperties</a></div><div class="ttdef"><b>Definition</b> MQTTProperties.h:116</div></div>
<div class="ttc" id="astruct_m_q_t_t_response_html"><div class="ttname"><a href="struct_m_q_t_t_response.html">MQTTResponse</a></div><div class="ttdef"><b>Definition</b> MQTTClient.h:1033</div></div>
<div class="ttc" id="astruct_m_q_t_t_response_html_a2199c9d905dbfa279895cf8123c10f4f"><div class="ttname"><a href="struct_m_q_t_t_response.html#a2199c9d905dbfa279895cf8123c10f4f">MQTTResponse::reasonCodes</a></div><div class="ttdeci">enum MQTTReasonCodes * reasonCodes</div><div class="ttdef"><b>Definition</b> MQTTClient.h:1037</div></div>
<div class="ttc" id="astruct_m_q_t_t_response_html_a580d8a8ecb285f5a86c2a3865438f8ee"><div class="ttname"><a href="struct_m_q_t_t_response.html#a580d8a8ecb285f5a86c2a3865438f8ee">MQTTResponse::reasonCode</a></div><div class="ttdeci">enum MQTTReasonCodes reasonCode</div><div class="ttdef"><b>Definition</b> MQTTClient.h:1035</div></div>
<div class="ttc" id="astruct_m_q_t_t_response_html_a72e9294467b8329a78bc840fe6c5b230"><div class="ttname"><a href="struct_m_q_t_t_response.html#a72e9294467b8329a78bc840fe6c5b230">MQTTResponse::properties</a></div><div class="ttdeci">MQTTProperties * properties</div><div class="ttdef"><b>Definition</b> MQTTClient.h:1038</div></div>
<div class="ttc" id="astruct_m_q_t_t_response_html_aad880fc4455c253781e8968f2239d56f"><div class="ttname"><a href="struct_m_q_t_t_response.html#aad880fc4455c253781e8968f2239d56f">MQTTResponse::version</a></div><div class="ttdeci">int version</div><div class="ttdef"><b>Definition</b> MQTTClient.h:1034</div></div>
<div class="ttc" id="astruct_m_q_t_t_response_html_ac97316626bd4faa6b71277c221275f4b"><div class="ttname"><a href="struct_m_q_t_t_response.html#ac97316626bd4faa6b71277c221275f4b">MQTTResponse::reasonCodeCount</a></div><div class="ttdeci">int reasonCodeCount</div><div class="ttdef"><b>Definition</b> MQTTClient.h:1036</div></div>
<div class="ttc" id="astruct_m_q_t_t_subscribe__options_html"><div class="ttname"><a href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a></div><div class="ttdef"><b>Definition</b> MQTTSubscribeOpts.h:22</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:06 for Paho MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
