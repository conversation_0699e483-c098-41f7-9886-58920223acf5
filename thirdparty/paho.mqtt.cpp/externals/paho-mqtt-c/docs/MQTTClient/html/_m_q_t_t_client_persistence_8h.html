<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho MQTT C Client Library: MQTTClientPersistence.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#define-members">Macros</a> &#124;
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle"><div class="title">MQTTClientPersistence.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>This structure represents a persistent data store, used to store outbound and inbound messages, in order to achieve reliable messaging.  
<a href="#details">More...</a></p>

<p><a href="_m_q_t_t_client_persistence_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_client__persistence.html">MQTTClient_persistence</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">A structure containing the function pointers to a persistence implementation and the context or state that will be shared across all the persistence functions.  <a href="struct_m_q_t_t_client__persistence.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:aaa948291718a9c06369b854b0f64bc32" id="r_aaa948291718a9c06369b854b0f64bc32"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aaa948291718a9c06369b854b0f64bc32">MQTTCLIENT_PERSISTENCE_DEFAULT</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:aaa948291718a9c06369b854b0f64bc32"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae01e089313a65ac4661ed216b6ac00fa" id="r_ae01e089313a65ac4661ed216b6ac00fa"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae01e089313a65ac4661ed216b6ac00fa">MQTTCLIENT_PERSISTENCE_NONE</a>&#160;&#160;&#160;1</td></tr>
<tr class="separator:ae01e089313a65ac4661ed216b6ac00fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5dc68b8616e4041e037bad94ce07681b" id="r_a5dc68b8616e4041e037bad94ce07681b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5dc68b8616e4041e037bad94ce07681b">MQTTCLIENT_PERSISTENCE_USER</a>&#160;&#160;&#160;2</td></tr>
<tr class="separator:a5dc68b8616e4041e037bad94ce07681b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab716e21e53c84a5ad62aa962a2a8f7db" id="r_ab716e21e53c84a5ad62aa962a2a8f7db"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab716e21e53c84a5ad62aa962a2a8f7db">MQTTCLIENT_PERSISTENCE_ERROR</a>&#160;&#160;&#160;-2</td></tr>
<tr class="separator:ab716e21e53c84a5ad62aa962a2a8f7db"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:a4c7d332bb16907058ae3b375488b6008" id="r_a4c7d332bb16907058ae3b375488b6008"><td class="memItemLeft" align="right" valign="top">typedef int(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4c7d332bb16907058ae3b375488b6008">Persistence_open</a>) (void **handle, const char *clientID, const char *serverURI, void *context)</td></tr>
<tr class="memdesc:a4c7d332bb16907058ae3b375488b6008"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize the persistent store.  <br /></td></tr>
<tr class="separator:a4c7d332bb16907058ae3b375488b6008"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3582de2c87e89f617e8e553b2a0e279a" id="r_a3582de2c87e89f617e8e553b2a0e279a"><td class="memItemLeft" align="right" valign="top">typedef int(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3582de2c87e89f617e8e553b2a0e279a">Persistence_close</a>) (void *handle)</td></tr>
<tr class="memdesc:a3582de2c87e89f617e8e553b2a0e279a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Close the persistent store referred to by the handle.  <br /></td></tr>
<tr class="separator:a3582de2c87e89f617e8e553b2a0e279a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44679cab77cfbd6e2a4639cdd27ac80c" id="r_a44679cab77cfbd6e2a4639cdd27ac80c"><td class="memItemLeft" align="right" valign="top">typedef int(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a44679cab77cfbd6e2a4639cdd27ac80c">Persistence_put</a>) (void *handle, char *key, int bufcount, char *buffers[], int buflens[])</td></tr>
<tr class="memdesc:a44679cab77cfbd6e2a4639cdd27ac80c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Put the specified data into the persistent store.  <br /></td></tr>
<tr class="separator:a44679cab77cfbd6e2a4639cdd27ac80c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc3aff3c570fa5509e9d6814a85ab867" id="r_adc3aff3c570fa5509e9d6814a85ab867"><td class="memItemLeft" align="right" valign="top">typedef int(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#adc3aff3c570fa5509e9d6814a85ab867">Persistence_get</a>) (void *handle, char *key, char **buffer, int *buflen)</td></tr>
<tr class="memdesc:adc3aff3c570fa5509e9d6814a85ab867"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieve the specified data from the persistent store.  <br /></td></tr>
<tr class="separator:adc3aff3c570fa5509e9d6814a85ab867"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a73350bf7208658bf5434a59f7bdbae90" id="r_a73350bf7208658bf5434a59f7bdbae90"><td class="memItemLeft" align="right" valign="top">typedef int(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a73350bf7208658bf5434a59f7bdbae90">Persistence_remove</a>) (void *handle, char *key)</td></tr>
<tr class="memdesc:a73350bf7208658bf5434a59f7bdbae90"><td class="mdescLeft">&#160;</td><td class="mdescRight">Remove the data for the specified key from the store.  <br /></td></tr>
<tr class="separator:a73350bf7208658bf5434a59f7bdbae90"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2601cc91eeabdbf9578f8dd45e4997a8" id="r_a2601cc91eeabdbf9578f8dd45e4997a8"><td class="memItemLeft" align="right" valign="top">typedef int(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2601cc91eeabdbf9578f8dd45e4997a8">Persistence_keys</a>) (void *handle, char ***keys, int *nkeys)</td></tr>
<tr class="memdesc:a2601cc91eeabdbf9578f8dd45e4997a8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the keys in this persistent data store.  <br /></td></tr>
<tr class="separator:a2601cc91eeabdbf9578f8dd45e4997a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acee7097c1a0ab44b98c870f533687887" id="r_acee7097c1a0ab44b98c870f533687887"><td class="memItemLeft" align="right" valign="top">typedef int(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acee7097c1a0ab44b98c870f533687887">Persistence_clear</a>) (void *handle)</td></tr>
<tr class="memdesc:acee7097c1a0ab44b98c870f533687887"><td class="mdescLeft">&#160;</td><td class="mdescRight">Clears the persistence store, so that it no longer contains any persisted data.  <br /></td></tr>
<tr class="separator:acee7097c1a0ab44b98c870f533687887"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a753a0f9a9c51284d63a907af19c7bbba" id="r_a753a0f9a9c51284d63a907af19c7bbba"><td class="memItemLeft" align="right" valign="top">typedef int(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a753a0f9a9c51284d63a907af19c7bbba">Persistence_containskey</a>) (void *handle, char *key)</td></tr>
<tr class="memdesc:a753a0f9a9c51284d63a907af19c7bbba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether any data has been persisted using the specified key.  <br /></td></tr>
<tr class="separator:a753a0f9a9c51284d63a907af19c7bbba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab865640a1cc53b68622004c5a2d29fae" id="r_ab865640a1cc53b68622004c5a2d29fae"><td class="memItemLeft" align="right" valign="top">typedef int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab865640a1cc53b68622004c5a2d29fae">MQTTPersistence_beforeWrite</a>(void *context, int bufcount, char *buffers[], int buflens[])</td></tr>
<tr class="separator:ab865640a1cc53b68622004c5a2d29fae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af5a966a574c6ad7a35f1ebb7edd5c1c4" id="r_af5a966a574c6ad7a35f1ebb7edd5c1c4"><td class="memItemLeft" align="right" valign="top">typedef int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af5a966a574c6ad7a35f1ebb7edd5c1c4">MQTTPersistence_afterRead</a>(void *context, char **buffer, int *buflen)</td></tr>
<tr class="separator:af5a966a574c6ad7a35f1ebb7edd5c1c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>This structure represents a persistent data store, used to store outbound and inbound messages, in order to achieve reliable messaging. </p>
<p>The MQTT Client persists QoS1 and QoS2 messages in order to meet the assurances of delivery associated with these <a class="el" href="qos.html">Quality of service</a> levels. The messages are saved in persistent storage The type and context of the persistence implementation are specified when the MQTT client is created (see <a class="el" href="_m_q_t_t_client_8h.html#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>). The default persistence type (<a class="el" href="#aaa948291718a9c06369b854b0f64bc32">MQTTCLIENT_PERSISTENCE_DEFAULT</a>) uses a file system-based persistence mechanism. The <em>persistence_context</em> argument passed to <a class="el" href="_m_q_t_t_client_8h.html#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a> when using the default peristence is a string representing the location of the persistence directory. If the context argument is NULL, the working directory will be used.</p>
<p>To use memory-based persistence, an application passes <a class="el" href="#ae01e089313a65ac4661ed216b6ac00fa">MQTTCLIENT_PERSISTENCE_NONE</a> as the <em>persistence_type</em> to <a class="el" href="_m_q_t_t_client_8h.html#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>. This can lead to message loss in certain situations, but can be appropriate in some cases (see <a class="el" href="qos.html">Quality of service</a>).</p>
<p>Client applications can provide their own persistence mechanism by passing <a class="el" href="#a5dc68b8616e4041e037bad94ce07681b">MQTTCLIENT_PERSISTENCE_USER</a> as the <em>persistence_type</em>. To implement a custom persistence mechanism, the application must pass an initialized <a class="el" href="struct_m_q_t_t_client__persistence.html" title="A structure containing the function pointers to a persistence implementation and the context or state...">MQTTClient_persistence</a> structure as the <em>persistence_context</em> argument to <a class="el" href="_m_q_t_t_client_8h.html#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>.</p>
<p>If the functions defined return an <a class="el" href="#ab716e21e53c84a5ad62aa962a2a8f7db">MQTTCLIENT_PERSISTENCE_ERROR</a> then the state of the persisted data should remain as it was prior to the function being called. For example, if <a class="el" href="#a44679cab77cfbd6e2a4639cdd27ac80c" title="Put the specified data into the persistent store.">Persistence_put()</a> returns <a class="el" href="#ab716e21e53c84a5ad62aa962a2a8f7db">MQTTCLIENT_PERSISTENCE_ERROR</a>, then it is assumed tha tthe persistent store does not contain the data that was passed to the function. Similarly, if <a class="el" href="#a73350bf7208658bf5434a59f7bdbae90" title="Remove the data for the specified key from the store.">Persistence_remove()</a> returns <a class="el" href="#ab716e21e53c84a5ad62aa962a2a8f7db">MQTTCLIENT_PERSISTENCE_ERROR</a> then it is assumed that the data to be removed is still held in the persistent store.</p>
<p>It is up to the persistence implementation to log any error information that may be required to diagnose a persistence mechanism failure. </p>
</div><h2 class="groupheader">Macro Definition Documentation</h2>
<a id="aaa948291718a9c06369b854b0f64bc32" name="aaa948291718a9c06369b854b0f64bc32"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaa948291718a9c06369b854b0f64bc32">&#9670;&#160;</a></span>MQTTCLIENT_PERSISTENCE_DEFAULT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_PERSISTENCE_DEFAULT&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This <em>persistence_type</em> value specifies the default file system-based persistence mechanism (see <a class="el" href="_m_q_t_t_client_8h.html#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>). </p>

</div>
</div>
<a id="ae01e089313a65ac4661ed216b6ac00fa" name="ae01e089313a65ac4661ed216b6ac00fa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae01e089313a65ac4661ed216b6ac00fa">&#9670;&#160;</a></span>MQTTCLIENT_PERSISTENCE_NONE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_PERSISTENCE_NONE&#160;&#160;&#160;1</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This <em>persistence_type</em> value specifies a memory-based persistence mechanism (see <a class="el" href="_m_q_t_t_client_8h.html#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>). </p>

</div>
</div>
<a id="a5dc68b8616e4041e037bad94ce07681b" name="a5dc68b8616e4041e037bad94ce07681b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5dc68b8616e4041e037bad94ce07681b">&#9670;&#160;</a></span>MQTTCLIENT_PERSISTENCE_USER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_PERSISTENCE_USER&#160;&#160;&#160;2</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This <em>persistence_type</em> value specifies an application-specific persistence mechanism (see <a class="el" href="_m_q_t_t_client_8h.html#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>). </p>

</div>
</div>
<a id="ab716e21e53c84a5ad62aa962a2a8f7db" name="ab716e21e53c84a5ad62aa962a2a8f7db"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab716e21e53c84a5ad62aa962a2a8f7db">&#9670;&#160;</a></span>MQTTCLIENT_PERSISTENCE_ERROR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTCLIENT_PERSISTENCE_ERROR&#160;&#160;&#160;-2</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Application-specific persistence functions must return this error code if there is a problem executing the function. </p>

</div>
</div>
<h2 class="groupheader">Typedef Documentation</h2>
<a id="a4c7d332bb16907058ae3b375488b6008" name="a4c7d332bb16907058ae3b375488b6008"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4c7d332bb16907058ae3b375488b6008">&#9670;&#160;</a></span>Persistence_open</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef int(* Persistence_open) (void **handle, const char *clientID, const char *serverURI, void *context)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Initialize the persistent store. </p>
<p>Either open the existing persistent store for this client ID or create a new one if one doesn't exist. If the persistent store is already open, return without taking any action.</p>
<p>An application can use the same client identifier to connect to many different servers. The <em>clientid</em> in conjunction with the <em>serverURI</em> uniquely identifies the persistence store required.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>The address of a pointer to a handle for this persistence implementation. This function must set handle to a valid reference to the persistence following a successful return. The handle pointer is passed as an argument to all the other persistence functions. It may include the context parameter and/or any other data for use by the persistence functions. </td></tr>
    <tr><td class="paramname">clientID</td><td>The client identifier for which the persistent store should be opened. </td></tr>
    <tr><td class="paramname">serverURI</td><td>The connection string specified when the MQTT client was created (see <a class="el" href="_m_q_t_t_client_8h.html#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient_create()</a>). </td></tr>
    <tr><td class="paramname">context</td><td>A pointer to any data required to initialize the persistent store (see <a class="el" href="struct_m_q_t_t_client__persistence.html" title="A structure containing the function pointers to a persistence implementation and the context or state...">MQTTClient_persistence</a>). </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return 0 if the function completes successfully, otherwise return <a class="el" href="#ab716e21e53c84a5ad62aa962a2a8f7db">MQTTCLIENT_PERSISTENCE_ERROR</a>. </dd></dl>

</div>
</div>
<a id="a3582de2c87e89f617e8e553b2a0e279a" name="a3582de2c87e89f617e8e553b2a0e279a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3582de2c87e89f617e8e553b2a0e279a">&#9670;&#160;</a></span>Persistence_close</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef int(* Persistence_close) (void *handle)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Close the persistent store referred to by the handle. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>The handle pointer from a successful call to <a class="el" href="#a4c7d332bb16907058ae3b375488b6008" title="Initialize the persistent store.">Persistence_open()</a>. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return 0 if the function completes successfully, otherwise return <a class="el" href="#ab716e21e53c84a5ad62aa962a2a8f7db">MQTTCLIENT_PERSISTENCE_ERROR</a>. </dd></dl>

</div>
</div>
<a id="a44679cab77cfbd6e2a4639cdd27ac80c" name="a44679cab77cfbd6e2a4639cdd27ac80c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a44679cab77cfbd6e2a4639cdd27ac80c">&#9670;&#160;</a></span>Persistence_put</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef int(* Persistence_put) (void *handle, char *key, int bufcount, char *buffers[], int buflens[])</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Put the specified data into the persistent store. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>The handle pointer from a successful call to <a class="el" href="#a4c7d332bb16907058ae3b375488b6008" title="Initialize the persistent store.">Persistence_open()</a>. </td></tr>
    <tr><td class="paramname">key</td><td>A string used as the key for the data to be put in the store. The key is later used to retrieve data from the store with <a class="el" href="#adc3aff3c570fa5509e9d6814a85ab867" title="Retrieve the specified data from the persistent store.">Persistence_get()</a>. </td></tr>
    <tr><td class="paramname">bufcount</td><td>The number of buffers to write to the persistence store. </td></tr>
    <tr><td class="paramname">buffers</td><td>An array of pointers to the data buffers associated with this <em>key</em>. </td></tr>
    <tr><td class="paramname">buflens</td><td>An array of lengths of the data buffers. <em>buflen[n]</em> gives the length of <em>buffer[n]</em>. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return 0 if the function completes successfully, otherwise return <a class="el" href="#ab716e21e53c84a5ad62aa962a2a8f7db">MQTTCLIENT_PERSISTENCE_ERROR</a>. </dd></dl>

</div>
</div>
<a id="adc3aff3c570fa5509e9d6814a85ab867" name="adc3aff3c570fa5509e9d6814a85ab867"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adc3aff3c570fa5509e9d6814a85ab867">&#9670;&#160;</a></span>Persistence_get</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef int(* Persistence_get) (void *handle, char *key, char **buffer, int *buflen)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Retrieve the specified data from the persistent store. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>The handle pointer from a successful call to <a class="el" href="#a4c7d332bb16907058ae3b375488b6008" title="Initialize the persistent store.">Persistence_open()</a>. </td></tr>
    <tr><td class="paramname">key</td><td>A string that is the key for the data to be retrieved. This is the same key used to save the data to the store with <a class="el" href="#a44679cab77cfbd6e2a4639cdd27ac80c" title="Put the specified data into the persistent store.">Persistence_put()</a>. </td></tr>
    <tr><td class="paramname">buffer</td><td>The address of a pointer to a buffer. This function sets the pointer to point at the retrieved data, if successful. </td></tr>
    <tr><td class="paramname">buflen</td><td>The address of an int that is set to the length of <em>buffer</em> by this function if successful. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return 0 if the function completes successfully, otherwise return <a class="el" href="#ab716e21e53c84a5ad62aa962a2a8f7db">MQTTCLIENT_PERSISTENCE_ERROR</a>. </dd></dl>

</div>
</div>
<a id="a73350bf7208658bf5434a59f7bdbae90" name="a73350bf7208658bf5434a59f7bdbae90"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a73350bf7208658bf5434a59f7bdbae90">&#9670;&#160;</a></span>Persistence_remove</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef int(* Persistence_remove) (void *handle, char *key)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Remove the data for the specified key from the store. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>The handle pointer from a successful call to <a class="el" href="#a4c7d332bb16907058ae3b375488b6008" title="Initialize the persistent store.">Persistence_open()</a>. </td></tr>
    <tr><td class="paramname">key</td><td>A string that is the key for the data to be removed from the store. This is the same key used to save the data to the store with <a class="el" href="#a44679cab77cfbd6e2a4639cdd27ac80c" title="Put the specified data into the persistent store.">Persistence_put()</a>. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return 0 if the function completes successfully, otherwise return <a class="el" href="#ab716e21e53c84a5ad62aa962a2a8f7db">MQTTCLIENT_PERSISTENCE_ERROR</a>. </dd></dl>

</div>
</div>
<a id="a2601cc91eeabdbf9578f8dd45e4997a8" name="a2601cc91eeabdbf9578f8dd45e4997a8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2601cc91eeabdbf9578f8dd45e4997a8">&#9670;&#160;</a></span>Persistence_keys</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef int(* Persistence_keys) (void *handle, char ***keys, int *nkeys)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the keys in this persistent data store. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>The handle pointer from a successful call to <a class="el" href="#a4c7d332bb16907058ae3b375488b6008" title="Initialize the persistent store.">Persistence_open()</a>. </td></tr>
    <tr><td class="paramname">keys</td><td>The address of a pointer to pointers to strings. Assuming successful execution, this function allocates memory to hold the returned keys (strings used to store the data with <a class="el" href="#a44679cab77cfbd6e2a4639cdd27ac80c" title="Put the specified data into the persistent store.">Persistence_put()</a>). It also allocates memory to hold an array of pointers to these strings. <em>keys</em> is set to point to the array of pointers to strings. </td></tr>
    <tr><td class="paramname">nkeys</td><td>A pointer to the number of keys in this persistent data store. This function sets the number of keys, if successful. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return 0 if the function completes successfully, otherwise return <a class="el" href="#ab716e21e53c84a5ad62aa962a2a8f7db">MQTTCLIENT_PERSISTENCE_ERROR</a>. </dd></dl>

</div>
</div>
<a id="acee7097c1a0ab44b98c870f533687887" name="acee7097c1a0ab44b98c870f533687887"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acee7097c1a0ab44b98c870f533687887">&#9670;&#160;</a></span>Persistence_clear</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef int(* Persistence_clear) (void *handle)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Clears the persistence store, so that it no longer contains any persisted data. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>The handle pointer from a successful call to <a class="el" href="#a4c7d332bb16907058ae3b375488b6008" title="Initialize the persistent store.">Persistence_open()</a>. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return 0 if the function completes successfully, otherwise return <a class="el" href="#ab716e21e53c84a5ad62aa962a2a8f7db">MQTTCLIENT_PERSISTENCE_ERROR</a>. </dd></dl>

</div>
</div>
<a id="a753a0f9a9c51284d63a907af19c7bbba" name="a753a0f9a9c51284d63a907af19c7bbba"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a753a0f9a9c51284d63a907af19c7bbba">&#9670;&#160;</a></span>Persistence_containskey</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef int(* Persistence_containskey) (void *handle, char *key)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns whether any data has been persisted using the specified key. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>The handle pointer from a successful call to <a class="el" href="#a4c7d332bb16907058ae3b375488b6008" title="Initialize the persistent store.">Persistence_open()</a>. </td></tr>
    <tr><td class="paramname">key</td><td>The string to be tested for existence in the store. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return 0 if the key was found in the store, otherwise return <a class="el" href="#ab716e21e53c84a5ad62aa962a2a8f7db">MQTTCLIENT_PERSISTENCE_ERROR</a>. </dd></dl>

</div>
</div>
<a id="ab865640a1cc53b68622004c5a2d29fae" name="ab865640a1cc53b68622004c5a2d29fae"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab865640a1cc53b68622004c5a2d29fae">&#9670;&#160;</a></span>MQTTPersistence_beforeWrite</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef int MQTTPersistence_beforeWrite(void *context, int bufcount, char *buffers[], int buflens[])</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A callback which is invoked just before a write to persistence. This can be used to transform the data, for instance to encrypt it. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">context</td><td>The context as set in ::MQTTAsync_setBeforePersistenceWrite </td></tr>
    <tr><td class="paramname">bufcount</td><td>The number of buffers to write to the persistence store. </td></tr>
    <tr><td class="paramname">buffers</td><td>An array of pointers to the data buffers. </td></tr>
    <tr><td class="paramname">buflens</td><td>An array of lengths of the data buffers. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return 0 if the function completes successfully, otherwise non 0. </dd></dl>

</div>
</div>
<a id="af5a966a574c6ad7a35f1ebb7edd5c1c4" name="af5a966a574c6ad7a35f1ebb7edd5c1c4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af5a966a574c6ad7a35f1ebb7edd5c1c4">&#9670;&#160;</a></span>MQTTPersistence_afterRead</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef int MQTTPersistence_afterRead(void *context, char **buffer, int *buflen)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A callback which is invoked just after a read from persistence. This can be used to transform the data, for instance to decrypt it. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">context</td><td>The context as set in ::MQTTAsync_setAfterPersistenceRead </td></tr>
    <tr><td class="paramname">buffer</td><td>The address of a pointer to a buffer. </td></tr>
    <tr><td class="paramname">buflen</td><td>The address of an int that is the length of the buffer. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return 0 if the function completes successfully, otherwise non 0. </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:06 for Paho MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
