<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho MQTT C Client Library: MQTTClient_SSLOptions Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">MQTTClient_SSLOptions Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="_m_q_t_t_client_8h_source.html">MQTTClient.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:aa5326df180cb23c59afbcab711a06479" id="r_aa5326df180cb23c59afbcab711a06479"><td class="memItemLeft" align="right" valign="top">char&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa5326df180cb23c59afbcab711a06479">struct_id</a> [4]</td></tr>
<tr class="separator:aa5326df180cb23c59afbcab711a06479"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0761a5e5be0383882e42924de8e51f82" id="r_a0761a5e5be0383882e42924de8e51f82"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0761a5e5be0383882e42924de8e51f82">struct_version</a></td></tr>
<tr class="separator:a0761a5e5be0383882e42924de8e51f82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a032835d4c4a1c1e19b53c330a673a6e0" id="r_a032835d4c4a1c1e19b53c330a673a6e0"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a032835d4c4a1c1e19b53c330a673a6e0">trustStore</a></td></tr>
<tr class="separator:a032835d4c4a1c1e19b53c330a673a6e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32b476382955289ce427112b59f21c3e" id="r_a32b476382955289ce427112b59f21c3e"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a32b476382955289ce427112b59f21c3e">keyStore</a></td></tr>
<tr class="separator:a32b476382955289ce427112b59f21c3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7dd436cbb916fba200595c3519f09ec4" id="r_a7dd436cbb916fba200595c3519f09ec4"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7dd436cbb916fba200595c3519f09ec4">privateKey</a></td></tr>
<tr class="separator:a7dd436cbb916fba200595c3519f09ec4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abb427571ba37b51f6985f1a6906ca031" id="r_abb427571ba37b51f6985f1a6906ca031"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abb427571ba37b51f6985f1a6906ca031">privateKeyPassword</a></td></tr>
<tr class="separator:abb427571ba37b51f6985f1a6906ca031"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa683926d52134077f27d6dc67bda13ab" id="r_aa683926d52134077f27d6dc67bda13ab"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa683926d52134077f27d6dc67bda13ab">enabledCipherSuites</a></td></tr>
<tr class="separator:aa683926d52134077f27d6dc67bda13ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a75f6c13b7634e15f96dd9f17db6cf0be" id="r_a75f6c13b7634e15f96dd9f17db6cf0be"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a75f6c13b7634e15f96dd9f17db6cf0be">enableServerCertAuth</a></td></tr>
<tr class="separator:a75f6c13b7634e15f96dd9f17db6cf0be"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3543ea1481b68d73cdde833280bb9c45" id="r_a3543ea1481b68d73cdde833280bb9c45"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3543ea1481b68d73cdde833280bb9c45">sslVersion</a></td></tr>
<tr class="separator:a3543ea1481b68d73cdde833280bb9c45"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a94900629685d5ed08f66fd2931f573ce" id="r_a94900629685d5ed08f66fd2931f573ce"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a94900629685d5ed08f66fd2931f573ce">verify</a></td></tr>
<tr class="separator:a94900629685d5ed08f66fd2931f573ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3078b3c824cc9753a57898072445c34d" id="r_a3078b3c824cc9753a57898072445c34d"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3078b3c824cc9753a57898072445c34d">CApath</a></td></tr>
<tr class="separator:a3078b3c824cc9753a57898072445c34d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a21b6ca8a73ba197e65f6a93365d39c04" id="r_a21b6ca8a73ba197e65f6a93365d39c04"><td class="memItemLeft" align="right" valign="top">int(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a21b6ca8a73ba197e65f6a93365d39c04">ssl_error_cb</a> )(const char *str, size_t len, void *u)</td></tr>
<tr class="separator:a21b6ca8a73ba197e65f6a93365d39c04"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a189f11195f4d5a70024adffdb050885f" id="r_a189f11195f4d5a70024adffdb050885f"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a189f11195f4d5a70024adffdb050885f">ssl_error_context</a></td></tr>
<tr class="separator:a189f11195f4d5a70024adffdb050885f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a94317cdaf352f9ae496976f8a30f8fee" id="r_a94317cdaf352f9ae496976f8a30f8fee"><td class="memItemLeft" align="right" valign="top">unsigned int(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a94317cdaf352f9ae496976f8a30f8fee">ssl_psk_cb</a> )(const char *hint, char *identity, unsigned int max_identity_len, unsigned char *psk, unsigned int max_psk_len, void *u)</td></tr>
<tr class="separator:a94317cdaf352f9ae496976f8a30f8fee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7f597518dd5b9db5a515081f8e0bd1f" id="r_ab7f597518dd5b9db5a515081f8e0bd1f"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab7f597518dd5b9db5a515081f8e0bd1f">ssl_psk_context</a></td></tr>
<tr class="separator:ab7f597518dd5b9db5a515081f8e0bd1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0826fcae7c2816e04772c61542c6846b" id="r_a0826fcae7c2816e04772c61542c6846b"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0826fcae7c2816e04772c61542c6846b">disableDefaultTrustStore</a></td></tr>
<tr class="separator:a0826fcae7c2816e04772c61542c6846b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f8661600fb8bacf031150f8dcd293a5" id="r_a4f8661600fb8bacf031150f8dcd293a5"><td class="memItemLeft" align="right" valign="top">const unsigned char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4f8661600fb8bacf031150f8dcd293a5">protos</a></td></tr>
<tr class="separator:a4f8661600fb8bacf031150f8dcd293a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a26f5d839c92f9772c2a5d05486277a42" id="r_a26f5d839c92f9772c2a5d05486277a42"><td class="memItemLeft" align="right" valign="top">unsigned int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a26f5d839c92f9772c2a5d05486277a42">protos_len</a></td></tr>
<tr class="separator:a26f5d839c92f9772c2a5d05486277a42"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>MQTTClient_sslProperties defines the settings to establish an SSL/TLS connection using the OpenSSL library. It covers the following scenarios:</p><ul>
<li>Server authentication: The client needs the digital certificate of the server. It is included in a store containting trusted material (also known as "trust store").</li>
<li>Mutual authentication: Both client and server are authenticated during the SSL handshake. In addition to the digital certificate of the server in a trust store, the client will need its own digital certificate and the private key used to sign its digital certificate stored in a "key store".</li>
<li>Anonymous connection: Both client and server do not get authenticated and no credentials are needed to establish an SSL connection. Note that this scenario is not fully secure since it is subject to man-in-the-middle attacks. </li>
</ul>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="aa5326df180cb23c59afbcab711a06479" name="aa5326df180cb23c59afbcab711a06479"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa5326df180cb23c59afbcab711a06479">&#9670;&#160;</a></span>struct_id</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char struct_id[4]</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The eyecatcher for this structure. Must be MQTS </p>

</div>
</div>
<a id="a0761a5e5be0383882e42924de8e51f82" name="a0761a5e5be0383882e42924de8e51f82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0761a5e5be0383882e42924de8e51f82">&#9670;&#160;</a></span>struct_version</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int struct_version</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The version number of this structure. Must be 0, 1, 2, 3, 4 or 5. 0 means no sslVersion 1 means no verify, CApath 2 means no ssl_error_context, ssl_error_cb 3 means no ssl_psk_cb, ssl_psk_context, disableDefaultTrustStore 4 means no protos, protos_len </p>

</div>
</div>
<a id="a032835d4c4a1c1e19b53c330a673a6e0" name="a032835d4c4a1c1e19b53c330a673a6e0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a032835d4c4a1c1e19b53c330a673a6e0">&#9670;&#160;</a></span>trustStore</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* trustStore</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The file in PEM format containing the public digital certificates trusted by the client. </p>

</div>
</div>
<a id="a32b476382955289ce427112b59f21c3e" name="a32b476382955289ce427112b59f21c3e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a32b476382955289ce427112b59f21c3e">&#9670;&#160;</a></span>keyStore</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* keyStore</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The file in PEM format containing the public certificate chain of the client. It may also include the client's private key. </p>

</div>
</div>
<a id="a7dd436cbb916fba200595c3519f09ec4" name="a7dd436cbb916fba200595c3519f09ec4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7dd436cbb916fba200595c3519f09ec4">&#9670;&#160;</a></span>privateKey</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* privateKey</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If not included in the sslKeyStore, this setting points to the file in PEM format containing the client's private key. </p>

</div>
</div>
<a id="abb427571ba37b51f6985f1a6906ca031" name="abb427571ba37b51f6985f1a6906ca031"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abb427571ba37b51f6985f1a6906ca031">&#9670;&#160;</a></span>privateKeyPassword</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* privateKeyPassword</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The password to load the client's privateKey if encrypted. </p>

</div>
</div>
<a id="aa683926d52134077f27d6dc67bda13ab" name="aa683926d52134077f27d6dc67bda13ab"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa683926d52134077f27d6dc67bda13ab">&#9670;&#160;</a></span>enabledCipherSuites</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* enabledCipherSuites</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The list of cipher suites that the client will present to the server during the SSL handshake. For a full explanation of the cipher list format, please see the OpenSSL on-line documentation: <a href="http://www.openssl.org/docs/apps/ciphers.html#CIPHER_LIST_FORMAT">http://www.openssl.org/docs/apps/ciphers.html#CIPHER_LIST_FORMAT</a> If this setting is ommitted, its default value will be "ALL", that is, all the cipher suites -excluding those offering no encryption- will be considered. This setting can be used to set an SSL anonymous connection ("aNULL" string value, for instance). </p>

</div>
</div>
<a id="a75f6c13b7634e15f96dd9f17db6cf0be" name="a75f6c13b7634e15f96dd9f17db6cf0be"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a75f6c13b7634e15f96dd9f17db6cf0be">&#9670;&#160;</a></span>enableServerCertAuth</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int enableServerCertAuth</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>True/False option to enable verification of the server certificate </p>

</div>
</div>
<a id="a3543ea1481b68d73cdde833280bb9c45" name="a3543ea1481b68d73cdde833280bb9c45"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3543ea1481b68d73cdde833280bb9c45">&#9670;&#160;</a></span>sslVersion</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int sslVersion</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The SSL/TLS version to use. Specify one of MQTT_SSL_VERSION_DEFAULT (0), MQTT_SSL_VERSION_TLS_1_0 (1), MQTT_SSL_VERSION_TLS_1_1 (2) or MQTT_SSL_VERSION_TLS_1_2 (3). Only used if struct_version is &gt;= 1. </p>

</div>
</div>
<a id="a94900629685d5ed08f66fd2931f573ce" name="a94900629685d5ed08f66fd2931f573ce"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a94900629685d5ed08f66fd2931f573ce">&#9670;&#160;</a></span>verify</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int verify</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Whether to carry out post-connect checks, including that a certificate matches the given host name. Exists only if struct_version &gt;= 2 </p>

</div>
</div>
<a id="a3078b3c824cc9753a57898072445c34d" name="a3078b3c824cc9753a57898072445c34d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3078b3c824cc9753a57898072445c34d">&#9670;&#160;</a></span>CApath</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* CApath</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>From the OpenSSL documentation: If CApath is not NULL, it points to a directory containing CA certificates in PEM format. Exists only if struct_version &gt;= 2 </p>

</div>
</div>
<a id="a21b6ca8a73ba197e65f6a93365d39c04" name="a21b6ca8a73ba197e65f6a93365d39c04"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a21b6ca8a73ba197e65f6a93365d39c04">&#9670;&#160;</a></span>ssl_error_cb</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int(* ssl_error_cb) (const char *str, size_t len, void *u)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Callback function for OpenSSL error handler ERR_print_errors_cb Exists only if struct_version &gt;= 3 </p>

</div>
</div>
<a id="a189f11195f4d5a70024adffdb050885f" name="a189f11195f4d5a70024adffdb050885f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a189f11195f4d5a70024adffdb050885f">&#9670;&#160;</a></span>ssl_error_context</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* ssl_error_context</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Application-specific contex for OpenSSL error handler ERR_print_errors_cb Exists only if struct_version &gt;= 3 </p>

</div>
</div>
<a id="a94317cdaf352f9ae496976f8a30f8fee" name="a94317cdaf352f9ae496976f8a30f8fee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a94317cdaf352f9ae496976f8a30f8fee">&#9670;&#160;</a></span>ssl_psk_cb</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">unsigned int(* ssl_psk_cb) (const char *hint, char *identity, unsigned int max_identity_len, unsigned char *psk, unsigned int max_psk_len, void *u)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Callback function for setting TLS-PSK options. Parameters correspond to that of SSL_CTX_set_psk_client_callback, except for u which is the pointer ssl_psk_context. Exists only if struct_version &gt;= 4 </p>

</div>
</div>
<a id="ab7f597518dd5b9db5a515081f8e0bd1f" name="ab7f597518dd5b9db5a515081f8e0bd1f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab7f597518dd5b9db5a515081f8e0bd1f">&#9670;&#160;</a></span>ssl_psk_context</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* ssl_psk_context</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Application-specific contex for ssl_psk_cb Exists only if struct_version &gt;= 4 </p>

</div>
</div>
<a id="a0826fcae7c2816e04772c61542c6846b" name="a0826fcae7c2816e04772c61542c6846b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0826fcae7c2816e04772c61542c6846b">&#9670;&#160;</a></span>disableDefaultTrustStore</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int disableDefaultTrustStore</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Don't load default SSL CA. Should be used together with PSK to make sure regular servers with certificate in place is not accepted. Exists only if struct_version &gt;= 4 </p>

</div>
</div>
<a id="a4f8661600fb8bacf031150f8dcd293a5" name="a4f8661600fb8bacf031150f8dcd293a5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4f8661600fb8bacf031150f8dcd293a5">&#9670;&#160;</a></span>protos</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const unsigned char* protos</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The protocol-lists must be in wire-format, which is defined as a vector of non-empty, 8-bit length-prefixed, byte strings. The length-prefix byte is not included in the length. Each string is limited to 255 bytes. A byte-string length of 0 is invalid. A truncated byte-string is invalid. Check documentation for SSL_CTX_set_alpn_protos Exists only if struct_version &gt;= 5 </p>

</div>
</div>
<a id="a26f5d839c92f9772c2a5d05486277a42" name="a26f5d839c92f9772c2a5d05486277a42"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a26f5d839c92f9772c2a5d05486277a42">&#9670;&#160;</a></span>protos_len</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">unsigned int protos_len</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The length of the vector protos vector Exists only if struct_version &gt;= 5 </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_m_q_t_t_client_8h_source.html">MQTTClient.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:06 for Paho MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
