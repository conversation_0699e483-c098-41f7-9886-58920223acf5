<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho MQTT C Client Library: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all functions, variables, defines, enums, and typedefs with links to the files they belong to:</div>

<h3><a id="index_m" name="index_m"></a>- m -</h3><ul>
<li>MQTT_BAD_SUBSCRIBE&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#ade337b363b7f4bc7c1a7b2858e0380bd">MQTTClient.h</a></li>
<li>MQTT_INVALID_PROPERTY_ID&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#afc56d2e8937a0c8f180d68ad93945945">MQTTProperties.h</a></li>
<li>MQTT_SSL_VERSION_DEFAULT&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a2549ea897af26c76198284731db9e721">MQTTClient.h</a></li>
<li>MQTT_SSL_VERSION_TLS_1_0&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a7e5da3d6f0d2b53409bbfcf6e56f3d2d">MQTTClient.h</a></li>
<li>MQTT_SSL_VERSION_TLS_1_1&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#abdff87efa3f2ee473a1591e10638b537">MQTTClient.h</a></li>
<li>MQTT_SSL_VERSION_TLS_1_2&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a3a94dbdeafbb73c73a068e7c2085fbab">MQTTClient.h</a></li>
<li>MQTTClient&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a7649e3913f9a216424d296f88a969c59">MQTTClient.h</a></li>
<li>MQTTCLIENT_0_LEN_WILL_TOPIC&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aacf90ba5292e25122e6fd5ec2a38efe5">MQTTClient.h</a></li>
<li>MQTTCLIENT_BAD_MQTT_OPTION&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a1babaca56ffae802fa1e246a2649927e">MQTTClient.h</a></li>
<li>MQTTCLIENT_BAD_MQTT_VERSION&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aab84cecd25638896eb45b8f5ffd82bf7">MQTTClient.h</a></li>
<li>MQTTCLIENT_BAD_PROTOCOL&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a1d0cb25b450136f036a238546487344a">MQTTClient.h</a></li>
<li>MQTTCLIENT_BAD_QOS&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a51cc8ca032acf4ae14f83996524b8cdc">MQTTClient.h</a></li>
<li>MQTTCLIENT_BAD_STRUCTURE&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a747615d8064e3fe024ae5565ec63e1ce">MQTTClient.h</a></li>
<li>MQTTCLIENT_BAD_UTF8_STRING&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a678a4744192de9c8dca220d9965809dd">MQTTClient.h</a></li>
<li>MQTTClient_connect()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aaa8ae61cd65c9dc0846df10122d7bd4e">MQTTClient.h</a></li>
<li>MQTTClient_connect5()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aa777f80cb3eec5610f976aff30b8c0d6">MQTTClient.h</a></li>
<li>MQTTClient_connectionLost&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a6bb253f16754e7cc81798c9fda0e36cf">MQTTClient.h</a></li>
<li>MQTTClient_connectOptions_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aefd7c865f2641c8155b763fdf3061c25">MQTTClient.h</a></li>
<li>MQTTClient_connectOptions_initializer5&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a1f0c7608262ac9c00cb94e9c8f9fc984">MQTTClient.h</a></li>
<li>MQTTClient_connectOptions_initializer5_ws&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a8c37c9f77f0b67e2520c8f91acf1afea">MQTTClient.h</a></li>
<li>MQTTClient_connectOptions_initializer_ws&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a0a98fda162a78ee8c8cbd7d9d39494f4">MQTTClient.h</a></li>
<li>MQTTClient_create()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a9a0518d9ca924d12c1329dbe3de5f2b6">MQTTClient.h</a></li>
<li>MQTTClient_createOptions_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a763e477a5aceb6aff279111c7693e691">MQTTClient.h</a></li>
<li>MQTTClient_createWithOptions()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#ade24f717a9b39d38b081e1d5e0db1661">MQTTClient.h</a></li>
<li>MQTTClient_deliveryComplete&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#abef83794d8252551ed248cde6eb845a6">MQTTClient.h</a></li>
<li>MQTTClient_deliveryToken&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a73e49030fd8b7074aa1aa45669b7fe8d">MQTTClient.h</a></li>
<li>MQTTClient_destroy()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#ae700c3f5cfea3813264ce95e7c8cf498">MQTTClient.h</a></li>
<li>MQTTClient_disconnect()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a1e4d90c13a3c0705bc4a13bfe64e6525">MQTTClient.h</a></li>
<li>MQTTClient_disconnect5()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a1762c469715b7f718c4e63a427e6c13c">MQTTClient.h</a></li>
<li>MQTTCLIENT_DISCONNECTED&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a561d053311cb492cf7226f419ee0d516">MQTTClient.h</a></li>
<li>MQTTClient_disconnected&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a41108d4cccb67a9d6884ebae52211c46">MQTTClient.h</a></li>
<li>MQTTCLIENT_FAILURE&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#af33a6d6c0e8a6a747bf39638e0bba36b">MQTTClient.h</a></li>
<li>MQTTClient_free()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a203b545c999beb6b825ec99b6aea79ab">MQTTClient.h</a></li>
<li>MQTTClient_freeMessage()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#abd8abde4f39d3e689029de27f7a98a65">MQTTClient.h</a></li>
<li>MQTTClient_getPendingDeliveryTokens()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a2a617c6b0492c04a4ddea592f5e53604">MQTTClient.h</a></li>
<li>MQTTClient_getVersionInfo()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aef6eba956a5ff6072854a9e353487087">MQTTClient.h</a></li>
<li>MQTTClient_global_init()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a21804ede1a506d1d69a472bc30acc8ba">MQTTClient.h</a></li>
<li>MQTTClient_init_options_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#ac17057c8c22c0717d3adf4e040440f73">MQTTClient.h</a></li>
<li>MQTTClient_isConnected()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a6e8231e8c47f6f67f7ebbb5dcb4c69c0">MQTTClient.h</a></li>
<li>MQTTClient_malloc()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a1f3ae01af021b014df147c9996156a69">MQTTClient.h</a></li>
<li>MQTTCLIENT_MAX_MESSAGES_INFLIGHT&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a8fc442fc2e9dfb422a163ab1fa02e0cb">MQTTClient.h</a></li>
<li>MQTTClient_message_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aa1fd995924d3df75959fcf57e87aefac">MQTTClient.h</a></li>
<li>MQTTClient_messageArrived&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aa42130dd069e7e949bcab37b6dce64a5">MQTTClient.h</a></li>
<li>MQTTCLIENT_NULL_PARAMETER&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#ac3232abd7f86bbba26faea0e2b132c3c">MQTTClient.h</a></li>
<li>MQTTCLIENT_PERSISTENCE_DEFAULT&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#aaa948291718a9c06369b854b0f64bc32">MQTTClientPersistence.h</a></li>
<li>MQTTCLIENT_PERSISTENCE_ERROR&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#ab716e21e53c84a5ad62aa962a2a8f7db">MQTTClientPersistence.h</a></li>
<li>MQTTCLIENT_PERSISTENCE_NONE&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#ae01e089313a65ac4661ed216b6ac00fa">MQTTClientPersistence.h</a></li>
<li>MQTTCLIENT_PERSISTENCE_USER&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#a5dc68b8616e4041e037bad94ce07681b">MQTTClientPersistence.h</a></li>
<li>MQTTClient_publish()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#afe9c34013c3511b8ef6cd36bf703678d">MQTTClient.h</a></li>
<li>MQTTClient_publish5()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a8148186cc7683a6bb57f621653df51df">MQTTClient.h</a></li>
<li>MQTTClient_published&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a6c3f51e50e2c47328eee1b0c920ed103">MQTTClient.h</a></li>
<li>MQTTClient_publishMessage()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#ace320b8a92c7087d9dd5cf242d50389d">MQTTClient.h</a></li>
<li>MQTTClient_publishMessage5()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a362042ce973c012bad6a1aa3b5984f5d">MQTTClient.h</a></li>
<li>MQTTClient_receive()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a4c2df88d00a3dadd510a8cb774739366">MQTTClient.h</a></li>
<li>MQTTClient_setCallbacks()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aad27d07782991a4937ebf2f39a021f83">MQTTClient.h</a></li>
<li>MQTTClient_setCommandTimeout()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a96067a2fb74d2a61c7e93015629548e0">MQTTClient.h</a></li>
<li>MQTTClient_setDisconnected()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a8adea083a162735d5c7592160088eea0">MQTTClient.h</a></li>
<li>MQTTClient_setPublished()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a9f13911351a3de6b1ebdabd4cb4116ba">MQTTClient.h</a></li>
<li>MQTTClient_setTraceCallback()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a22870f94aa4cb1827626612f1ded7c69">MQTTClient.h</a></li>
<li>MQTTClient_setTraceLevel()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a4dfa35d29db54b10b15b8ac2d9a778be">MQTTClient.h</a></li>
<li>MQTTCLIENT_SSL_NOT_SUPPORTED&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a1c67fc83ba1a8f26236aa49b127bdb61">MQTTClient.h</a></li>
<li>MQTTClient_SSLOptions_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#ab9b2a2c6b52dbb2ac842ad99a9ce6d99">MQTTClient.h</a></li>
<li>MQTTClient_strerror()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a68535b4c6d8f28b29a52569926cdeb50">MQTTClient.h</a></li>
<li>MQTTClient_subscribe()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a9c1c28258f0d5c6a44ff53a98618f5f3">MQTTClient.h</a></li>
<li>MQTTClient_subscribe5()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#af35ab7375435f7b6388c5ff4610dad3d">MQTTClient.h</a></li>
<li>MQTTClient_subscribeMany()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a92fa1c13f3db8399e042fbdbdfb692b3">MQTTClient.h</a></li>
<li>MQTTClient_subscribeMany5()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a5390c2402f135c12826ffbf6fc261f7c">MQTTClient.h</a></li>
<li>MQTTCLIENT_SUCCESS&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#acba095704d79e5a1996389fa26203f73">MQTTClient.h</a></li>
<li>MQTTClient_token&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a8b2beb5227708f8127b666f5a7fc41b3">MQTTClient.h</a></li>
<li>MQTTCLIENT_TOPICNAME_TRUNCATED&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a29afebfce0bdf6cda1e37abc0c4b6690">MQTTClient.h</a></li>
<li>MQTTCLIENT_TRACE_ERROR&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba6eefffc98c1ba698224ba351f12e6a91">MQTTClient.h</a></li>
<li>MQTTCLIENT_TRACE_FATAL&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba35626cc4876d074c4c21f8c4f54fdf38">MQTTClient.h</a></li>
<li>MQTTCLIENT_TRACE_LEVELS&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09b">MQTTClient.h</a></li>
<li>MQTTCLIENT_TRACE_MAXIMUM&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba38a4c3c4e2fc99711793ee2815aee40c">MQTTClient.h</a></li>
<li>MQTTCLIENT_TRACE_MEDIUM&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba4bb7e7221b59e9be4515f2182c03ea99">MQTTClient.h</a></li>
<li>MQTTCLIENT_TRACE_MINIMUM&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09bacf029d9a231bd07e5e1a6f3bd0b6004e">MQTTClient.h</a></li>
<li>MQTTCLIENT_TRACE_PROTOCOL&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09ba29f21f77cf34ab2467520d7738fd8eb1">MQTTClient.h</a></li>
<li>MQTTCLIENT_TRACE_SEVERE&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aa0ae95caa9c16d152b5036b1bac2e09baf060569bdbb4015cfce028937b4cfa69">MQTTClient.h</a></li>
<li>MQTTClient_traceCallback&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#afa5758290a1162e5135bca97bbfd5774">MQTTClient.h</a></li>
<li>MQTTClient_unsubscribe()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aa8731be3dbc6a25f41f037f8bbbb054b">MQTTClient.h</a></li>
<li>MQTTClient_unsubscribe5()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a58356c13867f18df60fd4c7ec9457c48">MQTTClient.h</a></li>
<li>MQTTClient_unsubscribeMany()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a50abbce720d50b9f84b97ff9fa1f546d">MQTTClient.h</a></li>
<li>MQTTClient_unsubscribeMany5()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a46bdb532d2153110ccffb2f0748d1ba5">MQTTClient.h</a></li>
<li>MQTTClient_waitForCompletion()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a83807ec81fe8c3941e368ab329d43067">MQTTClient.h</a></li>
<li>MQTTClient_willOptions_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#aae0811659c59f5dad0467544f91645eb">MQTTClient.h</a></li>
<li>MQTTCLIENT_WRONG_MQTT_VERSION&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#ae9070d21de569f999a9575049cdd6da1">MQTTClient.h</a></li>
<li>MQTTClient_yield()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a8ad3d29864a9ca08202b0832e0f6678e">MQTTClient.h</a></li>
<li>MQTTPersistence_afterRead&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#af5a966a574c6ad7a35f1ebb7edd5c1c4">MQTTClientPersistence.h</a></li>
<li>MQTTPersistence_beforeWrite&#160;:&#160;<a class="el" href="_m_q_t_t_client_persistence_8h.html#ab865640a1cc53b68622004c5a2d29fae">MQTTClientPersistence.h</a></li>
<li>MQTTProperties&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a7758f1a5eceb6f46c8540630e39e2fb4">MQTTProperties.h</a></li>
<li>MQTTProperties_add()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a88f1d21556c2d23330d71357cd226a15">MQTTProperties.h</a></li>
<li>MQTTProperties_copy()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a69b3e474ee2f828e5b827d615fe0fe72">MQTTProperties.h</a></li>
<li>MQTTProperties_free()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#ab68247ed365ee51170a9309c828b1823">MQTTProperties.h</a></li>
<li>MQTTProperties_getNumericValue()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#ad8643f0e68deb29e16ef88fc225e03c2">MQTTProperties.h</a></li>
<li>MQTTProperties_getNumericValueAt()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a1d4f3fd86fc47241fefe623556d99ea9">MQTTProperties.h</a></li>
<li>MQTTProperties_getProperty()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#ac5c49930de80af1e0df0f0584f142078">MQTTProperties.h</a></li>
<li>MQTTProperties_getPropertyAt()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a345b64fc33afd05148aa018c24c42c80">MQTTProperties.h</a></li>
<li>MQTTProperties_hasProperty()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#abde30a2a44f41c649bd84f4d1467b72c">MQTTProperties.h</a></li>
<li>MQTTProperties_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a5a80e158486a414ccdfcdd7f75f23988">MQTTProperties.h</a></li>
<li>MQTTProperties_len()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a2850f38d4ff89af52999fdc42cdff6fa">MQTTProperties.h</a></li>
<li>MQTTProperties_propertyCount()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#ac7ea96a57ad09e9d0fc3203d008f52aa">MQTTProperties.h</a></li>
<li>MQTTProperties_read()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#afcb874dfcc9f0eaa0b063e2fad740871">MQTTProperties.h</a></li>
<li>MQTTProperties_write()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#ade0027a4e571bd288fe40271ff7aa497">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_ASSIGNED_CLIENT_IDENTIFER&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a768d84858fd18d5d5a7dee394929c672">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_ASSIGNED_CLIENT_IDENTIFIER&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab5153f683f5a25f6e3a9e3aeb37f1fd6">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_AUTHENTICATION_DATA&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4abdf9feec165aceefbe7aa46764f6ab6e">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_AUTHENTICATION_METHOD&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a7c53f1e414b577d787b5d51af3204100">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_CONTENT_TYPE&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a4027d9e0fb53a62ae35963e700b56198">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_CORRELATION_DATA&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a887d3dd3f0ce31255324f5a1ba8b72c5">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_MAXIMUM_PACKET_SIZE&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a6834ea9878f028d5fbdeccaaeae492e5">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_MAXIMUM_QOS&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a506faeb89c407cf78853c777d750fa59">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_MESSAGE_EXPIRY_INTERVAL&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a284c0e62d47ee8d358b16a8075632b4a">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_PAYLOAD_FORMAT_INDICATOR&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ae5d077520427d03b44096f631411575d">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_REASON_STRING&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a3dce8f679474e901ce4aec076e9e59e1">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_RECEIVE_MAXIMUM&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab2688fe8d7d263c27c00d41776cb8f9f">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_REQUEST_PROBLEM_INFORMATION&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a3954daf1d5772b5d56eefa1ab6a28aa1">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_REQUEST_RESPONSE_INFORMATION&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a420b882a337dc1fd5f336ac6cd0529bf">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_RESPONSE_INFORMATION&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a2584b050f016af496c7f0b46692dbc00">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_RESPONSE_TOPIC&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a7fa9996eef721d318504fbb0a8d4bac5">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_RETAIN_AVAILABLE&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a448b3a40afaa5f7195701e7dc8bed30c">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_SERVER_KEEP_ALIVE&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab106f320e7537b79644f25d3efcd68c7">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_SERVER_REFERENCE&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a0168e8a59f7994c02b7a7fd2fc3735c4">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_SESSION_EXPIRY_INTERVAL&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a22e4caa63f63ca3f9b1c1330711ee766">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_SHARED_SUBSCRIPTION_AVAILABLE&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ae04a7356f9e11654f15a3b21f2aae636">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIER&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a70ead9c93f06396a4d9469b65bff0c96">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIERS_AVAILABLE&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a8b366cfd8bd3f388bafb67f3ebf83505">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_TOPIC_ALIAS&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ad4dfb37d341ea190afc144668e5e3bee">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_TOPIC_ALIAS_MAXIMUM&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a0a0b0b0715ecc9ccf471c75aa4c21c23">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_USER_PROPERTY&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a596ff540370235d3eca693ce30dd4af8">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_WILDCARD_SUBSCRIPTION_AVAILABLE&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ad05993f90baaee0ba7094ccef4d378b9">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_CODE_WILL_DELAY_INTERVAL&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a53fd81bc554f152a2772d282be7ce5ef">MQTTProperties.h</a></li>
<li>MQTTProperty_getType()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a7d30ad0520bc9b9366e700d4b493b173">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_TYPE_BINARY_DATA&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a6643aed682b9b07f98159856776fe7b4">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_TYPE_BYTE&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ac36f96ce58c98a8ebbe0783df030726a">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_TYPE_FOUR_BYTE_INTEGER&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958aa49c558733bd735ae872fd87ad0d7e15">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_TYPE_TWO_BYTE_INTEGER&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ae301a9e68326cc2d8bfefeca401e78e6">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_TYPE_UTF_8_ENCODED_STRING&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ad45c866a5bef6c5048a7af21405734d1">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_TYPE_UTF_8_STRING_PAIR&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a28ab5fe5b159f3b3a8884b0f61527214">MQTTProperties.h</a></li>
<li>MQTTPROPERTY_TYPE_VARIABLE_BYTE_INTEGER&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a27bbcb5bc4f584f96612c0cec329c6a7">MQTTProperties.h</a></li>
<li>MQTTPropertyCodes&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTProperties.h</a></li>
<li>MQTTPropertyName()&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a69d277e6a0f27a05279eca2736e09840">MQTTProperties.h</a></li>
<li>MQTTPropertyTypes&#160;:&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958">MQTTProperties.h</a></li>
<li>MQTTREASONCODE_ADMINISTRATIVE_ACTION&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ae1e3b428072be26d2cbf6f88361f76cc">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_BAD_AUTHENTICATION_METHOD&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af62e569703d7a7f0acffaa59522b9dc3">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_BAD_USER_NAME_OR_PASSWORD&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279abfc617112d5856722108912c5c6633ff">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_BANNED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ab4cf7578f0078293fa66a4cd5e5d4aa4">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_CLIENT_IDENTIFIER_NOT_VALID&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ab58bb236e7dbd000a56c590c01bc73fd">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_CONNECTION_RATE_EXCEEDED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a879c56ed34fa2dd6492e7a34a9747bc1">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_CONTINUE_AUTHENTICATION&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a0c0726c0e87eaddd636708497c69d055">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_DISCONNECT_WITH_WILL_MESSAGE&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a55f533a6cc98417d08dac8cc69da0ed3">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_GRANTED_QOS_0&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a3fd0d12c0e44b4df9f716aef89b61aff">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_GRANTED_QOS_1&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a07578b30b2d72af2eeea6be268475876">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_GRANTED_QOS_2&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a74ac34a39a849c9c369b18545a4b1f93">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_IMPLEMENTATION_SPECIFIC_ERROR&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a41629fa453cdf14ef6a5370a16d5a19c">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_KEEP_ALIVE_TIMEOUT&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af21a6c320e34993d7aa169330ab23409">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_MALFORMED_PACKET&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a2cbee3502c00d304bf1091195457fcf5">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_MAXIMUM_CONNECT_TIME&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6f07c3b42690afc7b117321dc4e2657f">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_MESSAGE_RATE_TOO_HIGH&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af76d0e32fb44fa94e407b1af5dc7aa4e">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_NO_MATCHING_SUBSCRIBERS&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1720d8b04af4c0d92e27b378d735e899">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_NO_SUBSCRIPTION_FOUND&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a55208c34a26f67e112d53c54be37acb9">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_NORMAL_DISCONNECTION&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a3590f41d984646bc58c82734c1516c92">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_NOT_AUTHORIZED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a91a14fc763349cf4a7047d24f13d0803">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_PACKET_IDENTIFIER_IN_USE&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279adaee01dbc97a0773b5032a29c797613a">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_PACKET_IDENTIFIER_NOT_FOUND&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a4908a8293054f8ff8d6c47fe0cf31932">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_PACKET_TOO_LARGE&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a11a587e15c468bf1c6ba9df7e8fd78aa">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_PAYLOAD_FORMAT_INVALID&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a2d629400116e1723c5e2e597bbfe29ca">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_PROTOCOL_ERROR&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ae0dad403f352e31449764e2ac94c7756">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_QOS_NOT_SUPPORTED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a83865a2440b512e5602152521e3810bb">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_QUOTA_EXCEEDED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a954fcabf6e88925b2a57bcd84032d9f9">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_RE_AUTHENTICATE&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6cc1b342856c1d96d54c368148b536f7">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_RECEIVE_MAXIMUM_EXCEEDED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a45afaacbefd2d816fddf9fe9804b61d1">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_RETAIN_NOT_SUPPORTED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279aa4378012148d98599398bc4a3480c38f">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_SERVER_BUSY&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af507e75147b0b34f36955c9f62389a74">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_SERVER_MOVED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a783254c7acf8de52ee345bc176f9d6c0">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_SERVER_SHUTTING_DOWN&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a085e1572ffce61838807b7429b691113">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_SERVER_UNAVAILABLE&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a0cfd4de78870b3fb0499b916d06d40bb">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_SESSION_TAKEN_OVER&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ad15ffa6884f97976e237afafcbccea21">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_SHARED_SUBSCRIPTIONS_NOT_SUPPORTED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1c694648e36a40162939a2785450b6bd">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_SUBSCRIPTION_IDENTIFIERS_NOT_SUPPORTED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a7bcd0f9b21c398a217667aebb4107842">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_SUCCESS&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_TOPIC_ALIAS_INVALID&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a8e0fcdd051e154e319058600b58652ec">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_TOPIC_FILTER_INVALID&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a00319b171f469824dd6938cbd0212b5b">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_TOPIC_NAME_INVALID&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6268968177868576f6b9239aa9afd8ac">MQTTReasonCodes.h</a></li>
<li>MQTTReasonCode_toString()&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#a385737b840eb180f35b9a714ea295ceb">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_UNSPECIFIED_ERROR&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1881ee597bfef9157f0034a1377328e3">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_UNSUPPORTED_PROTOCOL_VERSION&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a021ceca20e6d35279075a2b93ece973d">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_USE_ANOTHER_SERVER&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279aabaee4062c4e4941b9eed59f09e9440c">MQTTReasonCodes.h</a></li>
<li>MQTTREASONCODE_WILDCARD_SUBSCRIPTIONS_NOT_SUPPORTED&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a81b5708f676f52594b680f085e444e1f">MQTTReasonCodes.h</a></li>
<li>MQTTReasonCodes&#160;:&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes.h</a></li>
<li>MQTTResponse&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a0d31d490adbe677902b99eca127bee56">MQTTClient.h</a></li>
<li>MQTTResponse_free()&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a01bd2c5f98ec5c0636a106db33f2b01b">MQTTClient.h</a></li>
<li>MQTTResponse_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a17f171200136bcfa933eb50ef21531a7">MQTTClient.h</a></li>
<li>MQTTSubscribe_options&#160;:&#160;<a class="el" href="_m_q_t_t_subscribe_opts_8h.html#aa68db3eaed272ae1aaea294401079d8a">MQTTSubscribeOpts.h</a></li>
<li>MQTTSubscribe_options_initializer&#160;:&#160;<a class="el" href="_m_q_t_t_subscribe_opts_8h.html#aec3b45fd0367106eea344396f87cfda7">MQTTSubscribeOpts.h</a></li>
<li>MQTTVERSION_3_1&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a4603b988e76872e1f23f135d225ce2fb">MQTTClient.h</a></li>
<li>MQTTVERSION_3_1_1&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#ac79cc6fdeaa9e3f4ee12c3418898b1ef">MQTTClient.h</a></li>
<li>MQTTVERSION_5&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#af8b176fa4d5b89789767ce972338e1e3">MQTTClient.h</a></li>
<li>MQTTVERSION_DEFAULT&#160;:&#160;<a class="el" href="_m_q_t_t_client_8h.html#a75b80b01f98d5a1ffa2a4d42995a8397">MQTTClient.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:07 for Paho MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
