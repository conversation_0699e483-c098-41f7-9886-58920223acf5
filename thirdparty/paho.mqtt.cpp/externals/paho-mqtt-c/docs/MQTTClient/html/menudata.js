/*
 @licstart  The following is the entire license notice for the JavaScript code in this file.

 The MIT License (MIT)

 Copyright (C) 1997-2020 by <PERSON>

 Permission is hereby granted, free of charge, to any person obtaining a copy of this software
 and associated documentation files (the "Software"), to deal in the Software without restriction,
 including without limitation the rights to use, copy, modify, merge, publish, distribute,
 sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in all copies or
 substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING
 BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
 DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

 @licend  The above is the entire license notice for the JavaScript code in this file
*/
var menudata={children:[
{text:"Main Page",url:"index.html"},
{text:"Related Pages",url:"pages.html"},
{text:"Data Structures",url:"annotated.html",children:[
{text:"Data Structures",url:"annotated.html"},
{text:"Data Fields",url:"functions.html",children:[
{text:"All",url:"functions.html",children:[
{text:"a",url:"functions.html#index_a"},
{text:"b",url:"functions.html#index_b"},
{text:"c",url:"functions.html#index_c"},
{text:"d",url:"functions.html#index_d"},
{text:"e",url:"functions.html#index_e"},
{text:"h",url:"functions.html#index_h"},
{text:"i",url:"functions.html#index_i"},
{text:"k",url:"functions.html#index_k"},
{text:"l",url:"functions.html#index_l"},
{text:"m",url:"functions.html#index_m"},
{text:"n",url:"functions.html#index_n"},
{text:"p",url:"functions.html#index_p"},
{text:"q",url:"functions.html#index_q"},
{text:"r",url:"functions.html#index_r"},
{text:"s",url:"functions.html#index_s"},
{text:"t",url:"functions.html#index_t"},
{text:"u",url:"functions.html#index_u"},
{text:"v",url:"functions.html#index_v"},
{text:"w",url:"functions.html#index_w"}]},
{text:"Variables",url:"functions_vars.html",children:[
{text:"a",url:"functions_vars.html#index_a"},
{text:"b",url:"functions_vars.html#index_b"},
{text:"c",url:"functions_vars.html#index_c"},
{text:"d",url:"functions_vars.html#index_d"},
{text:"e",url:"functions_vars.html#index_e"},
{text:"h",url:"functions_vars.html#index_h"},
{text:"i",url:"functions_vars.html#index_i"},
{text:"k",url:"functions_vars.html#index_k"},
{text:"l",url:"functions_vars.html#index_l"},
{text:"m",url:"functions_vars.html#index_m"},
{text:"n",url:"functions_vars.html#index_n"},
{text:"p",url:"functions_vars.html#index_p"},
{text:"q",url:"functions_vars.html#index_q"},
{text:"r",url:"functions_vars.html#index_r"},
{text:"s",url:"functions_vars.html#index_s"},
{text:"t",url:"functions_vars.html#index_t"},
{text:"u",url:"functions_vars.html#index_u"},
{text:"v",url:"functions_vars.html#index_v"},
{text:"w",url:"functions_vars.html#index_w"}]}]}]},
{text:"Files",url:"files.html",children:[
{text:"File List",url:"files.html"},
{text:"Globals",url:"globals.html",children:[
{text:"All",url:"globals.html",children:[
{text:"m",url:"globals.html#index_m"},
{text:"p",url:"globals_p.html#index_p"}]},
{text:"Functions",url:"globals_func.html",children:[
{text:"m",url:"globals_func.html#index_m"}]},
{text:"Typedefs",url:"globals_type.html"},
{text:"Enumerations",url:"globals_enum.html"},
{text:"Enumerator",url:"globals_eval.html",children:[
{text:"m",url:"globals_eval.html#index_m"}]},
{text:"Macros",url:"globals_defs.html",children:[
{text:"m",url:"globals_defs.html#index_m"}]}]}]}]}
