<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho MQTT C Client Library: MQTTProperties Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">MQTTProperties Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="_m_q_t_t_properties_8h_source.html">MQTTProperties.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:ad43c3812e6d13e0518d9f8b8f463ffcf" id="r_ad43c3812e6d13e0518d9f8b8f463ffcf"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad43c3812e6d13e0518d9f8b8f463ffcf">count</a></td></tr>
<tr class="separator:ad43c3812e6d13e0518d9f8b8f463ffcf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8de324382d8fd2f5939bf3372e059383" id="r_a8de324382d8fd2f5939bf3372e059383"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8de324382d8fd2f5939bf3372e059383">max_count</a></td></tr>
<tr class="separator:a8de324382d8fd2f5939bf3372e059383"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9f59b34b1f25fe00023291b678246bcc" id="r_a9f59b34b1f25fe00023291b678246bcc"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9f59b34b1f25fe00023291b678246bcc">length</a></td></tr>
<tr class="separator:a9f59b34b1f25fe00023291b678246bcc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3ac4c38b423393c1553dcf8b71e7dd58" id="r_a3ac4c38b423393c1553dcf8b71e7dd58"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_property.html">MQTTProperty</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3ac4c38b423393c1553dcf8b71e7dd58">array</a></td></tr>
<tr class="separator:a3ac4c38b423393c1553dcf8b71e7dd58"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>MQTT version 5 property list </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="ad43c3812e6d13e0518d9f8b8f463ffcf" name="ad43c3812e6d13e0518d9f8b8f463ffcf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad43c3812e6d13e0518d9f8b8f463ffcf">&#9670;&#160;</a></span>count</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int count</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>number of property entries in the array </p>

</div>
</div>
<a id="a8de324382d8fd2f5939bf3372e059383" name="a8de324382d8fd2f5939bf3372e059383"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8de324382d8fd2f5939bf3372e059383">&#9670;&#160;</a></span>max_count</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int max_count</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>max number of properties that the currently allocated array can store </p>

</div>
</div>
<a id="a9f59b34b1f25fe00023291b678246bcc" name="a9f59b34b1f25fe00023291b678246bcc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9f59b34b1f25fe00023291b678246bcc">&#9670;&#160;</a></span>length</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int length</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>mbi: byte length of all properties </p>

</div>
</div>
<a id="a3ac4c38b423393c1553dcf8b71e7dd58" name="a3ac4c38b423393c1553dcf8b71e7dd58"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3ac4c38b423393c1553dcf8b71e7dd58">&#9670;&#160;</a></span>array</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_property.html">MQTTProperty</a>* array</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>array of properties </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_m_q_t_t_properties_8h_source.html">MQTTProperties.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:06 for Paho MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
