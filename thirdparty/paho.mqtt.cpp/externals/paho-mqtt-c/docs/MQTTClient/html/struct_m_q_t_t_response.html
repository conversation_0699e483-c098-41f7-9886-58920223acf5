<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho MQTT C Client Library: MQTTResponse Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">MQTTResponse Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="_m_q_t_t_client_8h_source.html">MQTTClient.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:aad880fc4455c253781e8968f2239d56f" id="r_aad880fc4455c253781e8968f2239d56f"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aad880fc4455c253781e8968f2239d56f">version</a></td></tr>
<tr class="separator:aad880fc4455c253781e8968f2239d56f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a580d8a8ecb285f5a86c2a3865438f8ee" id="r_a580d8a8ecb285f5a86c2a3865438f8ee"><td class="memItemLeft" align="right" valign="top">enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a580d8a8ecb285f5a86c2a3865438f8ee">reasonCode</a></td></tr>
<tr class="separator:a580d8a8ecb285f5a86c2a3865438f8ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac97316626bd4faa6b71277c221275f4b" id="r_ac97316626bd4faa6b71277c221275f4b"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac97316626bd4faa6b71277c221275f4b">reasonCodeCount</a></td></tr>
<tr class="separator:ac97316626bd4faa6b71277c221275f4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2199c9d905dbfa279895cf8123c10f4f" id="r_a2199c9d905dbfa279895cf8123c10f4f"><td class="memItemLeft" align="right" valign="top">enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2199c9d905dbfa279895cf8123c10f4f">reasonCodes</a></td></tr>
<tr class="separator:a2199c9d905dbfa279895cf8123c10f4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a72e9294467b8329a78bc840fe6c5b230" id="r_a72e9294467b8329a78bc840fe6c5b230"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a72e9294467b8329a78bc840fe6c5b230">properties</a></td></tr>
<tr class="separator:a72e9294467b8329a78bc840fe6c5b230"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>MQTT version 5.0 response information </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="aad880fc4455c253781e8968f2239d56f" name="aad880fc4455c253781e8968f2239d56f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aad880fc4455c253781e8968f2239d56f">&#9670;&#160;</a></span>version</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int version</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a580d8a8ecb285f5a86c2a3865438f8ee" name="a580d8a8ecb285f5a86c2a3865438f8ee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a580d8a8ecb285f5a86c2a3865438f8ee">&#9670;&#160;</a></span>reasonCode</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reasonCode</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ac97316626bd4faa6b71277c221275f4b" name="ac97316626bd4faa6b71277c221275f4b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac97316626bd4faa6b71277c221275f4b">&#9670;&#160;</a></span>reasonCodeCount</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int reasonCodeCount</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2199c9d905dbfa279895cf8123c10f4f" name="a2199c9d905dbfa279895cf8123c10f4f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2199c9d905dbfa279895cf8123c10f4f">&#9670;&#160;</a></span>reasonCodes</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a>* reasonCodes</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a72e9294467b8329a78bc840fe6c5b230" name="a72e9294467b8329a78bc840fe6c5b230"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a72e9294467b8329a78bc840fe6c5b230">&#9670;&#160;</a></span>properties</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* properties</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_m_q_t_t_client_8h_source.html">MQTTClient.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:06 for Paho MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
