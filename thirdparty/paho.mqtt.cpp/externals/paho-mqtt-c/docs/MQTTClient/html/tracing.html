<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.12.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho MQTT C Client Library: Tracing</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign">
   <div id="projectname">Paho MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.12.0 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',false);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div id="doc-content">
<div><div class="header">
  <div class="headertitle"><div class="title">Tracing</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>Runtime tracing is controlled by environment variables.</p>
<p>Tracing is switched on by setting MQTT_C_CLIENT_TRACE. A value of ON, or stdout, prints to stdout, any other value is interpreted as a file name to use.</p>
<p>The amount of trace detail is controlled with the MQTT_C_CLIENT_TRACE_LEVEL environment variable - valid values are ERROR, PROTOCOL, MINIMUM, MEDIUM and MAXIMUM (from least to most verbose).</p>
<p>The variable MQTT_C_CLIENT_TRACE_MAX_LINES limits the number of lines of trace that are output to a file. Two files are used at most, when they are full, the last one is overwritten with the new trace entries. The default size is 1000 lines.</p>
<h3>MQTT Packet Tracing</h3>
<p>A feature that can be very useful is printing the MQTT packets that are sent and received. To achieve this, use the following environment variable settings: </p><div class="fragment"><div class="line">MQTT_C_CLIENT_TRACE=ON</div>
<div class="line">MQTT_C_CLIENT_TRACE_LEVEL=PROTOCOL</div>
</div><!-- fragment --><p> The output you should see looks like this: </p><div class="fragment"><div class="line">20130528 155936.813 3 stdout-subscriber -&gt; CONNECT cleansession: 1 (0)</div>
<div class="line">20130528 155936.813 3 stdout-subscriber &lt;- CONNACK rc: 0</div>
<div class="line">20130528 155936.813 3 stdout-subscriber -&gt; SUBSCRIBE msgid: 1 (0)</div>
<div class="line">20130528 155936.813 3 stdout-subscriber &lt;- SUBACK msgid: 1</div>
<div class="line">20130528 155941.818 3 stdout-subscriber -&gt; DISCONNECT (0)</div>
</div><!-- fragment --><p> where the fields are:</p><ol type="1">
<li>date</li>
<li>time</li>
<li>socket number</li>
<li>client id</li>
<li>direction (-&gt; from client to server, &lt;- from server to client)</li>
<li>packet details</li>
</ol>
<h3>Default Level Tracing</h3>
<p>This is an extract of a default level trace of a call to connect: </p><div class="fragment"><div class="line">19700101 010000.000 (1152206656) (0)&gt; <a class="code hl_function" href="_m_q_t_t_client_8h.html#aaa8ae61cd65c9dc0846df10122d7bd4e">MQTTClient_connect</a>:893</div>
<div class="line">19700101 010000.000 (1152206656)  (1)&gt; MQTTClient_connectURI:716</div>
<div class="line">20130528 160447.479 Connecting to serverURI localhost:1883</div>
<div class="line">20130528 160447.479 (1152206656)   (2)&gt; MQTTProtocol_connect:98</div>
<div class="line">20130528 160447.479 (1152206656)    (3)&gt; MQTTProtocol_addressPort:48</div>
<div class="line">20130528 160447.479 (1152206656)    (3)&lt; MQTTProtocol_addressPort:73</div>
<div class="line">20130528 160447.479 (1152206656)    (3)&gt; Socket_new:599</div>
<div class="line">20130528 160447.479 New socket 4 <span class="keywordflow">for</span> localhost, port 1883</div>
<div class="line">20130528 160447.479 (1152206656)     (4)&gt; Socket_addSocket:163</div>
<div class="line">20130528 160447.479 (1152206656)      (5)&gt; Socket_setnonblocking:73</div>
<div class="line">20130528 160447.479 (1152206656)      (5)&lt; Socket_setnonblocking:78 (0)</div>
<div class="line">20130528 160447.479 (1152206656)     (4)&lt; Socket_addSocket:176 (0)</div>
<div class="line">20130528 160447.479 (1152206656)     (4)&gt; Socket_error:95</div>
<div class="line">20130528 160447.479 (1152206656)     (4)&lt; Socket_error:104 (115)</div>
<div class="line">20130528 160447.479 Connect pending</div>
<div class="line">20130528 160447.479 (1152206656)    (3)&lt; Socket_new:683 (115)</div>
<div class="line">20130528 160447.479 (1152206656)   (2)&lt; MQTTProtocol_connect:131 (115)</div>
<div class="ttc" id="a_m_q_t_t_client_8h_html_aaa8ae61cd65c9dc0846df10122d7bd4e"><div class="ttname"><a href="_m_q_t_t_client_8h.html#aaa8ae61cd65c9dc0846df10122d7bd4e">MQTTClient_connect</a></div><div class="ttdeci">int MQTTClient_connect(MQTTClient handle, MQTTClient_connectOptions *options)</div></div>
</div><!-- fragment --><p> where the fields are:</p><ol type="1">
<li>date</li>
<li>time</li>
<li>thread id</li>
<li>function nesting level</li>
<li>function entry (&gt;) or exit (&lt;)</li>
<li>function name : line of source code file</li>
<li>return value (if there is one)</li>
</ol>
<h3>Memory Allocation Tracing</h3>
<p>Setting the trace level to maximum causes memory allocations and frees to be traced along with the default trace entries, with messages like the following: </p><div class="fragment"><div class="line">20130528 161819.657 Allocating 16 bytes in heap at file /home/<USER>/workspaces/mqrtc/mqttv3c/src/MQTTPacket.c line 177 ptr 0x179f930</div>
<div class="line"> </div>
<div class="line">20130528 161819.657 Freeing 16 bytes in heap at file /home/<USER>/workspaces/mqrtc/mqttv3c/src/MQTTPacket.c line 201, heap use now 896 bytes</div>
</div><!-- fragment --><p> When the last MQTT client object is destroyed, if the trace is being recorded and all memory allocated by the client library has not been freed, an error message will be written to the trace. This can help with fixing memory leaks. The message will look like this: </p><div class="fragment"><div class="line">20130528 163909.208 Some memory not freed at shutdown, possible memory leak</div>
<div class="line">20130528 163909.208 Heap scan start, total 880 bytes</div>
<div class="line">20130528 163909.208 Heap element size 32, line 354, file /home/<USER>/workspaces/mqrtc/mqttv3c/src/MQTTPacket.c, ptr 0x260cb00</div>
<div class="line">20130528 163909.208   Content</div>
<div class="line">20130528 163909.209 Heap scan end</div>
</div><!-- fragment --><p>√* </p>
</div></div><!-- contents -->
</div><!-- PageDoc -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Tue Jan 7 2025 13:21:06 for Paho MQTT C Client Library by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.12.0
</small></address>
</div><!-- doc-content -->
</body>
</html>
