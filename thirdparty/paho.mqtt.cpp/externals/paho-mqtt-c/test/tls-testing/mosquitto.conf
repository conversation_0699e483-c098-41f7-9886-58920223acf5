log_type error
log_type warning
log_type notice
log_type information
#log_type debug

#log_dest file /var/log/mosquitto/tls-testing.log

allow_anonymous true
password_file test/tls-testing/mosquitto.pw

#message_size_limit 5000000

# non-SSL listeners
listener 1883
listener 18883

# listener for mutual authentication
listener 18884
cafile test/ssl/all-ca.crt
certfile test/ssl/server.crt
keyfile test/ssl/server.key
require_certificate true
use_identity_as_username false
#tls_version tlsv1

# server authentication - no client authentication
listener 18885
protocol websockets
cafile test/ssl/all-ca.crt
certfile test/ssl/server.crt
keyfile test/ssl/server.key
require_certificate false
#tls_version tlsv1

listener 18886
cafile test/tls-testing/keys/all-ca.crt
certfile test/ssl/server.crt
keyfile test/ssl/server.key
require_certificate false
#ciphers ADH-DES-CBC-SHA
#tls_version tlsv1

# server authentication - no client authentication - uses fake hostname to
# simulate mitm attack. Clients should refuse to connect to this listener.
listener 18887
#cafile test/tls-testing/keys/all-ca.crt
cafile test/tls-testing/keys/server/server.crt
certfile test/tls-testing/keys/server/server-mitm.crt
keyfile test/tls-testing/keys/server/server-mitm.key
require_certificate true
#tls_version tlsv1

# TLS-PSK authentication
listener 18888
ciphers PSK-AES128-CBC-SHA
psk_hint Test
psk_file test/tls-testing/mosquitto.psk
