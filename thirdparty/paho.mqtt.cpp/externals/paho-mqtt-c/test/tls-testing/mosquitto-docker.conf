log_type error
log_type warning
log_type notice
log_type information
#log_type debug

#log_dest file /var/log/mosquitto/tls-testing.log

allow_anonymous true

#message_size_limit 5000000

# non-SSL listeners
listener 1883
listener 18883

# listener for mutual authentication
listener 18884
cafile /mosquitto/config/keys/all-ca.crt
certfile /mosquitto/config/keys/server/server.crt
keyfile /mosquitto/config/keys/server/server.key
require_certificate true
#tls_version tlsv1

# server authentication - no client authentication
listener 18885
cafile /mosquitto/config/keys/all-ca.crt
certfile /mosquitto/config/keys/server/server.crt
keyfile /mosquitto/config/keys/server/server.key
require_certificate false
#tls_version tlsv1

listener 18886
cafile /mosquitto/config/keys/all-ca.crt
certfile /mosquitto/config/keys/server/server.crt
keyfile /mosquitto/config/keys/server/server.key
require_certificate false
#ciphers ADH-DES-CBC-SHA
#tls_version tlsv1

# server authentication - no client authentication - uses fake hostname to
# simulate mitm attack. Clients should refuse to connect to this listener.
listener 18887
#cafile /mosquitto/config/keys/all-ca.crt
cafile /mosquitto/config/keys/server/server.crt
certfile /mosquitto/config/keys/server/server-mitm.crt
keyfile /mosquitto/config/keys/server/server-mitm.key
require_certificate true
#tls_version tlsv1
