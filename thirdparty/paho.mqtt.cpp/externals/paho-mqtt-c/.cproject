<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
    	
    <storageModule moduleId="org.eclipse.cdt.core.settings">
        		
        <cconfiguration id="cdt.managedbuild.toolchain.gnu.base.**********">
            			
            <storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="cdt.managedbuild.toolchain.gnu.base.**********" moduleId="org.eclipse.cdt.core.settings" name="debug-native">
                				
                <externalSettings/>
                				
                <extensions>
                    					
                    <extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
                    					
                    <extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
                    					
                    <extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
                    					
                    <extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
                    					
                    <extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
                    					
                    <extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
                    				
                </extensions>
                			
            </storageModule>
            			
            <storageModule moduleId="cdtBuildSystem" version="4.0.0">
                				
                <configuration artifactName="${ProjName}" buildProperties="" description="" id="cdt.managedbuild.toolchain.gnu.base.**********" name="debug-native" optionalBuildProperties="org.eclipse.cdt.docker.launcher.containerbuild.property.selectedvolumes=,org.eclipse.cdt.docker.launcher.containerbuild.property.volumes=" parent="org.eclipse.cdt.build.core.emptycfg">
                    					
                    <folderInfo id="cdt.managedbuild.toolchain.gnu.base.**********.2105697674" name="/" resourcePath="">
                        						
                        <toolChain id="cdt.managedbuild.toolchain.gnu.base.1661896025" name="cdt.managedbuild.toolchain.gnu.base" superClass="cdt.managedbuild.toolchain.gnu.base">
                            							
                            <targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="cdt.managedbuild.target.gnu.platform.base.189112861" name="Debug Platform" osList="linux,hpux,aix,qnx" superClass="cdt.managedbuild.target.gnu.platform.base"/>
                            							
                            <builder arguments="cdt-build.sh" autoBuildTarget="" cleanBuildTarget="clean" command="sh" enableAutoBuild="false" enableCleanBuild="true" enabledIncrementalBuild="true" id="cdt.managedbuild.target.gnu.builder.base.1205354372" incrementalBuildTarget="" keepEnvironmentInBuildfile="false" managedBuildOn="false" name="Gnu Make Builder" parallelBuildOn="true" parallelizationNumber="optimal" superClass="cdt.managedbuild.target.gnu.builder.base"/>
                            							
                            <tool id="cdt.managedbuild.tool.gnu.archiver.base.1645786956" name="GCC Archiver" superClass="cdt.managedbuild.tool.gnu.archiver.base"/>
                            							
                            <tool id="cdt.managedbuild.tool.gnu.cpp.compiler.base.2093522624" name="GCC C++ Compiler" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.base">
                                								
                                <inputType id="cdt.managedbuild.tool.gnu.cpp.compiler.input.58852055" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.input"/>
                                							
                            </tool>
                            							
                            <tool id="cdt.managedbuild.tool.gnu.c.compiler.base.919295913" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.c.compiler.base">
                                								
                                <inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.873628110" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
                                							
                            </tool>
                            							
                            <tool id="cdt.managedbuild.tool.gnu.c.linker.base.134024992" name="GCC C Linker" superClass="cdt.managedbuild.tool.gnu.c.linker.base"/>
                            							
                            <tool id="cdt.managedbuild.tool.gnu.cpp.linker.base.637863378" name="GCC C++ Linker" superClass="cdt.managedbuild.tool.gnu.cpp.linker.base">
                                								
                                <inputType id="cdt.managedbuild.tool.gnu.cpp.linker.input.126058850" superClass="cdt.managedbuild.tool.gnu.cpp.linker.input">
                                    									
                                    <additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
                                    									
                                    <additionalInput kind="additionalinput" paths="$(LIBS)"/>
                                    								
                                </inputType>
                                							
                            </tool>
                            							
                            <tool id="cdt.managedbuild.tool.gnu.assembler.base.1158334210" name="GCC Assembler" superClass="cdt.managedbuild.tool.gnu.assembler.base">
                                								
                                <inputType id="cdt.managedbuild.tool.gnu.assembler.input.1398987161" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
                                							
                            </tool>
                            						
                        </toolChain>
                        					
                    </folderInfo>
                    					
                    <fileInfo id="cdt.managedbuild.toolchain.gnu.base.**********.660980254" name="mqttclient_module.c" rcbsApplicability="disable" resourcePath="test/python/mqttclient_module.c" toolsToInvoke="cdt.managedbuild.tool.gnu.c.compiler.base.919295913.892752676">
                        						
                        <tool id="cdt.managedbuild.tool.gnu.c.compiler.base.919295913.892752676" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.c.compiler.base.919295913">
                            							
                            <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="gnu.c.compiler.option.include.paths.1043418401" name="Include paths (-I)" superClass="gnu.c.compiler.option.include.paths" valueType="includePath">
                                								
                                <listOptionValue builtIn="false" value="/usr/include/python2.6"/>
                                							
                            </option>
                            							
                            <inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.1471037566" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
                            						
                        </tool>
                        					
                    </fileInfo>
                    					
                    <sourceEntries>
                        						
                        <entry flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name="src"/>
                        					
                    </sourceEntries>
                    				
                </configuration>
                			
            </storageModule>
            			
            <storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
            		
        </cconfiguration>
        		
        <cconfiguration id="cdt.managedbuild.toolchain.gnu.base.**********.128299804">
            			
            <storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="cdt.managedbuild.toolchain.gnu.base.**********.128299804" moduleId="org.eclipse.cdt.core.settings" name="debug-x86-linux">
                				
                <externalSettings/>
                				
                <extensions>
                    					
                    <extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
                    					
                    <extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
                    					
                    <extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
                    					
                    <extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
                    					
                    <extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
                    					
                    <extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
                    				
                </extensions>
                			
            </storageModule>
            			
            <storageModule moduleId="cdtBuildSystem" version="4.0.0">
                				
                <configuration artifactName="${ProjName}" buildProperties="" description="" id="cdt.managedbuild.toolchain.gnu.base.**********.128299804" name="debug-x86-linux" parent="org.eclipse.cdt.build.core.emptycfg">
                    					
                    <folderInfo id="cdt.managedbuild.toolchain.gnu.base.**********.128299804." name="/" resourcePath="">
                        						
                        <toolChain id="cdt.managedbuild.toolchain.gnu.base.1794382793" name="cdt.managedbuild.toolchain.gnu.base" superClass="cdt.managedbuild.toolchain.gnu.base">
                            							
                            <targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="cdt.managedbuild.target.gnu.platform.base.1485941262" name="Debug Platform" osList="linux,hpux,aix,qnx" superClass="cdt.managedbuild.target.gnu.platform.base"/>
                            							
                            <builder id="cdt.managedbuild.target.gnu.builder.base.220668335" keepEnvironmentInBuildfile="false" managedBuildOn="false" name="Gnu Make Builder" parallelBuildOn="true" parallelizationNumber="optimal" superClass="cdt.managedbuild.target.gnu.builder.base"/>
                            							
                            <tool id="cdt.managedbuild.tool.gnu.archiver.base.284844463" name="GCC Archiver" superClass="cdt.managedbuild.tool.gnu.archiver.base"/>
                            							
                            <tool id="cdt.managedbuild.tool.gnu.cpp.compiler.base.1478312340" name="GCC C++ Compiler" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.base">
                                								
                                <inputType id="cdt.managedbuild.tool.gnu.cpp.compiler.input.1918693642" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.input"/>
                                							
                            </tool>
                            							
                            <tool id="cdt.managedbuild.tool.gnu.c.compiler.base.1542253543" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.c.compiler.base">
                                								
                                <inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.273102950" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
                                							
                            </tool>
                            							
                            <tool id="cdt.managedbuild.tool.gnu.c.linker.base.1110240347" name="GCC C Linker" superClass="cdt.managedbuild.tool.gnu.c.linker.base"/>
                            							
                            <tool id="cdt.managedbuild.tool.gnu.cpp.linker.base.1992155787" name="GCC C++ Linker" superClass="cdt.managedbuild.tool.gnu.cpp.linker.base">
                                								
                                <inputType id="cdt.managedbuild.tool.gnu.cpp.linker.input.1355647881" superClass="cdt.managedbuild.tool.gnu.cpp.linker.input">
                                    									
                                    <additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
                                    									
                                    <additionalInput kind="additionalinput" paths="$(LIBS)"/>
                                    								
                                </inputType>
                                							
                            </tool>
                            							
                            <tool id="cdt.managedbuild.tool.gnu.assembler.base.1049977093" name="GCC Assembler" superClass="cdt.managedbuild.tool.gnu.assembler.base">
                                								
                                <inputType id="cdt.managedbuild.tool.gnu.assembler.input.1999881075" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
                                							
                            </tool>
                            						
                        </toolChain>
                        					
                    </folderInfo>
                    					
                    <fileInfo id="cdt.managedbuild.toolchain.gnu.base.**********.128299804.44392871" name="mqttclient_module.c" rcbsApplicability="disable" resourcePath="test/python/mqttclient_module.c" toolsToInvoke="cdt.managedbuild.tool.gnu.c.compiler.base.1542253543.1236301446">
                        						
                        <tool id="cdt.managedbuild.tool.gnu.c.compiler.base.1542253543.1236301446" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.c.compiler.base.1542253543">
                            							
                            <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="gnu.c.compiler.option.include.paths.387085861" name="Include paths (-I)" superClass="gnu.c.compiler.option.include.paths" valueType="includePath">
                                								
                                <listOptionValue builtIn="false" value="/usr/include/python2.6/"/>
                                							
                            </option>
                            							
                            <inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.912266645" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
                            						
                        </tool>
                        					
                    </fileInfo>
                    				
                </configuration>
                			
            </storageModule>
            			
            <storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
            		
        </cconfiguration>
        		
        <cconfiguration id="cdt.managedbuild.toolchain.gnu.base.**********.**********">
            			
            <storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="cdt.managedbuild.toolchain.gnu.base.**********.**********" moduleId="org.eclipse.cdt.core.settings" name="debug-arm-linux-gnueabihf">
                				
                <externalSettings/>
                				
                <extensions>
                    					
                    <extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
                    					
                    <extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
                    					
                    <extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
                    					
                    <extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
                    					
                    <extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
                    					
                    <extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
                    				
                </extensions>
                			
            </storageModule>
            			
            <storageModule moduleId="cdtBuildSystem" version="4.0.0">
                				
                <configuration artifactName="${ProjName}" buildProperties="" description="" id="cdt.managedbuild.toolchain.gnu.base.**********.**********" name="debug-arm-linux-gnueabihf" parent="org.eclipse.cdt.build.core.emptycfg">
                    					
                    <folderInfo id="cdt.managedbuild.toolchain.gnu.base.**********.**********." name="/" resourcePath="">
                        						
                        <toolChain id="cdt.managedbuild.toolchain.gnu.base.831723006" name="cdt.managedbuild.toolchain.gnu.base" superClass="cdt.managedbuild.toolchain.gnu.base">
                            							
                            <targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="cdt.managedbuild.target.gnu.platform.base.1838207249" name="Debug Platform" osList="linux,hpux,aix,qnx" superClass="cdt.managedbuild.target.gnu.platform.base"/>
                            							
                            <builder id="cdt.managedbuild.target.gnu.builder.base.373473642" keepEnvironmentInBuildfile="false" managedBuildOn="false" name="Gnu Make Builder" parallelBuildOn="true" parallelizationNumber="optimal" superClass="cdt.managedbuild.target.gnu.builder.base"/>
                            							
                            <tool id="cdt.managedbuild.tool.gnu.archiver.base.1386858473" name="GCC Archiver" superClass="cdt.managedbuild.tool.gnu.archiver.base"/>
                            							
                            <tool id="cdt.managedbuild.tool.gnu.cpp.compiler.base.1005171182" name="GCC C++ Compiler" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.base">
                                								
                                <inputType id="cdt.managedbuild.tool.gnu.cpp.compiler.input.1367844459" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.input"/>
                                							
                            </tool>
                            							
                            <tool id="cdt.managedbuild.tool.gnu.c.compiler.base.1805700752" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.c.compiler.base">
                                								
                                <inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.892553453" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
                                							
                            </tool>
                            							
                            <tool id="cdt.managedbuild.tool.gnu.c.linker.base.1775502227" name="GCC C Linker" superClass="cdt.managedbuild.tool.gnu.c.linker.base"/>
                            							
                            <tool id="cdt.managedbuild.tool.gnu.cpp.linker.base.2000163116" name="GCC C++ Linker" superClass="cdt.managedbuild.tool.gnu.cpp.linker.base">
                                								
                                <inputType id="cdt.managedbuild.tool.gnu.cpp.linker.input.1797952705" superClass="cdt.managedbuild.tool.gnu.cpp.linker.input">
                                    									
                                    <additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
                                    									
                                    <additionalInput kind="additionalinput" paths="$(LIBS)"/>
                                    								
                                </inputType>
                                							
                            </tool>
                            							
                            <tool id="cdt.managedbuild.tool.gnu.assembler.base.1735139022" name="GCC Assembler" superClass="cdt.managedbuild.tool.gnu.assembler.base">
                                								
                                <inputType id="cdt.managedbuild.tool.gnu.assembler.input.1355266401" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
                                							
                            </tool>
                            						
                        </toolChain>
                        					
                    </folderInfo>
                    					
                    <fileInfo id="cdt.managedbuild.toolchain.gnu.base.**********.**********.1410045178" name="mqttclient_module.c" rcbsApplicability="disable" resourcePath="test/python/mqttclient_module.c" toolsToInvoke="cdt.managedbuild.tool.gnu.c.compiler.base.1805700752.404015131">
                        						
                        <tool id="cdt.managedbuild.tool.gnu.c.compiler.base.1805700752.404015131" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.c.compiler.base.1805700752">
                            							
                            <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="gnu.c.compiler.option.include.paths.253579949" name="Include paths (-I)" superClass="gnu.c.compiler.option.include.paths" valueType="includePath">
                                								
                                <listOptionValue builtIn="false" value="/usr/include/python2.6/"/>
                                							
                            </option>
                            							
                            <inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.987997893" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
                            						
                        </tool>
                        					
                    </fileInfo>
                    				
                </configuration>
                			
            </storageModule>
            			
            <storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
            		
        </cconfiguration>
        	
    </storageModule>
    	
    <storageModule moduleId="cdtBuildSystem" version="4.0.0">
        		
        <project id="org.eclipse.paho.mqtt.c.null.**********" name="org.eclipse.paho.mqtt.c"/>
        	
    </storageModule>
    	
    <storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
    	
    <storageModule moduleId="refreshScope" versionNumber="2">
        		
        <configuration configurationName="arm-linux-gnueabihf">
            			
            <resource resourceType="PROJECT" workspacePath="/org.eclipse.paho.mqtt.c"/>
            		
        </configuration>
        		
        <configuration configurationName="debug-native"/>
        		
        <configuration configurationName="debug-x86-linux"/>
        		
        <configuration configurationName="Default">
            			
            <resource resourceType="PROJECT" workspacePath="/org.eclipse.paho.mqtt.c"/>
            		
        </configuration>
        		
        <configuration configurationName="debug-arm-linux-gnueabihf"/>
        		
        <configuration configurationName="x86-linux">
            			
            <resource resourceType="PROJECT" workspacePath="/org.eclipse.paho.mqtt.c"/>
            		
        </configuration>
        	
    </storageModule>
    	
    <storageModule moduleId="scannerConfiguration">
        		
        <autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
        		
        <scannerConfigBuildInfo instanceId="cdt.managedbuild.toolchain.gnu.base.**********.128299804;cdt.managedbuild.toolchain.gnu.base.**********.128299804.;cdt.managedbuild.tool.gnu.cpp.compiler.base.1478312340;cdt.managedbuild.tool.gnu.cpp.compiler.input.1918693642">
            			
            <autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
            		
        </scannerConfigBuildInfo>
        		
        <scannerConfigBuildInfo instanceId="cdt.managedbuild.toolchain.gnu.base.**********.128299804;cdt.managedbuild.toolchain.gnu.base.**********.128299804.;cdt.managedbuild.tool.gnu.c.compiler.base.1542253543;cdt.managedbuild.tool.gnu.c.compiler.input.273102950">
            			
            <autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
            		
        </scannerConfigBuildInfo>
        		
        <scannerConfigBuildInfo instanceId="cdt.managedbuild.toolchain.gnu.base.**********.**********;cdt.managedbuild.toolchain.gnu.base.**********.**********.;cdt.managedbuild.tool.gnu.c.compiler.base.1805700752;cdt.managedbuild.tool.gnu.c.compiler.input.892553453">
            			
            <autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
            		
        </scannerConfigBuildInfo>
        		
        <scannerConfigBuildInfo instanceId="cdt.managedbuild.toolchain.gnu.base.**********;cdt.managedbuild.toolchain.gnu.base.**********.2105697674;cdt.managedbuild.tool.gnu.cpp.compiler.base.2093522624;cdt.managedbuild.tool.gnu.cpp.compiler.input.58852055">
            			
            <autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
            		
        </scannerConfigBuildInfo>
        		
        <scannerConfigBuildInfo instanceId="cdt.managedbuild.toolchain.gnu.base.**********.**********;cdt.managedbuild.toolchain.gnu.base.**********.**********.;cdt.managedbuild.tool.gnu.cpp.compiler.base.1005171182;cdt.managedbuild.tool.gnu.cpp.compiler.input.1367844459">
            			
            <autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
            		
        </scannerConfigBuildInfo>
        		
        <scannerConfigBuildInfo instanceId="cdt.managedbuild.toolchain.gnu.base.**********;cdt.managedbuild.toolchain.gnu.base.**********.2105697674;cdt.managedbuild.tool.gnu.c.compiler.base.919295913;cdt.managedbuild.tool.gnu.c.compiler.input.873628110">
            			
            <autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
            		
        </scannerConfigBuildInfo>
        	
    </storageModule>
    	
    <storageModule moduleId="org.eclipse.cdt.make.core.buildtargets">
        		
        <buildTargets>
            			
            <target name="paho_c_pub" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
                				
                <buildCommand>make</buildCommand>
                				
                <buildArguments>-j8</buildArguments>
                				
                <buildTarget>paho_c_pub</buildTarget>
                				
                <stopOnError>true</stopOnError>
                				
                <useDefaultCommand>true</useDefaultCommand>
                				
                <runAllBuilders>true</runAllBuilders>
                			
            </target>
            		
        </buildTargets>
        	
    </storageModule>
    
</cproject>
