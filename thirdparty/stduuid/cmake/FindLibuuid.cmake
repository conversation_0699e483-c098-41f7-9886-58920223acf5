find_path(Libuuid_INCLUDE_DIRS uuid/uuid.h)
find_library(Libuuid_LIBRARIES uuid)

if (Libuuid_LIBRARIES AND Libuuid_INCLUDE_DIRS)
    set(Libuuid_FOUND YES)
    if (NOT Libuuid_FIND_QUIETLY)
        message(STATUS "Found libuuid: ${Libuuid_LIBRARIES}")
    endif ()
else ()
    if (Libuuid_FIND_REQUIRED)
        message(SEND_ERROR "Could NOT find libuuid")
    else ()
        if (NOT Libuuid_FIND_QUIETLY)
            message(STATUS "Could NOT find libuuid")
        endif ()
    endif ()
endif ()