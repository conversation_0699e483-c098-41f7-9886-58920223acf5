/**
 * @file UnixDomainSocket.h
 * <AUTHOR> (huang<PERSON><EMAIL>)
 * @brief uds通讯
 * @version 0.1
 * @date 2025-03-07
 * @copyright Copyright (c) {2024} 追觅科技有限公司版权所有
 */
#ifndef __UNIX_DOMAIN_SOCKET_H__
#define __UNIX_DOMAIN_SOCKET_H__

#include <iostream>
#include <mutex>
#include <vector>
#include <functional>

class UnixDomainSocket {
 public:
    UnixDomainSocket(std::string path);
    ~UnixDomainSocket();

    /**
     * @brief 创建UDS服务器
     * @return true 
     * @return false 
     */
    bool CreateServer();

    /**
     * @brief 以客户端的身份连接服务器
     * @return true 
     * @return false 
     */
    bool ConnectServer();

    /**
     * @brief 以客户端的身份发送数据到服务器
     * @param data 
     * @param len 
     * @return true 
     * @return false 
     */
    bool ClientSendMsg(const char* data, int len);

    /**
     * @brief 判断客户端是否连接
     * @return true 
     * @return false 
     */
    bool IsClientConnected();

    /**
     * @brief 以服务器的身份发送数据到指定客户端
     * @param fd 客户端句柄
     * @param data 数据指针
     * @param len 数据长度
     * @return true 
     * @return false 
     */
    bool ServerSendMsg(int fd, const char* data, int len);

    int GetClientSockfd() {
        return sockfd_client_;
    }

    /**
     * @brief 客户端接收数据回调函数
     * @param handler 
     */
    void AddReadServerDataCallBack(std::function<void(const uint8_t *, const uint32_t)> handler) {
        read_from_server_function_ = handler;
    }

    /**
     * @brief 服务端接收数据回调函数
     * @param handler 
     */
    void AddReadClientDataCallBack(std::function<void(const int fd, const uint8_t *, const uint32_t)> handler) {
        read_from_client_function_ = handler;
    }

    bool ClientSockfdClose();

    int cur_client_sockfd = -1;

private:
    std::string uds_path_;
    int sockfd_server_;
    int sockfd_client_;
    std::vector<int> sockfd_list_;
    const uint32_t MAX_CONNECT = 10;
    std::function<void(const uint8_t *, const uint32_t)> read_from_server_function_; // 接收回调
    std::function<void(const int fd, const uint8_t *, const uint32_t)> read_from_client_function_; // 接收回调
    std::function<void(const uint8_t *, const uint32_t)> accept_client_function_; // 接收回调

    /**
     * @brief 读取服务端的数据，自动触发
     * @param sockfd 
     */
    void ReadServerDataCallBack(int sockfd);

    /**
     * @brief 读取客户端数据，自动触发
     * @param sockfd 
     */
    void ReadClientDataCallBack(int sockfd);

    /**
     * @brief 连接客户端，自动触发
     * @param sockfd 
     */
    void AcceptClientCallBack(int sockfd);
};

#endif
