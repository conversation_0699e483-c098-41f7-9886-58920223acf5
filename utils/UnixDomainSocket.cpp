#include "UnixDomainSocket.h"
#include "xepoll.h"
#include <algorithm>
#include <arpa/inet.h>
#include <cstring>
#include <functional>
#include <iostream>
#include <netinet/in.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <unistd.h>
#include <sys/stat.h>

UnixDomainSocket::UnixDomainSocket(std::string path)
    : uds_path_(path), sockfd_server_(-1), sockfd_client_(-1)
{
}

UnixDomainSocket::~UnixDomainSocket()
{
    for (uint32_t i = 0; i < sockfd_list_.size(); i++) {
        MY_EPOLL.EpollDel(sockfd_list_[i]);
        close(sockfd_list_[i]);
    }
    if (sockfd_server_ > 0) {
        MY_EPOLL.EpollDel(sockfd_server_);
        close(sockfd_server_);
        unlink(uds_path_.c_str());
    }
    if (sockfd_client_ > 0) {
        MY_EPOLL.EpollDel(sockfd_client_);
        close(sockfd_client_);
    }
    // std::cout << "~UnixDomainSocket " << uds_path_.c_str() << std::endl;
}

bool UnixDomainSocket::CreateServer()
{
    sockfd_server_ = socket(AF_UNIX, SOCK_STREAM, 0);
    if (sockfd_server_ == -1) {
        perror("socket");
        return false;
    }

    struct sockaddr_un addr;
    memset(&addr, 0, sizeof(struct sockaddr_un));
    struct stat buffer;
    if (stat(uds_path_.c_str(), &buffer) == 0) {
        remove(uds_path_.c_str());
    }

    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, uds_path_.c_str(), sizeof(addr.sun_path) - 1);

    if (bind(sockfd_server_, (struct sockaddr *)&addr, sizeof(struct sockaddr_un)) == -1) {
        perror("bind");
        return false;
    }

    if (listen(sockfd_server_, MAX_CONNECT) == -1) {
        perror("listen");
        return false;
    }

    std::cout << "Server is listening on " << uds_path_ << std::endl;

    MY_EPOLL.EpollAddRead(sockfd_server_, std::bind(&UnixDomainSocket::AcceptClientCallBack, this, std::placeholders::_1));

    return true;
}

bool UnixDomainSocket::ConnectServer()
{
    sockfd_client_ = socket(AF_UNIX, SOCK_STREAM, 0);
    if (sockfd_client_ == -1) {
        perror("socket");
        return false;
    }

    struct sockaddr_un serverAddr;
    memset(&serverAddr, 0, sizeof(struct sockaddr_un));
    serverAddr.sun_family = AF_UNIX;
    strncpy(serverAddr.sun_path, uds_path_.c_str(), sizeof(serverAddr.sun_path) - 1);

    if (connect(sockfd_client_, (struct sockaddr *)&serverAddr, sizeof(struct sockaddr_un)) == -1) {
        // perror("connect");
        return false;
    }

    MY_EPOLL.EpollAddRead(sockfd_client_, std::bind(&UnixDomainSocket::ReadServerDataCallBack, this, std::placeholders::_1));

    return true;
}

bool UnixDomainSocket::IsClientConnected()
{
    if (sockfd_client_ == -1) {
        return false;
    } else {
        return true;
    }
}

bool UnixDomainSocket::ClientSendMsg(const char *data, int len)
{
    if (sockfd_client_ == -1) {
        std::cout << "client not connected" << std::endl;
        return false;
    }
    int32_t bytesSent = send(sockfd_client_, data, len, 0);
    if (bytesSent == -1) {
        perror("send");
        MY_EPOLL.EpollDel(sockfd_client_);
        close(sockfd_client_);
        sockfd_client_ = -1;
        return false;
    }
    return true;
}

bool UnixDomainSocket::ServerSendMsg(int fd, const char *data, int len)
{
    // 校验fd是否合法
    bool have_fd = false;
    for (uint32_t i = 0; i < sockfd_list_.size(); i++) {
        if (sockfd_list_[i] == fd) {
            have_fd = true;
            break;
        }
    }
    if (!have_fd) {
        return false;
    }

    int bytesSent = send(fd, data, len, 0);
    if (bytesSent == -1) {
        perror("send");
        MY_EPOLL.EpollDel(fd);
        close(fd);
        auto iter = std::remove(sockfd_list_.begin(), sockfd_list_.end(), fd);
        sockfd_list_.erase(iter, sockfd_list_.end());
    }
    return true;
}

void UnixDomainSocket::ReadServerDataCallBack(int sockfd)
{
    char buffer[1024];
    ssize_t bytesRead = read(sockfd, buffer, sizeof(buffer));
    if (bytesRead > 0) {
        buffer[bytesRead] = 0;
        if (read_from_server_function_) {
            read_from_server_function_((uint8_t *)buffer, bytesRead);
        } else {
            std::cout << buffer << std::endl;
        }
    } else {
        if (bytesRead == -1) {
            perror("read");
        }
        std::cout << "server " << sockfd << " close " << std::endl;
        close(sockfd);
        sockfd_client_ = -1;
    }
}

void UnixDomainSocket::ReadClientDataCallBack(int sockfd)
{
    char buffer[10240];
    ssize_t bytesRead = read(sockfd, buffer, sizeof(buffer));
    if (bytesRead > 0) {
        buffer[bytesRead] = 0;
        if (read_from_client_function_) {
            read_from_client_function_(sockfd, (uint8_t *)buffer, bytesRead);
        } else {
            std::cout << "Received data: " << buffer << std::endl;
        }
    } else {
        if (bytesRead == -1) {
            perror("read");
        }
        std::cout << "client " << sockfd << " close " << std::endl;
        if (cur_client_sockfd == sockfd) {
            cur_client_sockfd = -1;
        }
        auto iter = std::remove(sockfd_list_.begin(), sockfd_list_.end(), sockfd);
        sockfd_list_.erase(iter, sockfd_list_.end());
        MY_EPOLL.EpollDel(sockfd);
        close(sockfd);
    }
}

void UnixDomainSocket::AcceptClientCallBack(int sockfd)
{
    int client_socket = accept(sockfd, NULL, NULL);
    if (client_socket == -1) {
        perror("accept");
        return;
    }
    std::cout << "accept client: " << client_socket << std::endl;
    cur_client_sockfd = client_socket;

    sockfd_list_.push_back(client_socket);

    MY_EPOLL.EpollAddRead(client_socket, std::bind(&UnixDomainSocket::ReadClientDataCallBack, this, std::placeholders::_1));
}

bool UnixDomainSocket::ClientSockfdClose()
{
    if (sockfd_client_ > 0) {
        MY_EPOLL.EpollDel(sockfd_client_);
        close(sockfd_client_);
        sockfd_client_ = -1;
        return true;
    }
    return false;
}
