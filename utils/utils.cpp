#include <stdio.h>
#include <stdlib.h>
#include <fstream>
#include <sstream>
#include <iostream>
#include <string>
#include <sys/stat.h>
#include <dirent.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <stdio.h>
#include <string.h>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <unistd.h>
#include <regex>
#include <thread>
#include <openssl/md5.h>
#include "uuid.h"

#include "utils.h"

std::string Utils::ScanIioDevice(std::string name) {
    std::string device = "";
    if(!name.empty()) { //扫描设备
        for(int i = 0; i < 10; i++) {
            std::string filename = "/sys/bus/iio/devices/iio:device" + std::to_string(i) + "/name";
            // std::cout << "iio bus name" << filename << std::endl;
            struct stat buffer;
            if(stat(filename.c_str(), &buffer) == 0) {
                std::ifstream iio_name;
                iio_name.open(filename, std::ios::in);
                char buff[64] = {0};
                iio_name.read(buff, sizeof(buff));
                std::string dev_name(buff);

                if (dev_name.find(name) != std::string::npos) {
                    // std::cout << "find " << name << " device\n";
                    device = "/sys/bus/iio/devices/iio:device" + std::to_string(i) +"/";
                    break;
                }
            } else {
                // 搜索完了，没有找到对应设备
                std::cout << "iio bus scan end\n";
                break;
            }
        }
    }

    return device;
}

std::string Utils::ScanInputDevice(std::string name)
{
    std::string device = "";
    if(!name.empty()) { //扫描设备
        for(int i = 0; i < 10; i++) {
            std::string filename = "/sys/class/input/event" + std::to_string(i) + "/device/name";
            // std::cout << "iio bus name" << filename << std::endl;
            struct stat buffer;
            if(stat(filename.c_str(), &buffer) == 0) {
                std::ifstream iio_name;
                iio_name.open(filename, std::ios::in);
                char buff[64] = {0};
                iio_name.read(buff, sizeof(buff));
                std::string dev_name(buff);

                if (dev_name.find(name) != std::string::npos) {
                    // std::cout << "find " << name << " device\n";
                    device = "/dev/input/event" + std::to_string(i);
                    break;
                }
            } else {
                // 搜索完了，没有找到对应设备
                // std::cout << "input device scan end\n";
                break;
            }
        }
    }

    return device;
}

std::string Utils::ReadFileIntoString(const std::string& path) {
    std::ifstream input_file(path);
    if (!input_file.is_open()) {
        return "";
    }
    std::stringstream buffer;
    buffer << input_file.rdbuf();
    std::string contents(buffer.str());
    input_file.close();
    return contents;
}

void Utils::getFiles(std::string path, std::vector<std::string>& files)
{
#if defined(__unix__) || defined(__APPLE__)
    // check the parameter !
    if( path.empty() ) {
        return;
    }
    // check if dir_name is a valid dir
    struct stat s;
    lstat( path.c_str(), &s );
    if( ! S_ISDIR( s.st_mode ) ) {
        return;
    }

    struct dirent * filename;    // return value for readdir()
    DIR * dir;                   // return value for opendir()
    dir = opendir( path.c_str() );
    if( NULL == dir ) {
        return;
    }

    /* read all the files in the dir ~ */
    while( ( filename = readdir(dir) ) != NULL ) {
        // get rid of "." and ".."
        if( strcmp( filename->d_name , "." ) == 0 ||
            strcmp( filename->d_name , "..") == 0 )
            continue;
        std::string full_path = path + filename->d_name;
        struct stat s;
        lstat( full_path.c_str(), &s );
        if( S_ISDIR( s.st_mode ) ) {
            continue;
        }
        files.push_back(full_path);
    }
#endif
}

std::string Utils::Bytes2String(uint8_t *data, uint32_t len)
{
    char temp[512];
    std::string str("");
    for (size_t i = 0; i < len; i++) {
        snprintf(temp, sizeof(temp) - 1, "%02x ", data[i]);
        str.append(temp);
    }
    return str;
}


// CRC表格, 采用查表法实现CRC计算,为了减少工作量, 首先高位与低位互换,
// 这样只要CRC结果与原先CRC High的高位异或,即可得出正确的CRC结果.
const uint16_t g_usCRCTable[] =
    {
        0X0000, 0XC0C1, 0XC181, 0X0140, 0XC301, 0X03C0, 0X0280, 0XC241, 0XC601,
        0X06C0, 0X0780, 0XC741, 0X0500, 0XC5C1, 0XC481, 0X0440, 0XCC01, 0X0CC0,
        0X0D80, 0XCD41, 0X0F00, 0XCFC1, 0XCE81, 0X0E40, 0X0A00, 0XCAC1, 0XCB81,
        0X0B40, 0XC901, 0X09C0, 0X0880, 0XC841, 0XD801, 0X18C0, 0X1980, 0XD941,
        0X1B00, 0XDBC1, 0XDA81, 0X1A40, 0X1E00, 0XDEC1, 0XDF81, 0X1F40, 0XDD01,
        0X1DC0, 0X1C80, 0XDC41, 0X1400, 0XD4C1, 0XD581, 0X1540, 0XD701, 0X17C0,
        0X1680, 0XD641, 0XD201, 0X12C0, 0X1380, 0XD341, 0X1100, 0XD1C1, 0XD081,
        0X1040, 0XF001, 0X30C0, 0X3180, 0XF141, 0X3300, 0XF3C1, 0XF281, 0X3240,
        0X3600, 0XF6C1, 0XF781, 0X3740, 0XF501, 0X35C0, 0X3480, 0XF441, 0X3C00,
        0XFCC1, 0XFD81, 0X3D40, 0XFF01, 0X3FC0, 0X3E80, 0XFE41, 0XFA01, 0X3AC0,
        0X3B80, 0XFB41, 0X3900, 0XF9C1, 0XF881, 0X3840, 0X2800, 0XE8C1, 0XE981,
        0X2940, 0XEB01, 0X2BC0, 0X2A80, 0XEA41, 0XEE01, 0X2EC0, 0X2F80, 0XEF41,
        0X2D00, 0XEDC1, 0XEC81, 0X2C40, 0XE401, 0X24C0, 0X2580, 0XE541, 0X2700,
        0XE7C1, 0XE681, 0X2640, 0X2200, 0XE2C1, 0XE381, 0X2340, 0XE101, 0X21C0,
        0X2080, 0XE041, 0XA001, 0X60C0, 0X6180, 0XA141, 0X6300, 0XA3C1, 0XA281,
        0X6240, 0X6600, 0XA6C1, 0XA781, 0X6740, 0XA501, 0X65C0, 0X6480, 0XA441,
        0X6C00, 0XACC1, 0XAD81, 0X6D40, 0XAF01, 0X6FC0, 0X6E80, 0XAE41, 0XAA01,
        0X6AC0, 0X6B80, 0XAB41, 0X6900, 0XA9C1, 0XA881, 0X6840, 0X7800, 0XB8C1,
        0XB981, 0X7940, 0XBB01, 0X7BC0, 0X7A80, 0XBA41, 0XBE01, 0X7EC0, 0X7F80,
        0XBF41, 0X7D00, 0XBDC1, 0XBC81, 0X7C40, 0XB401, 0X74C0, 0X7580, 0XB541,
        0X7700, 0XB7C1, 0XB681, 0X7640, 0X7200, 0XB2C1, 0XB381, 0X7340, 0XB101,
        0X71C0, 0X7080, 0XB041, 0X5000, 0X90C1, 0X9181, 0X5140, 0X9301, 0X53C0,
        0X5280, 0X9241, 0X9601, 0X56C0, 0X5780, 0X9741, 0X5500, 0X95C1, 0X9481,
        0X5440, 0X9C01, 0X5CC0, 0X5D80, 0X9D41, 0X5F00, 0X9FC1, 0X9E81, 0X5E40,
        0X5A00, 0X9AC1, 0X9B81, 0X5B40, 0X9901, 0X59C0, 0X5880, 0X9841, 0X8801,
        0X48C0, 0X4980, 0X8941, 0X4B00, 0X8BC1, 0X8A81, 0X4A40, 0X4E00, 0X8EC1,
        0X8F81, 0X4F40, 0X8D01, 0X4DC0, 0X4C80, 0X8C41, 0X4400, 0X84C1, 0X8581,
        0X4540, 0X8701, 0X47C0, 0X4680, 0X8641, 0X8201, 0X42C0, 0X4380, 0X8341,
        0X4100, 0X81C1, 0X8081, 0X4040 };


/**
 *@brief  工具函数，用于计算循环冗余校验码
 *@param  pContent	指向需要计算校验码的数据
 *@param  usLength	数据的字节长度
 */
uint16_t Utils::CheckCRC(uint8_t *pContent, uint16_t usLength)
{
    uint16_t usCRCVal = 0xffff;                     // CRC初始值为0XFFFF
    uint16_t usCRCTemp;                             // CRC计算的临时变量
    uint16_t usCounter = 0;                         // 当前的计数个数
    uint8_t *pCurChar = pContent;                   // 当前的CRC指针
    uint8_t *pucLow = (uint8_t *)(&usCRCVal);       // 当前CRC的低字
    uint8_t *pucHigh = pucLow + 1;                  // 当前CRC的高字.

    // 从0字节开始遍历pContent所有内容
    while (usCounter < usLength)
    {
        // 与CRC的值异或.
        usCRCTemp = (*pucLow) ^ (*pCurChar);
        // 异或结果的低8位作为索引, 查g_usCRCTable表.
        usCRCTemp = g_usCRCTable[usCRCTemp];
        // 查表结果与CRC值的高8位(放到0~7位)异或, 结果作为CRC的结果
        usCRCVal = (uint16_t)((*pucHigh) ^ usCRCTemp);
        // 对于偶数字节的下一个字节,与checkSum的低32位字求和
        pCurChar++;
        usCounter++;
    }
    // 返回CRC结果
    return usCRCVal;
}

int64_t Utils::GetCurrentSecTime()
{
    auto current_time            = std::chrono::high_resolution_clock::now();
    auto duration_in_nanoseconds = std::chrono::duration_cast<std::chrono::seconds>(current_time.time_since_epoch());
    return duration_in_nanoseconds.count();
}

int64_t Utils::GetCurrentMsTime()
{
    auto current_time            = std::chrono::high_resolution_clock::now();
    auto duration_in_milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(current_time.time_since_epoch());
    return duration_in_milliseconds.count();
}

bool Utils::FileExists(const std::string name)
{
    struct stat buffer;
    return (stat(name.c_str(), &buffer) == 0);
}

bool Utils::DirectoryExists(const std::string path) {
    struct stat info;
    if (stat(path.c_str(), &info) != 0) {
        return false; // 路径不存在
    }
    return (info.st_mode & S_IFDIR); // 检查是否是目录
}

std::string Utils::getCurrentTime(int32_t zones)
{
    std::time_t result = std::time(nullptr) + zones * 3600;
    auto sec           = std::chrono::seconds(result);
    std::chrono::time_point<std::chrono::system_clock> now(sec);
    auto timet     = std::chrono::system_clock::to_time_t(now);
    auto localTime = *std::gmtime(&timet);

    std::stringstream ss;
    std::string str;
    ss << std::put_time(&localTime, "%H:%M");
    ss >> str;

    return str;
}

std::string Utils::getCurrentUser()
{
#if defined(__unix__)
    uid_t userid;
    struct passwd* pwd;
    userid = getuid();
    pwd = getpwuid(userid);
    return pwd->pw_name;
#elif defined(_WIN32) || defined(_WIN64)
#ifndef UNLEN
#define UNLEN 256  // 用户名最大长度
#endif
    TCHAR name[UNLEN + 1];
    DWORD size = UNLEN + 1;
    if (GetUserName(name, &size)) {
        return "";
    }
#endif
    return "";
}

// void Utils::Execute(std::string cmdline, std::string &recv)
// {
// #if defined(__unix__) || defined(__APPLE__)
//     FILE *stream = NULL;
//     char buff[1024];
//     char recv_buff[256]      = {0};

//     memset(recv_buff, 0, sizeof(recv_buff));

//     if ((stream = popen(cmdline.c_str(), "r")) != NULL) {
//         while (fgets(buff, 1024, stream)) {
//             strcat(recv_buff, buff);
//         }
//     }
//     recv = recv_buff;
//     pclose(stream);
// #endif
// }

void Utils::Execute(const std::string cmdline, std::string &recv) {
    #if defined(__unix__) || defined(__APPLE__)
    FILE *stream = nullptr;
    const int BUFF_SIZE = 1024;
    char buff[BUFF_SIZE];
    // 使用popen打开进程，并读取输出
    if ((stream = popen(cmdline.c_str(), "r")) != nullptr) {
        while (fgets(buff, BUFF_SIZE, stream) != nullptr) {
            // 直接使用std::string的append方法追加数据
            recv.append(buff);
        }
        pclose(stream);
    }
    #endif
}

void Utils::SuperExecute(std::string cmdline, std::string passwd)
{
#if defined(__unix__) || defined(__APPLE__)
    char cmd[256] = {0};
    int len = snprintf(cmd, sizeof(cmd), "echo %s | sudo -S %s", passwd.c_str(), cmdline.c_str());
    cmd[len] = 0;
    int ret = system(cmd);
    if (ret <= 0) {
        std::cerr << "system fail" << std::endl;
    }
#endif
}

void Utils::Addr2Line(std::string exe, std::vector<std::string>& strs)
{
#if defined(__unix__) || defined(__APPLE__)
    char str[1024] = {0};
    for (uint32_t i = 0; i < strs.size(); i++) {
        std::string line = strs[i];
        std::string::size_type index = line.find("(+"); // found start stuck
        line = line.substr(index + 1, line.size() - index - 1);
        if (index != std::string::npos) {
            index = line.find(")"); // foud end
            if (index != std::string::npos) {
                line = line.substr(0, index);
                int len = snprintf(str, sizeof(str), "addr2line -e %s %s", exe.c_str(), line.c_str());
                str[len] = 0;
                // std::cout << "Run " << str << std::endl;
                std::string recv;
                Execute(str, recv);
                std::ofstream outfile;
                if (recv.find("??") == std::string::npos) {
                    outfile.open("coredump.log", std::ios::out | std::ios::app);
                    if (outfile.is_open()) {
                        outfile << recv;
                        outfile.close();
                    }
                }
            }
        }
    }
#endif
}

static const std::string base64_chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    "abcdefghijklmnopqrstuvwxyz"
    "0123456789+/";

static inline bool is_base64(unsigned char c)
{
    return (isalnum(c) || (c == '+') || (c == '/'));
}

std::string Utils::Base64Encode(char const *bytes_to_encode, unsigned int in_len)
{
    std::string ret;
    int i = 0;
    int j = 0;
    unsigned char char_array_3[3];
    unsigned char char_array_4[4];

    while (in_len--) {
        char_array_3[i++] = *(bytes_to_encode++);
        if (i == 3) {
            char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
            char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
            char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
            char_array_4[3] = char_array_3[2] & 0x3f;

            for (i = 0; (i < 4); i++) {
                ret += base64_chars[char_array_4[i]];
            }
            i = 0;
        }
    }

    if (i) {
        for (j = i; j < 3; j++) {
            char_array_3[j] = '\0';
        }

        char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
        char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
        char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
        char_array_4[3] = char_array_3[2] & 0x3f;

        for (j = 0; (j < i + 1); j++) {
            ret += base64_chars[char_array_4[j]];
        }

        while ((i++ < 3)) {
            ret += '=';
        }
    }

    return ret;
}

std::string Utils::Base64Decode(std::string const &encoded_string)
{
    int in_len = encoded_string.size();
    int i      = 0;
    int j      = 0;
    int in_    = 0;
    unsigned char char_array_4[4], char_array_3[3];
    std::string ret;

    while (in_len-- && (encoded_string[in_] != '=') && is_base64(encoded_string[in_])) {
        char_array_4[i++] = encoded_string[in_];
        in_++;
        if (i == 4) {
            for (i = 0; i < 4; i++) {
                char_array_4[i] = base64_chars.find(char_array_4[i]);
            }

            char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
            char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
            char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

            for (i = 0; (i < 3); i++) {
                ret += char_array_3[i];
            }
            i = 0;
        }
    }

    if (i) {
        for (j = i; j < 4; j++) {
            char_array_4[j] = 0;
        }

        for (j = 0; j < 4; j++) {
            char_array_4[j] = base64_chars.find(char_array_4[j]);
        }

        char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
        char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
        char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

        for (j = 0; (j < i - 1); j++) {
            ret += char_array_3[j];
        }
    }

    return ret;
}


bool Utils::match_topic(const std::string& pattern, const std::string& topic) {
    // Create a regex pattern from the topic with wildcard handling
    std::string regex_pattern = "^";
    for (size_t i = 0; i < pattern.size(); ++i) {
        if (pattern[i] == '+') {
            regex_pattern += "[^/]+";
        } else if (pattern[i] == '#') {
            regex_pattern += ".*";
        } else {
            regex_pattern += pattern[i];
        }
    }
    regex_pattern += "$";

    std::regex topic_regex(regex_pattern);
    return std::regex_match(topic, topic_regex);
}

Json::Value Utils::GetResponseJson(){
    auto now = std::chrono::system_clock::now();
    auto millis = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    Json::Value result_json;
    Json::Value data_json;
    Json::Value sub_data_json;
    result_json["id"] = CreateUUID();
    result_json["timestamp"] = millis;
    result_json["method"] = "";
    data_json["code"] = 0;
    data_json["msg"] = "ok";
    data_json["data"] = sub_data_json;
    result_json["data"] = data_json;
    return result_json;
}

Json::Value Utils::GetMqttResponseJson(){
    auto now = std::chrono::system_clock::now();
    auto millis = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    Json::Value result_json;
    result_json["id"] = CreateUUID();
    result_json["version"] = "1.0";
    result_json["time"] = millis;
    result_json["method"] = "";
    result_json["code"] = 200;
    result_json["message"] = "success";
    result_json["data"] = Json::Value(Json::objectValue);
    return result_json;
}

std::string Utils::CreateUUID() {
    std::random_device rd;
    std::mt19937 gen(rd());
    uuids::uuid_random_generator generator(gen);
    uuids::uuid id = generator();
    return uuids::to_string(id);
}

void Utils::scanDiskDirectory(Json::Value& records) {
    // 获取当前目录和U盘目录列表上报
    Json::Value record;
    record["is_dir"] = true;
    record["file_path"] = "local";
    record["create_time"] = GetCurrentMsTime();
    record["size"] = 0;
    records.append(record);
    // 遍历mnt目录 获取以udisk开头的目录 也加入到records中
    std::string mntPath = "/mnt";
    DIR* mntDir = opendir(mntPath.c_str());
    if (!mntDir) {
        std::cerr << "Failed to open directory: " << mntPath << std::endl;
        return;
    }
    struct dirent* mntEntry;
    while ((mntEntry = readdir(mntDir)) != nullptr) {
        std::string name = mntEntry->d_name;
        if (name == "." || name == "..") continue;
        if (name.find("udisk") != std::string::npos) {
            Json::Value record;
            record["is_dir"] = true;
            record["file_path"] = name;
            record["create_time"] = GetCurrentMsTime();
            record["size"] = 0;
            records.append(record);
        }
    }
    closedir(mntDir);
}

void Utils::scanDirectory(std::string basePath, std::string relativePath, Json::Value& records) {
    std::string fullPath = basePath + (relativePath.empty() ? "" : "/" + relativePath);
    DIR* dir = opendir(fullPath.c_str());
    if (!dir) {
        std::cerr << "Failed to open directory: " << fullPath << std::endl;
        return;
    }
    
    struct dirent* entry;
    while ((entry = readdir(dir)) != nullptr) {
        std::string name = entry->d_name;
        if (name == "." || name == "..") continue;
        
        std::string fullEntryPath = fullPath + "/" + name;
        std::string relativeEntryPath = relativePath.empty() ? name : relativePath + "/" + name;
        if (basePath != "/mnt") {
            relativeEntryPath = "local/" + relativeEntryPath;
        }
        
        struct stat fileStat;
        if (stat(fullEntryPath.c_str(), &fileStat) == -1) continue;
        
        Json::Value record;
        record["is_dir"] = S_ISDIR(fileStat.st_mode);
        record["file_path"] = relativeEntryPath;
        record["create_time"] = static_cast<Json::Value::UInt64>(fileStat.st_mtime) * 1000; // Convert to milliseconds
        record["size"] = static_cast<Json::Value::UInt64>(fileStat.st_size);
        
        records.append(record);
    }
    
    closedir(dir);
}

std::string Utils::calculateMD5(const std::string& filepath) {
    MD5_CTX md5Context;
    MD5_Init(&md5Context);

    std::ifstream file(filepath, std::ios::binary);
    if (!file.is_open()) {
        return ""; // Or throw an exception
    }

    char buffer[1024];
    while (file.read(buffer, sizeof(buffer)) || file.gcount() > 0) {
        MD5_Update(&md5Context, buffer, file.gcount());
    }

    unsigned char result[MD5_DIGEST_LENGTH];
    MD5_Final(result, &md5Context);

    std::stringstream md5string;
    md5string << std::hex << std::setfill('0');
    for (int i = 0; i < MD5_DIGEST_LENGTH; i++) {
        md5string << std::setw(2) << (unsigned int)result[i];
    }
    return md5string.str();
}

double Utils::getUptimeInSeconds() {
    std::ifstream uptime_file("/proc/uptime");
    if (!uptime_file.is_open()) {
        std::cerr << "无法打开 /proc/uptime 文件" << std::endl;
        return -1;
    }

    double uptime_seconds = 0.0;
    uptime_file >> uptime_seconds;  // 第一个数是系统启动到现在的秒数
    return uptime_seconds;
}

std::string Utils::getCurrentTimeStr() {
    auto now = std::chrono::system_clock::now();
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;
    auto timer = std::chrono::system_clock::to_time_t(now);
    std::tm bt = *std::localtime(&timer);
    
    std::ostringstream oss;
    oss << std::put_time(&bt, "%Y-%m-%d %H:%M:%S");
    oss << '.' << std::setfill('0') << std::setw(3) << ms.count();

    return oss.str();
}

int Utils::GetLightChassis() {
    std::string recv;
    std::string cmd = "cat " + globalConfigObject["brightness_path"].asString() + " | tr -d '\n'";
    Utils::Execute(cmd, recv);
    int result = std::stoi(recv);
    return result;
}

std::string Utils::CreateDevice(const std::string url) {
    // 获取序列号
    std::string deviceCode = "";
    std::string devid            = Utils::ReadFileIntoString("/proc/cpuinfo");
    std::string cpuinfo          = "Serial";
    std::string::size_type index = devid.find(cpuinfo);
    if (index != std::string::npos) {
        uint64_t size                   = cpuinfo.size() + 4;
        std::string::size_type position = devid.find_first_of("\n", index + size);
        deviceCode                      = devid.substr(index + size, position - index - size);
        std::string msg = "CreateDevice Device ID is " + deviceCode;
        std::cout << msg << std::endl;
    }
    // 获取网卡mac地址
    std::string deviceMac;
    Execute("ip link show wlan0 | awk '/ether/ {print $2}' | tr -d '\n' | tr 'a-f' 'A-F'", deviceMac);
    std::string productUniqueId = "P300";

    Json::Value requestBody;
    requestBody["productUniqueId"] = productUniqueId;
    requestBody["deviceCode"] = deviceCode;
    requestBody["deviceMac"] = deviceMac;

    std::string requestBodyStr;
    JsoncppParseRead::ParseJsonToString(requestBodyStr, requestBody);

    // 使用curl发送POST请求
    std::string command = "curl -X POST -H \"Content-Type: application/json\" -d '" + requestBodyStr + "' " + url;
    std::cout << "CreateDevice command: " << command << std::endl;
    std::string responseStr;
    try {
        Execute(command, responseStr);
    } catch (const std::exception& e) {
        std::cerr << "Error executing command: " << e.what() << std::endl;
        return "";
    }
    std::cout << "CreateDevice response: " << responseStr << std::endl;

    Json::Value responseJson;
    if (!JsoncppParseRead::ReadStringToJson(responseStr, responseJson)) {
        std::cerr << "Failed to parse JSON response: " << responseStr << std::endl;
        return "";
    }

    // 提取并返回设备唯一ID
    if (responseJson.isMember("data") && responseJson["data"].isMember("deviceUniqueId")) {
        return responseJson["data"]["deviceUniqueId"].asString();
    } else {
        std::cerr << "Device ID not found in response: " << responseStr << std::endl;
        return "";
    }
}


bool Utils::GetDeviceCert(const std::string url) {
    std::string create_device_url = url + "/api/3dprint/device/v1/device/create";
    std::string get_cert_url = url + "/api/3dprint/device/v1/device/cert";
    std::string deviceUniqueId = CreateDevice(create_device_url);
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    if (deviceUniqueId.empty()) {
        std::cerr << "Failed to create device" << std::endl;
        return false;
    } else {
        // 使用curl发送POST请求
        get_cert_url += "?deviceUniqueId=" + deviceUniqueId;
        std::string requestBodyStr;
        std::string command = "curl -X POST -H \"Content-Type: application/json\" -d '" + requestBodyStr + "' " + get_cert_url;
        std::cout << "GetDeviceCert command: " << command << std::endl;
        std::string responseStr;
        Execute(command, responseStr);

        Json::Value responseJson;
        if (!JsoncppParseRead::ReadStringToJson(responseStr, responseJson)) {
            std::cerr << "Failed to parse JSON response: " << responseStr << std::endl;
            return "";
        }

        // 提取并返回设备ID
        if (responseJson.isMember("data") && responseJson["data"].isMember("clientId")) {
            if(responseJson["data"].isMember("deviceCn")) {
                globalConfigObject["device_cn"] = responseJson["data"]["deviceCn"].asString();
            }
            globalConfigObject["device_id"] = responseJson["data"]["clientId"].asString();
            std::string mqttproc_config_file_path = globalConfigObject["mqttproc_config_file_path"].asString();
            if (Utils::FileExists(mqttproc_config_file_path)) {
                std::ofstream file(mqttproc_config_file_path);
                file << globalConfigObject;
                file.close();
                std::cout << "clientId write success!! " << responseJson["data"]["clientId"].asString() << std::endl;
            }
        }
        // 创建目录
        std::string server_ca_cert_file = globalConfigObject["server_ca_cert_file"].asString();
        std::string server_ca_cert_dir = server_ca_cert_file.substr(0, server_ca_cert_file.rfind("/"));
        if (!Utils::DirectoryExists(server_ca_cert_dir)) {
            std::string command = "mkdir -p " + server_ca_cert_dir;
            std::string result;
            Utils::Execute(command, result);
        }
        std::string recv;
        if (responseJson.isMember("data") && responseJson["data"].isMember("serverRootCrt")) {
            std::string serverRootCrt = responseJson["data"]["serverRootCrt"].asString();
            std::string command = "echo \"" + serverRootCrt + "\" > " + static_cast<std::string>(globalConfigObject["server_ca_cert_file"].asString());
            Utils::Execute(command, recv);
            std::cout << "serverRootCrt write success!!" << std::endl;
        }
        if (responseJson.isMember("data") && responseJson["data"].isMember("clientCrt")) {
            std::string clientCrt = responseJson["data"]["clientCrt"].asString();
            std::string command = "echo \"" + clientCrt + "\" > " + static_cast<std::string>(globalConfigObject["client_cert_file"].asString());
            Utils::Execute(command, recv);
            std::cout << "clientCrt write success!!" << std::endl;
        }
        if (responseJson.isMember("data") && responseJson["data"].isMember("clientKey")) {
            std::string clientKey = responseJson["data"]["clientKey"].asString();
            std::string command = "echo \"" + clientKey + "\" > " + static_cast<std::string>(globalConfigObject["client_key_file"].asString());
            Utils::Execute(command, recv);
            std::cout << "clientKey write success!!" << std::endl;
        }

        Utils::Execute("sync", recv);
    }
    return true;
}

bool Utils::isNetworkAvailable() {
    std::string ip = globalConfigObject["server_url"].asString();
    std::string new_addr;
    size_t pos1 = ip.find("://") + 3;
    size_t pos2 = ip.find(":", pos1);
    if (pos2 != std::string::npos) {
        new_addr = ip.substr(pos1, pos2 - pos1);
    } else {
        new_addr = ip.substr(pos1);
    }
    std::string command = "ping -c 1 -w 1 " + new_addr + " > /dev/null 2>&1";
    std::cout << "isNetworkAvailable: " << command << std::endl;
    const char* command_cstr = command.c_str();
    int result = std::system(command.c_str());
    std::cout << "isNetworkAvailable: " << result << std::endl;
    return result == 0;
}

void Utils::syncNetworkTime() {
    while (true) {
        // 判断当前年份是否大于等于2025 月份大于3月
        time_t now = time(0);
        tm* currentTime = localtime(&now);
        if (currentTime->tm_year + 1900 >= 2025 && currentTime->tm_mon + 1 > 3) {
            std::cout << "Current year is greater than or equal to 2025. Skipping time synchronization." << std::endl;
            return;
        }
        // TODO 系统做时间同步后，此处可以删除
        SuperExecute("date -s \"$(curl -sI baidu.com | grep -i Date | cut -d' ' -f2-)\"", "admin1234.");
        std::cout << "Network time synchronized successfully." << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(10));
    }
}