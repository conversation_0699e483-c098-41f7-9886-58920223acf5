/**
 * @file jsonparse.h
 * <AUTHOR> (<PERSON><PERSON><PERSON><PERSON><PERSON>@dreame.tech)
 * @brief json解析
 * @version 0.1
 * @date 2024-11-29
 * @copyright Copyright (c) {2024} 追觅科技有限公司版权所有
 */
#ifndef __JSONPARSE_H__
#define __JSONPARSE_H__

#include <stdio.h>

#include <iostream>
#include <string>

#include "json/json.h"
class JsoncppParseRead
{
public:
    JsoncppParseRead();
    ~JsoncppParseRead();
    static bool ParseJsonToString(std::string &des_string, const Json::Value &src_json);

    static bool ReadStringToJson(const std::string &src_string, Json::Value &dst_json);

    static bool ReadFileToJson(const std::string &file_name, Json::Value &des_json);

private:
};

#endif
