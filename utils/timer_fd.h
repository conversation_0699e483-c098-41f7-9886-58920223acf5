/**
 * @file timer_fd.h
 * <AUTHOR> (h<PERSON><PERSON><PERSON><PERSON>@dreame.tech)
 * @brief 定时器
 * @version 0.1
 * @date 2024-11-29
 * @copyright Copyright (c) {2024} 追觅科技有限公司版权所有
 */
#ifndef __TIMER_FD_H__
#define __TIMER_FD_H__

#if defined(__APPLE__)
#include <dispatch/dispatch.h>
#elif defined(__linux__)
#include <linux/input.h>
#include <sys/timerfd.h>
#endif
#include <iostream>
#include <functional>
#include <cstdint>

class TimerFd
{
private:
#if defined(__APPLE__)
    dispatch_source_t timer_;
#elif defined(__linux__)
    int timer_fd_;
#endif
    std::function<void(void)> read_function_; // 接收回调

    /**
     * @brief 定时器回调
     * @return int
     */
    void timeOutCallBack();

public:
    TimerFd(/* args */);
    ~TimerFd();

    /**
     * @brief 初始化定时器
     * @return bool
     */
    bool InitTimer(void);

    /**
     * @brief 启动定时
     */
    void StartTimer(uint32_t seconds, uint32_t nseconds = 0);

    /**
     * @brief 定时取消
     */
    void CancelTimer();

    /**
     * @brief 回调注册
     * @param handler
     */
    void AddCallback(std::function<void(void)> handler);

    /**
     * 清除回调
     */
    void RemoveCallback()
    {
        read_function_ = nullptr;
    }
};

#endif
