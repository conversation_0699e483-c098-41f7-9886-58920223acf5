/**
 * @file utils.h
 * <AUTHOR> (huang<PERSON><EMAIL>)
 * @brief 封装一些小工具
 * @version 0.1
 * @date 2024-11-29
 * @copyright Copyright (c) {2024} 追觅科技有限公司版权所有
 */
#ifndef __UTILS_H__
#define __UTILS_H__

#include <iostream>
#include <vector>
#include <cstdint>
#include "json/json.h"
#include "jsonparse.h"
#include "config.h"
#if defined(__unix__) || defined(__APPLE__)
#include <pwd.h>
#elif defined(_WIN32) || defined(_WIN64)
#include <windows.h>
#endif

namespace Utils {
/**
 * @brief 搜索设备 iio 设备
 * @param name 模块名称
 * @return std::string 返回路径
 */
std::string ScanIioDevice(std::string name);

/**
 * @brief 搜索设备 输入 设备
 * @param name 模块名称
 * @return std::string 返回输入设备
*/
std::string ScanInputDevice(std::string name);

/**
 * @brief 一次性读取文件所有内容
 * @param path 文件路径
 * @return std::string 内容
 */
std::string ReadFileIntoString(const std::string& path);

/**
 * @brief 获取文件夹下所有文件
 * @param path
 * @param files
 */
void getFiles(std::string path, std::vector<std::string>& files);

/**
 * @brief 字节转文本
 * @param data
 * @param len
 * @return std::string
 */
std::string Bytes2String(uint8_t *data, uint32_t len);

/**
 * @brief 工具函数，用于计算循环冗余校验码
 * @param pContent 指向需要计算校验码的数据
 * @param usLength 数据的字节长度
 * @return uint16_t
 */
uint16_t CheckCRC(uint8_t *pContent, uint16_t usLength);

/**
 * @brief 获取当前秒级时间戳
 * @return uint32_t
 */
int64_t GetCurrentSecTime();

/**
 * @brief 获取当前毫秒级时间戳
 * @return uint32_t
 */
int64_t GetCurrentMsTime();

/**
 * @brief 检查文件是否存在
 * @param name
 * @return true
 * @return false
 */
bool FileExists(const std::string name);

/**
 * @brief 检查文件夹是否存在
 * @param name
 * @return true
 * @return false
 */
bool DirectoryExists(const std::string path);

/**
 * @brief 获取特定时区的时分
 * @param zones
 * @return std::string
 */
std::string getCurrentTime(int32_t zones);

/**
 * @brief 获取当前用户
 * @return std::string
 */
std::string getCurrentUser();

/**
 * @brief 管道运行命令
 * @param cmdline
 * @param recv
 */
void Execute(std::string cmdline, std::string &recv);

/**
 * @brief 超级用户运行命令
 * @param cmdline
 */
void SuperExecute(std::string cmdline, std::string passwd);

/**
 * @brief
 * @param line
 */
void Addr2Line(std::string exe, std::vector<std::string>& strs);

/**
 * @brief base64编码
 * @param len
 * @return std::string
 */
std::string Base64Encode(char const *, unsigned int len);

/**
 * @brief base64解码
 * @param s
 * @return std::string
 */
std::string Base64Decode(std::string const &s);

/**
 * @brief 判断实际 topic 和订阅 topic 是否匹配
 * @param pattern 
 * @param topic 
 * @return true 
 * @return false 
 */
bool match_topic(const std::string& pattern, const std::string& topic);

/**
 * @brief 获取对mqtt服务的JSON响应体的初步格式
 * @return Json::Value 
 */
Json::Value GetResponseJson();

/**
 * @brief 获取对mqtt服务的JSON响应体的初步格式
 * @return Json::Value 
 */
Json::Value GetMqttResponseJson();

std::string CreateUUID();

/**
 * @brief 获取系统运行时间（以秒为单位）
 * @return double 系统运行时间，如果获取失败则返回-1
 */
double getUptimeInSeconds();

void scanDirectory(std::string basePath, std::string relativePath, Json::Value& records);

static std::string getCurrentTimeStr();

int GetLightChassis();

/**
 * @brief 创建设备
 * @param url 
 * @return std::string 
 */
std::string CreateDevice(const std::string url);

/**
 * @brief 获取设备证书
 * @param url 
 * @return true 
 * @return false 
 */
bool GetDeviceCert(const std::string url);

/**
 * @brief 检查网络是否可用
 * @return true 
 * @return false 
 */
bool isNetworkAvailable();

/**
 * @brief 同步当前UTC网络时间
 */
void syncNetworkTime();

/**
 * @brief 获取磁盘目录信息
 */
void scanDiskDirectory(Json::Value& records);

}

namespace Utils {
    std::string calculateMD5(const std::string& filepath);
}

#endif
