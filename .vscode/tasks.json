{"version": "2.0.0", "tasks": [{"label": "create-build-directory", "type": "shell", "command": "mkdir", "args": ["-p", "${workspaceFolder}/build"]}, {"label": "cmake", "type": "shell", "command": "cmake", "args": ["..", "-DCMAKE_INSTALL_PREFIX=/userdata", "-DCMAKE_BUILD_TYPE=Debug"], "options": {"cwd": "${workspaceFolder}/build"}, "dependsOn": ["create-build-directory"]}, {"label": "make", "type": "shell", "command": "make", "options": {"cwd": "${workspaceFolder}/build"}, "dependsOn": ["cmake"]}, {"label": "build-debug", "group": {"kind": "build", "isDefault": true}, "dependsOn": ["make"]}]}