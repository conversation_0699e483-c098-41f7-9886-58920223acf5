{"files.associations": {"fstream": "cpp", "sstream": "cpp", "array": "cpp", "atomic": "cpp", "*.tcc": "cpp", "cctype": "cpp", "chrono": "cpp", "clocale": "cpp", "cmath": "cpp", "condition_variable": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "unordered_map": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "map": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "set": "cpp", "string": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "ostream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "thread": "cpp", "typeinfo": "cpp", "bit": "cpp", "bitset": "cpp", "charconv": "cpp", "codecvt": "cpp", "regex": "cpp", "source_location": "cpp", "future": "cpp", "shared_mutex": "cpp", "cinttypes": "cpp", "variant": "cpp", "complex": "cpp", "forward_list": "cpp", "list": "cpp", "filesystem": "cpp", "unordered_set": "cpp", "span": "cpp"}, "C_Cpp.default.compilerPath": "/usr/bin/gcc"}