CMAKE_MINIMUM_REQUIRED(VERSION 3.10)
SET(CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH} ${CMAKE_CURRENT_SOURCE_DIR}/cmake)
# INCLUDE(build_for_rv1126)

project(mqttproc VERSION 0.0.2 LANGUAGES C CXX)

message(STATUS "System : ${CMAKE_SYSTEM_NAME}")
message(STATUS "Target system processor: ${CMAKE_SYSTEM_PROCESSOR}")
message(STATUS "Host architecture: ${CMAKE_HOST_SYSTEM_PROCESSOR}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")

# set(CMAKE_BUILD_TYPE "Release")
# set(CMAKE_BUILD_TYPE "Debug")
if (${CMAKE_BUILD_TYPE} MATCHES "Debug")
add_definitions(-DBACKTRACE_DEBUG)
endif ()

# Default to C99
if(NOT CMAKE_C_STANDARD)
  set(CMAKE_C_STANDARD 99)
endif()

# Default to C++11
if(NOT CMAKE_CXX_STANDARD)
  # set(CMAKE_CXX_STANDARD 11)
  set(CMAKE_CXX_STANDARD 17)
endif()

MESSAGE(STATUS ${CMAKE_SYSTEM_NAME})

# 执行git命令，并把结果重定向到自定义变量（此处是我的变量<GIT_VERSION>）。
execute_process(
	COMMAND	git log -1 --format=%H 		
	WORKING_DIRECTORY ${PROJECT_SOURCE_DIR} 
	OUTPUT_VARIABLE GIT_VERSION
)
execute_process(
	COMMAND git remote -v 			
	WORKING_DIRECTORY ${PROJECT_SOURCE_DIR} 
	OUTPUT_VARIABLE GIT_REMOTE_PATH
)

# 对得到的结果进行处理，尤其注意 \n,\t,\r之类的特殊字符，在cmake时没问题，但是生成的.cmake文件有问题，导致make出错。
string (REGEX REPLACE ".*\t| .*" "" GIT_PATH ${GIT_REMOTE_PATH})
string (REGEX REPLACE "[\n\t\r]" "" GIT_VERSION ${GIT_VERSION})

# 增加编译选项，把宏导入源码
add_definitions( -DGIT_VERSION=\"${GIT_VERSION}\")
add_definitions( -DGIT_PATH=\"${GIT_PATH}\")
add_definitions(-DPROJECT_PATH="${CMAKE_INSTALL_PREFIX}")
# add_definitions(-DPROJECT_PATH="/root/iot_3dprinter")


# IF (WIN32)
# 	MESSAGE(STATUS "Now is windows.")
#     add_subdirectory(iocp)
# ELSEIF (APPLE)
# 	MESSAGE(STATUS "Now is Apple systens.")
#     add_subdirectory(kqueue)
# ELSEIF (UNIX)
# 	MESSAGE(STATUS "Now is UNIX-like OS's.")
#     add_subdirectory(epoll)
# ENDIF ()


include_directories(
  include
  utils
  thirdparty/spdlog/src
  thirdparty/paho.mqtt.cpp/externals/paho-mqtt-c/src
  thirdparty/paho.mqtt.cpp
  thirdparty/paho.mqtt.cpp/src
  thirdparty/stduuid
)

file(GLOB main_srcs 
    src/*.cpp
    utils/*.cpp
)

add_subdirectory(thirdparty/lib_json)
add_subdirectory(thirdparty/spdlog)
set(PAHO_WITH_MQTT_C ON CACHE INTERNAL "")
set(PAHO_BUILD_SHARED OFF CACHE INTERNAL "")
set(PAHO_BUILD_STATIC ON CACHE INTERNAL "")
set(PAHO_ENABLE_TESTING OFF CACHE INTERNAL "")
set(PAHO_WITH_SSL ON CACHE INTERNAL "")
set(PAHO_HIGH_PERFORMANCE ON CACHE INTERNAL "")
set(PAHO_BUILD_EXAMPLES OFF CACHE INTERNAL "")
add_subdirectory(thirdparty/paho.mqtt.cpp)
add_subdirectory(thirdparty/stduuid)

set(EXENAME ${PROJECT_NAME}-v${PROJECT_VERSION}-${GIT_VERSION})
add_executable(${EXENAME} ${main_srcs})

target_link_libraries(${EXENAME} 
	pthread
	json
	spdlog
  paho-mqttpp3-static
  paho-mqtt3as-static
  stduuid
)

add_custom_target(create_symlink ALL
    COMMAND ${CMAKE_COMMAND} -E create_symlink ${EXENAME} ${PROJECT_NAME}
    COMMENT "Creating alias symlink for my_program"
)

add_dependencies(create_symlink ${EXENAME})

# if(PAHO_BUILD_STATIC)
#     install(TARGETS paho-mqttpp3-static DESTINATION ${CMAKE_INSTALL_PREFIX}/lib)
#     install(TARGETS paho-mqtt3as-static DESTINATION ${CMAKE_INSTALL_PREFIX}/lib)
# else()
#     install(TARGETS paho-mqttpp3-shared DESTINATION ${CMAKE_INSTALL_PREFIX}/lib)
#     install(TARGETS paho-mqtt3as DESTINATION ${CMAKE_INSTALL_PREFIX}/lib)
# endif()

install(TARGETS ${EXENAME}
    RUNTIME DESTINATION ${PROJECT_NAME})

# 安装软链接到 bin 目录
install(
    FILES ${CMAKE_BINARY_DIR}/${PROJECT_NAME}
    DESTINATION ${PROJECT_NAME}
)

install(DIRECTORY config
	DESTINATION ${PROJECT_NAME}/etc
)