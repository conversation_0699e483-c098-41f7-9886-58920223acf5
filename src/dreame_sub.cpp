#include <iostream>
#include "dreame_sub.h"
#include "jsonparse.h"
#include "logger.h"
#include "config.h"
#include <chrono>
#include "utils.h"

DreameSub::DreameSub(std::shared_ptr<Dreame3dMqtt> dreame3d_mqtt, std::shared_ptr<DreameUds> dreame_uds)
: dreame3d_mqtt_ptr_(dreame3d_mqtt), mqtt_recv_queue(nullptr), dreame_uds_ptr_(dreame_uds), klipper_handler_(nullptr), uds_server_queue(nullptr), ota_handler_(nullptr), ota_info(std::make_shared<OtaInfo>()),
gcode_handler_(new Gcode()), klipper_info_(std::make_shared<KlipperInfo>()), video_stream_handler_(new VideoStream()), print_task_handler_(new PrintTaskHandler()),
color_box_handler_(new ColorBoxHandler()), extFil_box_handler_(new ExtFilBoxHandler()), processing_printing(false)
{
    // 将服务端下发的method指令对应到个模块的业务处理函数
    method_map_func_["thing.service.machine_gcode.set"] = std::bind(&KlipperHandler::ScriptSet, klipper_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.move_axis.set"] = std::bind(&KlipperHandler::ScriptSet, klipper_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.move_reposition.set"] = std::bind(&KlipperHandler::ScriptSet, klipper_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.property.temp.set"] = std::bind(&KlipperHandler::CommandDispatch, klipper_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.property.temp.get"] = std::bind(&KlipperHandler::CommandDispatch, klipper_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.property.fan.set"] = std::bind(&KlipperHandler::CommandDispatch, klipper_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.property.fan.get"] = std::bind(&KlipperHandler::CommandDispatch, klipper_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.property.print.get"] = std::bind(&KlipperHandler::CommandDispatch, klipper_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.property.print.set"] = std::bind(&KlipperHandler::CommandDispatch, klipper_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.move_extruder.set"] = std::bind(&KlipperHandler::CommandDispatch, klipper_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.machine_hot_bed_leveling.set"] = std::bind(&KlipperHandler::CommandDispatch, klipper_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.print_clear.set"] = std::bind(&KlipperHandler::CommandDispatch, klipper_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["upload_client_error_code"] = std::bind(&KlipperHandler::HandleClientCode, klipper_handler_, std::placeholders::_1, std::placeholders::_2);

    method_map_func_["thing.service.devices_file_list_get.set"] = std::bind(&Gcode::FileSdList, gcode_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.devices_file_info_get.set"] = std::bind(&Gcode::FileInfo, gcode_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.devices_plate_info_get.set"] = std::bind(&Gcode::FilePlateInfo, gcode_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.devices_file_delete.set"] = std::bind(&Gcode::FileDelete, gcode_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["set_file_delete"] = std::bind(&Gcode::FileDelete, gcode_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.print_start.set"] = std::bind(&PrintTaskHandler::PrintTaskStart, print_task_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.print_pause.set"] = std::bind(&PrintTaskHandler::PrintTaskPause, print_task_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.print_resume.set"] = std::bind(&PrintTaskHandler::PrintTaskResume, print_task_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.print_cancel.set"] = std::bind(&PrintTaskHandler::PrintTaskStop, print_task_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.print_queue_start.set"] = std::bind(&PrintTaskHandler::HandlePrintQueue, print_task_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.print_queue_print_info.set"] = std::bind(&PrintTaskHandler::GetPrintTaskInfoDetail, print_task_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.print_queue_delete.set"] = std::bind(&PrintTaskHandler::DeletePrintTaskQueueTargetId, print_task_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.print_queue_adjust.set"] = std::bind(&PrintTaskHandler::PrintTaskQueueAdjust, print_task_handler_, std::placeholders::_1, std::placeholders::_2);

    method_map_func_["thing.property.machine.set"] = std::bind(&DreameSub::SetMachineInfo, this, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.property.machine.get"] = std::bind(&DreameSub::DeviceCheck, this, std::placeholders::_1, std::placeholders::_2);
    // method_map_func_["thing.property.account.set"] = std::bind(&DreameSub::AccountBindSet, this, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.property.account.fetch"] = std::bind(&DreameSub::AccountBindSet, this, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.property.light.get"] = std::bind(&DreameSub::GetLightChassis, this, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.property.light.set"] = std::bind(&DreameSub::SetLightChassis, this, std::placeholders::_1, std::placeholders::_2);

    method_map_func_["thing.property.ota.get"] = std::bind(&OtaHandler::OtaVersionGet, ota_handler_, std::placeholders::_1, std::placeholders::_2);
    // method_map_func_["thing.property.ota.set"] = std::bind(&OtaHandler::OtaVersionSet, ota_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.property.ota.fetch"] = std::bind(&OtaHandler::OtaVersionSet, ota_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.start_ota.set"] = std::bind(&OtaHandler::OtaUpgradeStart, ota_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.cancel_ota.set"] = std::bind(&OtaHandler::OtaUpgradeCancel, ota_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["get_new_ver"] = std::bind(&OtaHandler::GetNewVer, ota_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["set_ota_upgrade_progress"] = std::bind(&OtaHandler::SetOtaUpgradeProgress, ota_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["get_client_cur_ver"] = std::bind(&OtaHandler::HandleOtaVersionFromOtaProc, ota_handler_, std::placeholders::_1, std::placeholders::_2);

    method_map_func_["thing.service.start_video.set"] = std::bind(&VideoStream::StartStream, video_stream_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.service.end_video.set"] = std::bind(&VideoStream::StopStream, video_stream_handler_, std::placeholders::_1, std::placeholders::_2);

    method_map_func_["colorBox/loadedslot/get"] = std::bind(&ColorBoxHandler::ColorBoxLoadedSlot, color_box_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["colorBox/info/get"] = std::bind(&ColorBoxHandler::ColorBoxInfoGet, color_box_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["colorBox/dryer/set"] = std::bind(&ColorBoxHandler::ColorBoxDryerSet, color_box_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["colorBox/fil/feed/set"] = std::bind(&ColorBoxHandler::ColorBoxFilFeedSet, color_box_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["colorBox/fil/autoFeed/set"] = std::bind(&ColorBoxHandler::ColorBoxFilAutoFeedSet, color_box_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["colorBox/slot/refresh/set"] = std::bind(&ColorBoxHandler::ColorBoxSlotRefreshSet, color_box_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["colorBox/slot/info/set"] = std::bind(&ColorBoxHandler::ColorBoxSlotInfoSet, color_box_handler_, std::placeholders::_1, std::placeholders::_2);

    method_map_func_["thing.property.ext_fil_box.set"] = std::bind(&ExtFilBoxHandler::ExtFilBoxFilSet, extFil_box_handler_, std::placeholders::_1, std::placeholders::_2);
    method_map_func_["thing.property.ext_fil_box.get"] = std::bind(&ExtFilBoxHandler::ExtFilBoxFilGet, extFil_box_handler_, std::placeholders::_1, std::placeholders::_2);

    KlpperInfoInit();
    OtaInfoInit();
    light_chassis = 1;
    user_id_ = "";
    username_ = "";
}

void DreameSub::DeviceCheck(DreameSub* dreame_sub_ptr, Json::Value req_json){
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = "thing.property.machine.up";
    response["data"]["machine_online_state"] = 1;  // 1在线 2离线
    response["data"]["machine_state"] = klipper_info_->machine_state;
    response["data"]["machine_keep_alive_period"] = globalConfigObject["machine_keep_alive_period"].asInt();
    response["data"]["machine_open_lid_inspection"] = true; // TODO 开盖检查状态 待获取, 默认先返回true
    response["data"]["machine_is_homed"] = klipper_info_->machine_is_homed;
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

void DreameSub::UnbindUser(){
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["method"] = "thing.property.account.up";
    response["data"]["user_bind_info"]["user_id"] = "";
    response["data"]["user_bind_info"]["username"] = "";
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    dreame3d_mqtt_ptr_->UploadTopicMsg(response_string);
}

void DreameSub::AccountBindSet(DreameSub* dreame_sub_ptr, Json::Value req_json){
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = "thing.property.account.up";
    if (!req_json["data"].isMember("user_bind_info") || req_json["data"]["user_bind_info"].isNull()){
        std::cout << "user_bind_info is null" << std::endl;
    } else {
        user_id_ = req_json["data"]["user_bind_info"]["user_id"].asString();
        username_ = req_json["data"]["user_bind_info"]["username"].asString();
    }
    response["data"]["user_bind_info"]["user_id"] = user_id_;
    response["data"]["user_bind_info"]["username"] = username_;
    std::cout << "user_id: " << user_id_ << std::endl;
    std::cout << "username: " << username_ << std::endl;
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

std::string DreameSub::KlipperDataJsonParse(Json::Value req_json) {
    Json::Value send_data;
    send_data["id"] = req_json["id"].asString();
    send_data["src"] = "mqtt_proc";
    send_data["des"] = "klippy_proc";
    send_data["method"] = req_json["method"].asString();
    if (req_json["data"].isObject()) {
        send_data["params"] = req_json["data"];
    } else if (req_json["data"].isNull()) {
        send_data["params"] = Json::Value(Json::objectValue);
    } else if (req_json["data"].isArray()){
        send_data["params"]["data"] = req_json["data"];
    } else {
        send_data["params"] = Json::Value(Json::objectValue);
    }
    std::string send_data_string;
    JsoncppParseRead::ParseJsonToString(send_data_string, send_data);
    return send_data_string;
}

std::string DreameSub::VideoDataJsonParse(Json::Value req_json) {
    Json::Value send_data;
    send_data["id"] = req_json["id"].asString();
    send_data["src"] = "mqtt_proc";
    send_data["des"] = "cam_proc";
    send_data["method"] = req_json["method"].asString();
    if (req_json["data"].isObject()) {
        send_data["params"] = req_json["data"];
    } else if (req_json["data"].isNull()) {
        send_data["params"] = Json::Value(Json::objectValue);
    } else {
        send_data["params"] = Json::Value(Json::objectValue);
    }
    std::string send_data_string;
    JsoncppParseRead::ParseJsonToString(send_data_string, send_data);
    return send_data_string;
}

void DreameSub::SetMachineInfo(DreameSub* dreame_sub_ptr, Json::Value req_json) {
    int period = req_json["data"]["period"].asInt();
    if (period == 0){
        // 设置为0的时候恢复默认值20
        period = 20;
    }
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    if (req_json["data"].isMember("machine_keep_alive_period")){
        SetMachineKeepAlivePeriod(req_json["data"]["machine_keep_alive_period"].asInt());
    }
    if (req_json["data"].isMember("machine_open_lid_inspection")){
        // TODO 设置开盖状态 req_json["data"]["machine_open_lid_inspection"].asBool()
    }
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

void DreameSub::GetUserBindInfo(DreameSub* dreame_sub_ptr) {
    Json::Value response;
    std::string response_string;
    response = Utils::GetMqttResponseJson();
    // fetch的时候服务端会忽略传给服务端的user_id和username
    response["method"] = "thing.property.account.fetch";
    Json::Value data_array;
    data_array.append("user_bind_info"); 
    response["data"] = data_array;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

std::shared_ptr<Dreame3dMqtt>* DreameSub::getMqttPtr() {
    return &dreame3d_mqtt_ptr_;
}

std::shared_ptr<DreameUds>* DreameSub::getDreameUdsPtr() {
    return &dreame_uds_ptr_;
}

void DreameSub::KlpperInfoInit() {
    klipper_info_->machine_state = 1;
    klipper_info_->klipper_state = "standby"; // ready, standby, printing, paused, shutdown, error
    klipper_info_->update_time = Utils::GetCurrentMsTime();
    klipper_info_->state = ""; // ready, standby, printing, paused, shutdown, error
    klipper_info_->machine_is_homed = false;
}

void DreameSub::OtaInfoInit() {
    ota_info->printer_ver = "";
    ota_info->printer_url = "";
    ota_info->printer_md5 = "";
    ota_info->acf_box_ver = "";
    ota_info->acf_box_url = "";
    ota_info->acf_box_md5 = "";
    ota_info->status = 0;
    ota_info->progress = 0;
    ota_info->latest_ota_version = Json::Value(Json::objectValue);
    ota_info->current_ota_version = Json::Value(Json::objectValue);
}

DreameSub::~DreameSub() {
    if (!topic_.empty()) {
        std::cout << "Mqtt delete topic " << topic_ << std::endl;
        dreame3d_mqtt_ptr_->DelTopic(topic_.c_str());
    }
    if (recvMqttMsgQueueThread.joinable()) {
        recvMqttMsgQueueThread.join();
    }
    if (handleRecvMainprocMsgQueueThread.joinable()) {
        handleRecvMainprocMsgQueueThread.join();
    }
    if (handleHeartbeatMsgThread.joinable()) {
        handleHeartbeatMsgThread.join();
    }
}

void DreameSub::SubMsgArrived(mqtt::const_message_ptr &msg)
{
    std::unique_lock<std::mutex> lck(g_mtx_);
    std::string control_str;
    control_str = msg->to_string();
    std::string control_str_msg;
    control_str_msg = "Recv from server: ";
    control_str_msg += control_str;
    std::cout << control_str_msg << std::endl;
    LOG_INFO(control_str_msg);
    Json::Value control_json;
    JsoncppParseRead::ReadStringToJson(control_str, control_json);
    // 订阅的消息都放入循环队列中
    mqtt_recv_queue->push(control_str);
}

void DreameSub::processRecvMqttMsgQueue() {
    while (true) {
        try {
            if (!mqtt_recv_queue->empty()) {
                std::string result;
                Json::Value recv_json;
                bool parse_success = false;

                // 队列操作和JSON解析的异常保护
                try {
                    result = mqtt_recv_queue->front();
                    mqtt_recv_queue->pop();
                    parse_success = JsoncppParseRead::ReadStringToJson(result, recv_json);
                } 
                catch (const std::exception& e) {
                    // 日志记录：队列操作或JSON解析异常
                    std::cerr << "processRecvMqttMsgQueue Queue/JSON error: " << e.what() << std::endl;
                    continue;
                }
                catch (...) {
                    std::cerr << "Unknown processRecvMqttMsgQueue queue/JSON error" << std::endl;
                    continue;
                }

                // 处理有效JSON
                if (parse_success && recv_json.isObject()) {
                    std::string method = recv_json.get("method", "").asString();
                    auto it = method_map_func_.find(method);
                    
                    if (it != method_map_func_.end()) {
                        auto execute_handler = [this, it](const Json::Value& json) {
                            // 统一异常处理函数
                            try {
                                it->second(this, json);
                            }
                            catch (const std::exception& e) {
                                std::cerr << "processRecvMqttMsgQueue Handler error: " << e.what() << std::endl;
                            }
                            catch (...) {
                                std::cerr << "Unknown processRecvMqttMsgQueue handler error" << std::endl;
                            }
                        };

                        // 由于下载gcode会比较耗时会阻塞 所以单独起一个线程执行该函数, 不阻塞主线程
                        if (method == "thing.service.print_start.set") {
                            std::thread([execute_handler, recv_json]() {
                                execute_handler(recv_json);
                            }).detach();
                        } else {
                            execute_handler(recv_json);
                        }
                    }
                }
            }
        }
        catch (const std::exception& e) {
            std::cerr << "processRecvMqttMsgQueue Main loop error: " << e.what() << std::endl;
        }
        catch (...) {
            std::cerr << "Unknown processRecvMqttMsgQueue main loop error" << std::endl;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

bool DreameSub::HandleFromKlippyProcMsg(Json::Value recv_json){
    // klipper不可用的时候会返回error相关的字段
    if (recv_json["src"] == "klippy_proc" && recv_json["des"] == "mqtt_proc" && recv_json.isMember("error")) {
        std::string error = recv_json["error"]["error"].asString();
        std::string message = recv_json["error"]["message"].asString();
        std::string task_id = recv_json["id"].asString();
        std::string msg = " klippy_proc error: " + error + " " + message;
        std::cout << msg << std::endl;
        LOG_INFO(msg);
        Json::Value response;
        response = Utils::GetMqttResponseJson();
        Json::Value task_info = GetTaskIDInfo(task_id);
        if (task_info["task_id"].asString() == task_id) {
            const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::KlipperCommandError);
            response["id"] = task_id;
            response["method"] = task_info["method"].asString();
            response["code"] = error.code;
            response["message"] = error.message + msg;
            std::string response_string;
            JsoncppParseRead::ParseJsonToString(response_string, response);
            dreame3d_mqtt_ptr_->UploadTopicMsg(response_string);
        }
        DeleteTaskID(task_id);
        return true;
    }
    // 将klipper回复的数据放入要上传的mainproc_msg_list_数组中
    if (recv_json["src"] == "klippy_proc" && recv_json["des"] == "mqtt_proc") {
        // 清理已处理的task任务数据数据
        DeleteTaskID(recv_json["id"].asString());
        std::string result_str("");
        if (recv_json.isMember("result") && recv_json["result"]["method"].isString()){
            // 清除不需要上报的字段
            recv_json["result"].removeMember("src");
            recv_json["result"].removeMember("des");
            JsoncppParseRead::ParseJsonToString(result_str, recv_json["result"]);
        } else {
            recv_json.removeMember("src");
            recv_json.removeMember("des");
            JsoncppParseRead::ParseJsonToString(result_str, recv_json);
        }
        // 特殊处理查询来自klipper的状态数据
        if (recv_json.isMember("result") && recv_json["result"]["method"].asString()=="get.klipper.info") {
            // 处理心跳数据
            dreame3d_mqtt_ptr_->UploadHeartbeatMsg(result_str);
            return true;
        }
        // 正常处理数据
        dreame3d_mqtt_ptr_->UploadTopicMsg(result_str);
    }
    return false;
}

bool DreameSub::HandleFromCamProcMsg(Json::Value recv_json){
    if (recv_json["src"] == "cam_proc" && recv_json["des"] == "mqtt_proc") {
        // 清理已处理的task任务数据数据
        DeleteTaskID(recv_json["id"].asString());
        std::string result_str("");
        Json::Value response = Utils::GetMqttResponseJson();
        response["id"] = recv_json["id"].asString();
        response["method"] = recv_json["method"].asString();
        if (recv_json["method"].asString() == "set_video_push" && recv_json["result"]["action"].asString() == "START"){
            std::cout << "1" << std::endl;
            // 处理开始推流的回复
            response["method"] = "thing.service.start_video.set";
            if (recv_json["result"]["sta"].asString() == "false") {
                const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::StartStreamError);
                response["code"] = error.code;
                response["message"] = error.message + " " + recv_json["result"]["message"].asString();
            }
        } else if (recv_json["method"].asString() == "set_video_push" && recv_json["result"]["action"].asString() == "STOP"){
            // 处理结束推流的回复
            if (recv_json["result"]["sta"].asString() == "false") {
                const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::StopStreamError);
                response["code"] = error.code;
                response["message"] = error.message + " " + recv_json["result"]["message"].asString();
            }
            response["method"] = "thing.service.end_video.set";
        }
        JsoncppParseRead::ParseJsonToString(result_str, response);
        dreame3d_mqtt_ptr_->UploadTopicMsg(result_str);
    }
    return false;
}

bool DreameSub::HandleFromOtaProcMsg(Json::Value recv_json){
    if (recv_json["src"] == "ota_proc" && recv_json["des"] == "mqtt_proc") {
        // 清理已处理的task任务数据数据
        DeleteTaskID(recv_json["id"].asString());
        if (recv_json.isObject()) {
            recv_json.removeMember("src");
            recv_json.removeMember("des");
            std::string method = recv_json.get("method", "").asString();
            // 处理ota进程推送过来的数据 如method 为set_ota_upgrade_progress的函数处理
            auto it = method_map_func_.find(method);
            if (it != method_map_func_.end()) {
                auto execute_handler = [this, it](const Json::Value& json) {
                    // 统一异常处理函数
                    try { it->second(this, json); }
                    catch (const std::exception& e) { 
                        std::string err_msg = "HandleFromOtaProcMsg Handler error: " + std::string(e.what());
                        std::cerr << err_msg << std::endl; 
                        LOG_ERROR(err_msg);
                    }
                    catch (...) { 
                        std::string err_msg = "HandleFromOtaProcMsg Unknown handler error";
                        std::cerr << err_msg << std::endl; 
                        LOG_ERROR(err_msg);
                    }
                };
                execute_handler(recv_json);
            }
        }
    }
    return false;
}

bool DreameSub::HandleFromGuiProcMsg(Json::Value recv_json){
    if (recv_json["src"] == "gui_proc" && recv_json["des"] == "mqtt_proc") {
        // 清理已处理的task任务数据数据
        DeleteTaskID(recv_json["id"].asString());
        if (recv_json.isObject()) {
            recv_json.removeMember("src");
            recv_json.removeMember("des");
            std::string method = recv_json.get("method", "").asString();
            // 处理gui进程推送过来的数据 如method 为set_gui_upgrade_progress的函数处理
            auto it = method_map_func_.find(method);
            if (it != method_map_func_.end()) {
                auto execute_handler = [this, it](const Json::Value& json) {
                    // 统一异常处理函数
                    try { it->second(this, json); }
                    catch (const std::exception& e) { 
                        std::string err_msg = "HandleFromOtaProcMsg Handler error: " + std::string(e.what());
                        std::cerr << err_msg << std::endl; 
                        LOG_ERROR(err_msg);
                    }
                    catch (...) { 
                        std::string err_msg = "HandleFromOtaProcMsg Unknown handler error";
                        std::cerr << err_msg << std::endl; 
                        LOG_ERROR(err_msg);
                    }
                };
                execute_handler(recv_json);
            }
        }
    }
    return false;
}

bool DreameSub::HandleFromAcfBoxProcMsg(Json::Value recv_json){
    if (recv_json["src"] == "acf_box" && recv_json["des"] == "mqtt_proc") {
        // 清理已处理的task任务数据数据
        DeleteTaskID(recv_json["id"].asString());
        std::string result_str("");
        recv_json.removeMember("src");
        recv_json.removeMember("des");
        JsoncppParseRead::ParseJsonToString(result_str, recv_json);
        dreame3d_mqtt_ptr_->UploadTopicMsg(result_str);
    }
    return false;
}

void DreameSub::processRecvMainprocMsgQueue() {
    Json::Value recv_json(Json::objectValue);
    // 使用lambda统一处理业务逻辑异常
    auto safe_handler = [](auto&& handler, auto&&... args) {
        try {
            handler(args...);
        } catch (const std::exception& e) {
            std::cerr << "Handler error: " << e.what() << std::endl;
        } catch (...) {
            std::cerr << "Unknown handler error" << std::endl;
        }
    };
    while (true) {
        try {
            // 主循环异常保护层
            if (!uds_server_queue->empty()) {
                try {
                    // 队列操作异常保护层
                    std::unique_lock<std::mutex> lck(dreame_uds_ptr_->g_mtx);
                    // 再次检查队列状态 防御
                    if (uds_server_queue->empty()) {
                        lck.unlock();
                        continue;
                    }
                    Result result;
                    try {
                        result = uds_server_queue->front();
                        uds_server_queue->pop();
                    } catch (const std::exception& e) {
                        std::cerr << "Queue operation failed: " << e.what() << std::endl;
                        continue;
                    } catch (...) {
                        std::cerr << "Unknown queue operation error" << std::endl;
                        continue;
                    }
                    // 日志记录
                    try {
                        std::string output_msg = "Recv Data From MainProc fd:" + 
                                               std::to_string(result.fd) + 
                                               " result:" + result.recv_str;
                        // 不打印频繁请求klipper的信息
                        if (result.recv_str.find("get.klipper.info") == std::string::npos) {
                            LOG_INFO(output_msg);
                            std::cout << output_msg << std::endl;
                        }
                        // LOG_INFO(output_msg);
                    } catch (...) {
                        std::cerr << "Logging failed" << std::endl;
                    }
                    // JSON解析
                    bool parse_success = false;
                    recv_json.clear();
                    try {
                        parse_success = JsoncppParseRead::ReadStringToJson(result.recv_str, recv_json);
                    } catch (const std::exception& e) {
                        std::cerr << "JSON parse error: " << e.what() << std::endl;
                        continue;
                    } catch (...) {
                        std::cerr << "Unknown JSON parse error" << std::endl;
                        continue;
                    }
                    // 消息处理
                    if (parse_success && recv_json.isObject()) {
                        try {
                            bool is_continue = false;
                            safe_handler([&](){ is_continue = HandleFromKlippyProcMsg(recv_json); });
                            if (is_continue) continue;
                            safe_handler([&](){ HandleFromCamProcMsg(recv_json); });
                            safe_handler([&](){ HandleFromOtaProcMsg(recv_json); });
                            safe_handler([&](){ HandleFromAcfBoxProcMsg(recv_json); });
                            safe_handler([&](){ HandleFromGuiProcMsg(recv_json); });
                        } catch (...) {
                            std::cerr << "Unknown handle error" << std::endl;
                        }
                    }
                } catch (const std::exception& e) {
                    std::cerr << "Critical section error: " << e.what() << std::endl;
                } catch (...) {
                    std::cerr << "Unknown critical section error" << std::endl;
                }
            } else {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
            // 判断连接上mqtt服务端(包括断网重连)和klipper服务端后 触发一次各项状态上报通知mqtt服务端
            try {
                if (dreame3d_mqtt_ptr_ && dreame3d_mqtt_ptr_->report_state &&
                   (*this->getDreameUdsPtr())->getClientSockFd() != -1) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
                    dreame3d_mqtt_ptr_->report_state = false;
                    safe_handler([&](){ klipper_handler_->UploadStatusNow(this); });
                    safe_handler([&](){ klipper_handler_->UploadMachineState(this); });
                    Json::Value response;
                    response["id"] = Utils::CreateUUID();
                    safe_handler([&](){ this->GetUserBindInfo(this); });
                    safe_handler([&](){ this->DeviceCheck(this, response); });
                    ota_handler_->GetClientCurrentOtaVersion(this);
                }
            } catch (...) {
                std::cerr << "Status report failed" << std::endl;
            }
        } catch (const std::exception& e) {
            std::cerr << "Main loop error: " << e.what() << std::endl;
        } catch (...) {
            std::cerr << "Unknown main loop error" << std::endl;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
}

void DreameSub::processHandleHeartbeatMsg() {
    int64_t last_timer = Utils::GetCurrentMsTime();
    int64_t heartbeat_last_timer = Utils::GetCurrentMsTime();
    // 使用lambda统一处理业务逻辑异常
    auto safe_handler = [this](auto&& func) {
        try {
            func();
        } catch (const std::exception& e) {
            LOG_ERROR(std::string("Handler error: ") + e.what());
        } catch (...) {
            LOG_ERROR("Unknown handler error");
        }
    };
    while (true) {
        try {
            int64_t cur_timer = Utils::GetCurrentMsTime();
            int64_t heartbeat_cur_timer = Utils::GetCurrentMsTime();
            // 1秒定时任务
            if (cur_timer - last_timer >= 1000) {
                last_timer = cur_timer;
                safe_handler([&]() {
                    std::unique_lock<std::mutex> lck(dreame3d_mqtt_ptr_->heartbeat_msg_g_mtx);
                    // 防御性检查
                    if (dreame3d_mqtt_ptr_->mainproc_heartbeat_msg_list.empty()) return;
                    // 处理心跳消息
                    auto& msg = dreame3d_mqtt_ptr_->mainproc_heartbeat_msg_list[0];
                    Json::Value mainproc_heartbeat_msg_json;
                    // JSON解析保护
                    bool parse_success = false;
                    try {
                        parse_success = JsoncppParseRead::ReadStringToJson(msg, mainproc_heartbeat_msg_json);
                    } catch (...) {
                        LOG_ERROR("JSON parse failed");
                    }
                    if (parse_success && mainproc_heartbeat_msg_json["method"].asString() == "get.klipper.info") {
                        // 状态更新
                        safe_handler([&]() {
                            int last_machine_state = klipper_info_->machine_state;
                            klipper_info_->machine_state = mainproc_heartbeat_msg_json["data"].get("machine_state", 4).asInt();
                            klipper_info_->klipper_state = mainproc_heartbeat_msg_json["data"].get("klipper_state", "").asString();
                            klipper_info_->machine_is_homed = mainproc_heartbeat_msg_json["data"].get("homed_axes", false).asBool();
                            klipper_info_->update_time = Utils::GetCurrentMsTime();
                            // 上次的状态和新收到的状态不一致的话, 上报一次
                            if (last_machine_state != klipper_info_->machine_state) {
                                safe_handler([&]() { klipper_handler_->UploadMachineState(this); });
                            }
                        });
                    }
                    // 清空队列
                    dreame3d_mqtt_ptr_->mainproc_heartbeat_msg_list.clear();
                });
            }
            // 10s超时检测, 认为klipper离线
            if (cur_timer - klipper_info_->update_time > 10000 && klipper_info_->machine_state != 4) {
                safe_handler([&]() {
                    klipper_info_->machine_state = 4;
                    klipper_handler_->UploadMachineState(this);
                });
            }
            // 3秒定时任务 获取klipper信息
            if (heartbeat_cur_timer - heartbeat_last_timer >= 3000) {
                heartbeat_last_timer = heartbeat_cur_timer;
                safe_handler([&]() {
                    // 检测灯光状态
                    safe_handler([&]() { CheckLightChassis(); });
                    safe_handler([&]() { klipper_handler_->GetKlipperInfo(this); });
                    // 处理超时任务
                    Json::Value response;
                    safe_handler([&]() { response = DeleteTimeoutTaskID(); });
                    if (response.get("task_id", "").asString() != "") {
                        safe_handler([&]() {
                            const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::CommandTimeout);
                            Json::Value upload_response = Utils::GetMqttResponseJson();
                            upload_response["id"] = response["task_id"].asString();
                            upload_response["method"] = response.get("method", "").asString();
                            upload_response["code"] = error.code;
                            upload_response["message"] = error.message;
                            std::string upload_response_string;
                            if (JsoncppParseRead::ParseJsonToString(upload_response_string, upload_response)) {
                                dreame3d_mqtt_ptr_->UploadTopicMsg(upload_response_string);
                            } else {
                                LOG_ERROR("Failed to serialize timeout response");
                            }
                        });
                    }
                });
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        } catch (const std::exception& e) {
            LOG_ERROR(std::string("Main loop error: ") + e.what());
        } catch (...) {
            LOG_ERROR("Unknown main loop error");
        }
    }
}

void DreameSub::SubTopic(std::string topic)
{
    topic_ = topic;
    dreame3d_mqtt_ptr_->AddCallback(topic_, QOS0, std::bind(&DreameSub::SubMsgArrived, this, std::placeholders::_1));
}

bool DreameSub::Init(CircularQueue<std::string> &queue)
{
    mqtt_recv_queue = &queue;
    // 将dreame_uds_ptr_的队列放到外部来
    uds_server_queue = dreame_uds_ptr_->getQueue();
    recvMqttMsgQueueThread = std::thread(&DreameSub::processRecvMqttMsgQueue, this);
    handleRecvMainprocMsgQueueThread = std::thread(&DreameSub::processRecvMainprocMsgQueue, this);
    handleHeartbeatMsgThread = std::thread(&DreameSub::processHandleHeartbeatMsg, this);
    return true;
}

int DreameSub::AddTaskID(std::string task_id, std::string method, int timeout)
{
    // timeout 是传入指定多少s后此任务就算超时
    std::unique_lock<std::mutex> lck(task_id_g_mtx);
    Json::Value task_id_json;
    task_id_json["task_id"] = task_id;
    task_id_json["method"] = method;
    task_id_json["timeout"] = (int)Utils::getUptimeInSeconds() + timeout;
    std::string msg;
    JsoncppParseRead::ParseJsonToString(msg, task_id_json);
    task_id_list.push_back(task_id_json);
    msg = "AddTaskID: " + msg + " task_id_list size:" + std::to_string(task_id_list.size());
    LOG_INFO(msg);
    std::cout << msg << std::endl;
    return task_id_list.size();
}

int DreameSub::DeleteTaskID(std::string task_id) {
    std::unique_lock<std::mutex> lck(task_id_g_mtx);
    if (task_id_list.size() == 0) {
        return 0;
    }
    for (uint32_t i = 0; i < task_id_list.size(); ++i) {
        if (task_id_list[i]["task_id"].asString() == task_id) {
            std::string msg = "DeleteTaskID: " + task_id + " method: "+ task_id_list[i]["method"].asString();
            LOG_INFO(msg);
            std::cout << msg << std::endl;
            task_id_list.erase(task_id_list.begin() + i);
            break;
        }
    }
    return task_id_list.size();
}

Json::Value DreameSub::DeleteTimeoutTaskID(){
    std::unique_lock<std::mutex> lck(task_id_g_mtx);
    Json::Value response;
    if (task_id_list.size() == 0) {
        return response;
    }
    for (uint32_t i = 0; i < task_id_list.size(); ++i) {
        if (task_id_list[i]["timeout"].asInt() < (int)Utils::getUptimeInSeconds()) {
            std::string msg = "DeleteTimeoutTaskID: " + task_id_list[i]["task_id"].asString() + " method: "+ task_id_list[i]["method"].asString();
            LOG_INFO(msg);
            std::cout << msg << std::endl;
            response = task_id_list[i];
            task_id_list.erase(task_id_list.begin() + i);
            break;
        }
    }
    return response;
}

Json::Value DreameSub::GetTaskIDInfo(std::string task_id){
    std::unique_lock<std::mutex> lck(task_id_g_mtx);
    Json::Value response;
    if (task_id_list.size() == 0) {
        return response;
    }
    for (uint32_t i = 0; i < task_id_list.size(); ++i) {
        if (task_id_list[i]["task_id"].asString() == task_id) {
            std::string msg = "GetTaskIDInfo: " + task_id_list[i]["task_id"].asString() + " method: "+ task_id_list[i]["method"].asString();
            LOG_INFO(msg);
            std::cout << msg << std::endl;
            response = task_id_list[i];
            break;
        }
    }
    return response;
}

void DreameSub::CheckLightChassis() {
    if (Utils::FileExists(globalConfigObject["brightness_path"].asString())) {
        int last_light_chassis = Utils::GetLightChassis();
        if (last_light_chassis != light_chassis) {
            light_chassis = last_light_chassis;
            Json::Value response;
            response = Utils::GetMqttResponseJson();
            response["method"] = "thing.property.light.up";
            response["data"]["light_chassis"] = light_chassis;
            std::string response_string;
            JsoncppParseRead::ParseJsonToString(response_string, response);
            dreame3d_mqtt_ptr_->UploadTopicMsg(response_string);
        }
    }
}

void DreameSub::GetLightChassis(DreameSub* dreame_sub_ptr, Json::Value req_json) {
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = "thing.property.light.up";
    if (!Utils::FileExists(globalConfigObject["brightness_path"].asString())) {
        const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::FileNotExist);
        response["code"] = error.code;
        response["message"] = error.message + " " + globalConfigObject["brightness_path"].asString();
    } else {
        int last_light_chassis = Utils::GetLightChassis();
        light_chassis = last_light_chassis;
        response["data"]["light_chassis"] = light_chassis;
    }
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

void DreameSub::SetLightChassis(DreameSub* dreame_sub_ptr, Json::Value req_json) {
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    int light_chassis = req_json["data"]["light_chassis"].asInt();
    if (Utils::FileExists(globalConfigObject["brightness_path"].asString())) {
        std::string recv;
        std::string cmd = "echo " + std::to_string(light_chassis) + " > " + globalConfigObject["brightness_path"].asString();
        Utils::Execute(cmd, recv);
        Utils::Execute("sync", recv);
    } else {
        const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::FileNotExist);
        response["code"] = error.code;
        response["message"] = error.message + " " + globalConfigObject["brightness_path"].asString();
    }
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

int DreameSub::AddPrintTask(Json::Value print_task) {
    std::unique_lock<std::mutex> lck(print_task_queue_g_mtx);
    print_task_queue_list.push_back(print_task);
    return print_task_queue_list.size();
}

bool DreameSub::DeletePrintTask(Json::Value print_task) {
    std::unique_lock<std::mutex> lck(print_task_queue_g_mtx);
    for (int i = print_task_queue_list.size() - 1; i >= 0; --i) {
        LOG_INFO("DeletePrintTask: " + print_task["data"]["task_info_id"].asString());
        std::string msg = "print_task_queue_list[" + std::to_string(i) + "]: " + print_task_queue_list[i].toStyledString();
        LOG_INFO(msg);
        if (print_task_queue_list[i]["data"]["print_task_info"]["task_info_id"].asString() == print_task["data"]["task_info_id"].asString()) {
            print_task_queue_list.erase(print_task_queue_list.begin() + i);
            return true;
        }
    }
    return false;
}

Json::Value DreameSub::GetFirstPrintTask() {
    std::unique_lock<std::mutex> lck(print_task_queue_g_mtx);
    if (print_task_queue_list.empty()) {
        return Json::Value(Json::objectValue);
    }
    Json::Value print_task = print_task_queue_list.front();
    print_task_queue_list.erase(print_task_queue_list.begin());
    return print_task;
}

bool DreameSub::AdjustPrintTaskQueue(Json::Value req_json) {
    std::unique_lock<std::mutex> lck(print_task_queue_g_mtx);
    if (print_task_queue_list.empty() || req_json["data"]["new_print_queue_list"].size() != print_task_queue_list.size()) {
        std::string msg = "AdjustPrintTaskQueue failed, print_task_queue_list size: " + std::to_string(print_task_queue_list.size()) + " req_json size: " + std::to_string(req_json["data"]["new_print_queue_list"].size());
        std::cout << msg << std::endl;
        LOG_ERROR(msg);
        return false;
    }
    std::vector<Json::Value> new_print_task_queue_list;
    for (int i = 0; i < req_json["data"]["new_print_queue_list"].size(); ++i) {
        for (int j = 0; j < print_task_queue_list.size(); ++j) {
            if (print_task_queue_list[j]["data"]["print_task_info"]["task_info_id"].asString() == req_json["data"]["new_print_queue_list"][i].asString()) {
                new_print_task_queue_list.push_back(print_task_queue_list[j]);
                break;
            }
        }
    }
    print_task_queue_list = new_print_task_queue_list;
    return true;
}
