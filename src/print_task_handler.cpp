/**
 * @file print_task_handler.cpp
 * <AUTHOR> (guo<PERSON><EMAIL>)
 * @brief
 * @version 0.1
 * @date 2025-04-03
 * @copyright Copyright (c) {2025} 追觅科技有限公司版权所有
 */

#include "utils.h"
#include <fstream>
#include <filesystem> 
#include <future> // Required for std::promise and std::future
#include "print_task_handler.h"

PrintTaskHandler::PrintTaskHandler() : print_task_info(std::make_shared<PrintTaskInfo>()) {
    print_task_info->printId = "";
    print_task_info->filename = "";
    print_task_info->currentLayer = 0;
    print_task_info->totalLayer = 0;
    print_task_info->printProgress = 0;
    print_task_info->printDuration = 0;
    print_task_info->remainTime = 0;
    print_task_info->suppliesUsage = 0;
}

PrintTaskHandler::~PrintTaskHandler() {}

void PrintTaskHandler::PrintTaskStart(DreameSub* dreame_sub_ptr, Json::Value req_json) {
    Json::Value response;
    std::string response_string;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    int machine_state = dreame_sub_ptr->klipper_info_->machine_state;
    LOG_INFO("PrintTaskStart processing_printing: " + std::to_string(dreame_sub_ptr->processing_printing.load(std::memory_order_relaxed)) + 
             " machine_state: " + std::to_string(machine_state) + 
             " print_from_queue: " + std::to_string(req_json.isMember("print_from_queue")));
    // processing_printing=true正在处理当前打印, machine_state==MACHINE_PRINTING正在打印, print_task_queue_listda打印队列有数据
    // 满足上面3中情况时, 将打印任务加入队列
    if (dreame_sub_ptr->processing_printing.load(std::memory_order_relaxed) || 
        machine_state == MACHINE_PRINTING ||
        !dreame_sub_ptr->print_task_queue_list.empty()) {
        if (req_json.isMember("print_from_queue") && machine_state != MACHINE_PRINTING) {
            // 特殊处理来自gui_proc或APP端调用下一个打印任务的请求 print_from_queue=true 表示打印队列中的排在前面的第一个任务
            // 其它情况继续走入队处理
        } else {
            dreame_sub_ptr->AddPrintTask(req_json);
            std::string response_string;
            JsoncppParseRead::ParseJsonToString(response_string, response);
            (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
            // 入队后 上报新的列表数据
            std::this_thread::sleep_for(std::chrono::seconds(2));
            GetPrintTaskQueueList(dreame_sub_ptr, req_json);
            return;
        }
    }
    // processing_printing是用来标识PrintTaskStart的处理过程是否处于正在处理中, 正在处理中的话, 有新的打印任务进来, 则先入队
    // 否则将标识改为正在处理中
    if (!dreame_sub_ptr->processing_printing.load(std::memory_order_relaxed)) {
        dreame_sub_ptr->processing_printing.store(true, std::memory_order_relaxed);
    }
    // 清除临时字段
    if (req_json.isMember("print_from_queue")) {
        req_json.removeMember("print_from_queue");
    }
    // 校验传参问题
    if (req_json["data"]["print_task_info"]["file_type"].asInt() == 0) {
        const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::PrintError);
        if (req_json["data"]["print_task_info"]["file_name"].asString() == "") {
            response["code"] = error.code;
            response["message"] = error.message + " file_name is empty";
        }
        else if (req_json["data"]["print_task_info"]["file_key"].asString() == "") {
            response["code"] = error.code;
            response["message"] = error.message + " file_key is empty";
        }
    }
    else if (req_json["data"]["print_task_info"]["file_type"].asInt() == 1 && req_json["data"]["print_task_info"]["file_path"].asString() == "") {
        const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::PrintError);
        response["code"] = error.code;
        response["message"] = error.message + " file_path is empty";
    }
    if (req_json["data"]["print_task_info"]["file_type"].asInt() == 1 && req_json["data"]["print_task_info"]["file_path"].asString() != "") {
        // 打印本地文件时 校验本地文件是否存在
        std::string local_file_path = globalConfigObject["gcode_file_dir"].asString() + "/" + req_json["data"]["print_task_info"]["file_path"].asString();
        if (req_json["data"]["print_task_info"]["file_path"].asString().substr(0, 6) == "local/") {
            local_file_path = globalConfigObject["gcode_file_dir"].asString() + "/" + req_json["data"]["print_task_info"]["file_path"].asString().substr(6);
        }
        if (req_json["data"]["print_task_info"]["file_path"].asString().substr(0, 5) == "udisk") {
            // TODO 暂时屏蔽U盘打印
            const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::FileNotExist);
            response["code"] = error.code;
            response["message"] = error.message;
        }

        // 本地打印 判断是否为3mf文件
        if (local_file_path.size() >= 4 && local_file_path.substr(local_file_path.size() - 4) == ".3mf") {
            // 如果是3mf文件，解压3mf，提取gcode
            std::string filename = local_file_path.substr(local_file_path.find_last_of('/') + 1);
            filename = filename.substr(0, filename.size() - 4); // 去掉.3mf
            std::string plate_index = req_json["data"]["print_task_info"]["plate_index"].asString();
            std::string extract_dir = globalConfigObject["gcode_file_dir"].asString() + "/" + "." + filename + ".3mf"; // New: /.xxx.3mf

            // 解压前，检查文件夹是否存在
            if (!Utils::DirectoryExists(extract_dir)) {
                // 文件夹不存在，则解压3mf
                std::string extract_cmd = "7z x '" + local_file_path + "' -o'" + extract_dir + "' > /dev/null 2>&1";
                std::cout << "unzipping: " << extract_cmd << std::endl;
                std::string recv;
                Utils::Execute(extract_cmd, recv);
            }
            // 对解压路径内的Metadata目录下的所有文件名加前缀
            std::string metadata_dir = extract_dir + "/Metadata/";
            if (Utils::DirectoryExists(metadata_dir)) {
                std::vector<std::string> files;
                Utils::getFiles(metadata_dir, files);

                for (const auto& file_path : files) {
                    size_t last_slash = file_path.find_last_of('/');
                    std::string dir = file_path.substr(0, last_slash);
                    std::string old_name = file_path.substr(last_slash + 1);
                    // 如果文件名没有前缀，则加上前缀
                    if (old_name.find(filename + "_") != 0) {
                        std::string new_name = filename + "_" + old_name;
                        std::string new_path = dir + "/" + new_name;
                        if (rename(file_path.c_str(), new_path.c_str()) != 0) {
                            std::cout << "rename fail:" << new_path << std::endl;
                        }
                        std::cout << "rename:" << new_path << std::endl;
                    }
                }
            }
            // 拼出gcode文件的路径
            std::string gcode_path = extract_dir + "/Metadata/" + filename + "_plate_" + plate_index + ".gcode";
            // std::cout << "拼出gcode文件的路径: " << gcode_path << std::endl;
            // 检查gcode是否存在
            if (!Utils::FileExists(gcode_path)) {
                // 3mf解压后未找到gcode
                const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::FileNotExist);
                response["code"] = error.code;
                response["message"] = error.message + gcode_path;
                JsoncppParseRead::ParseJsonToString(response_string, response);
                (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
                return;
            }
            // 直接用gcode_path继续后续流程
            print_task_info->filename = filename + "_plate_" + plate_index + ".gcode";
            local_file_path = gcode_path;

            // 更新请求json中的file_path / file_name
            req_json["data"]["print_task_info"]["file_path"] = "."+ filename + ".3mf/Metadata/" + print_task_info->filename;
            req_json["data"]["print_task_info"]["file_name"] = print_task_info->filename;
        }

        std::cout << "local_file_path: " << local_file_path << std::endl;
        if (!Utils::FileExists(local_file_path)) {
            const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::FileNotExist);
            response["code"] = error.code;
            response["message"] = error.message;
        }
    }
    // 上报打印反馈
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
    const auto& success = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::Success);
    if (response["code"] != success.code) {
        dreame_sub_ptr->processing_printing.store(false, std::memory_order_relaxed);
        return;
    }
    print_task_info->printId = req_json["data"]["print_task_info"]["task_info_id"].asString();
    print_task_info->filename = req_json["data"]["print_task_info"]["file_name"].asString();
    // 处理需要下载远程gcode文件
    bool md5_status = false;
    bool is_3mf_file = false;
    std::string recv;
    // 处理下载gcode文件
    HandleDownloadGcode(dreame_sub_ptr, req_json, md5_status, is_3mf_file);

    // 需要下载文件 且md5校验不过
    if (req_json["data"]["print_task_info"]["file_type"].asInt() == 0 && !md5_status) {
        Json::Value response_md5;
        response_md5 = Utils::GetMqttResponseJson();
        response_md5.removeMember("code");
        response_md5.removeMember("message");
        response_md5["id"] = req_json["id"].asString();
        response_md5["method"] = "thing.property.print.up";
        response_md5["data"]["print_step"]["task_info_id"] = print_task_info->printId;
        response_md5["data"]["print_step"]["print_step_info"] = 15;
        response_md5["data"]["print_step"]["step_data"]["error_msg"] = "md5 check failed";
        std::string response_string;
        JsoncppParseRead::ParseJsonToString(response_string, response_md5);
        (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
        // 通知klipper清除打印信息和gcode下载状态
        Json::Value send_data;
        send_data = Utils::GetMqttResponseJson();
        send_data["method"] = "set.print.task.info";
        send_data["data"]["print_data"] = Json::Value(Json::objectValue);
        send_data["data"]["is_gcode_download"] = false;
        (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), dreame_sub_ptr->KlipperDataJsonParse(send_data));
        dreame_sub_ptr->processing_printing.store(false, std::memory_order_relaxed);
        return;
    }
    if (is_3mf_file) {
        std::string filename = print_task_info->filename.substr(0, print_task_info->filename.size() - 4);
        // plate_index 是指定用哪个盘的gcode
        std::string plate_index = req_json["data"]["print_task_info"]["plate_index"].asString();
        print_task_info->filename = filename + "_plate_" + plate_index + ".gcode";
        
        // 更新请求json中的file_path\file_name
        req_json["data"]["print_task_info"]["file_path"] = "."+ filename + ".3mf/Metadata/" + print_task_info->filename;
        req_json["data"]["print_task_info"]["file_name"] = "."+ filename + ".3mf/Metadata/" + print_task_info->filename;
    }
    dreame_sub_ptr->AddTaskID(req_json["id"].asString(), req_json["method"].asString(), TASK_SHORT_TIMEOUT);
    // 将服务端下发的信息透传到klipper
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), dreame_sub_ptr->KlipperDataJsonParse(req_json));
    dreame_sub_ptr->processing_printing.store(false, std::memory_order_relaxed);
}

void PrintTaskHandler::HandleDownloadGcode(DreameSub* dreame_sub_ptr, Json::Value req_json, bool& md5_status, bool& is_3mf_file) {
    std::string recv;
    if (req_json["data"]["print_task_info"]["file_type"].asInt() == 0) {
        if (print_task_info->filename.substr(print_task_info->filename.size() - 4, print_task_info->filename.size()) == ".3mf") {
            // 判断3mf_dir目录是否存在, 不存在则创建
            if (!Utils::DirectoryExists(globalConfigObject["gcode_file_dir"].asString())) {
                Utils::Execute("mkdir -p " + globalConfigObject["gcode_file_dir"].asString(), recv);
            }
            is_3mf_file = true;
        }
        std::string filePath = globalConfigObject["gcode_file_dir"].asString() + "/" + print_task_info->filename;
        Json::Value download_url_json = GetDownloadUrl(dreame_sub_ptr, req_json);
        std::string downloadUrl = "";
        std::string fileMd5 = "";
        if (!download_url_json.empty()) {
            downloadUrl = download_url_json["data"]["downloadUrl"].asString();
            fileMd5 = download_url_json["data"]["md5"].asString();
        }
        else {
            LOG_ERROR("GetDownloadUrl failed, download_url_json is empty");
            return;
        }
        std::transform(fileMd5.begin(), fileMd5.end(), fileMd5.begin(), ::tolower);
        // 文件存在, 则先清除文件
        if (Utils::FileExists(filePath)) {
            std::string rm_file = "rm '" + filePath + "'";
            std::cout << rm_file << std::endl;
            Utils::Execute(rm_file, recv);
        }
        if (is_3mf_file) {
            // 3mf文件目录存在, 则先清除文件目录
            std::string filePathDir = globalConfigObject["gcode_file_dir"].asString() + "/." + print_task_info->filename;
            if (Utils::DirectoryExists(filePathDir)) {
                std::string rm_file_dir = "rm -r '" + filePathDir + "'";
                std::cout << rm_file_dir << std::endl;
                Utils::Execute(rm_file_dir, recv);
            }
        }
        std::shared_ptr<Gcode> gcode_ptr = dreame_sub_ptr->gcode_handler_;
        // 用 std::promise and std::future 来监听线程是否正常结束或者异常退出
        std::promise<bool> download_promise;
        std::future<bool> download_future = download_promise.get_future();
        // 在另一个线程中启动下载，这样可以同时监控进度
        std::thread download_thread([gcode_ptr, downloadUrl, filePath, &download_promise]() {
            try {
                bool success = gcode_ptr->GcodeDownload(downloadUrl, filePath);
                if (success) {
                    LOG_INFO("Download gcode file completed successfully");
                    std::cout << "Download gcode file completed successfully" << std::endl;
                }
                else {
                    LOG_ERROR("Download gcode file failed");
                    std::cout << "Download gcode file failed" << std::endl;
                }
                download_promise.set_value(success);
            }
            catch (const std::exception& e) {
                LOG_ERROR("Download gcode file exception: {}", e.what());
                std::cout << "Download gcode file exception: " << e.what() << std::endl;
                download_promise.set_exception(std::current_exception());
            }
            });

        bool download_success = false;
        int progress = -1;
        int last_progress = 0;
        // print_task_info 信息先更新到klipper当中，便于服务端在gcode下载的过程中也可以查到任务信息
        Json::Value send_data;
        send_data = Utils::GetMqttResponseJson();
        send_data["method"] = "set.print.task.info";
        // send_data["data"]["print_task_info"] = req_json["data"]["print_task_info"];
        send_data["data"]["print_data"] = req_json["data"];
        send_data["data"]["is_gcode_download"] = true;
        (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), dreame_sub_ptr->KlipperDataJsonParse(send_data));
        // 上报一次0值
        UploadGcodeDownloadProgress(dreame_sub_ptr, req_json);
        // 在主线程中监控进度
        std::string process_msg = "";
        float progress_float = 0.0f;
        // 添加进度为0的超时检测
        auto zero_progress_start = std::chrono::steady_clock::now();
        while (true) {
            progress_float = gcode_ptr->GetDownloadProgress();
            auto current_time = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::minutes>(current_time - zero_progress_start).count();
            // 3分钟内进度一直未变化时, 取消下载并退出
            if (progress_float < 0.000001 && elapsed >= 3) {
                gcode_ptr->download_cancel_ = true;
                gcode_ptr->CancelDownload();
                break;
            }
            progress = static_cast<int>(progress_float);
            // progress = gcode_ptr->GetDownloadProgress();
            process_msg = "file download progress: " + std::to_string(progress) + "%";
            std::cout << process_msg << std::endl;
            LOG_INFO(process_msg);
            if ((progress != -1 && progress - last_progress >= 5) || (progress == 100)) {
                // 进度每5%上报一次到服务端
                last_progress = progress;
                UploadGcodeDownloadProgress(dreame_sub_ptr, req_json);
            }
            if (progress == 100) {
                download_success = true;
                std::cout << "Download gcode file success!" << std::endl;
                break;
            }
            // 检测线程是否正常结束或者异常退出
            if (download_future.wait_for(std::chrono::milliseconds(100)) == std::future_status::ready) {
                try {
                    download_success = download_future.get(); // Get the result or rethrow the exception
                    break;
                }
                catch (const std::exception& e) {
                    LOG_ERROR("Download thread exception caught in main thread: {}", e.what());
                    std::cout << "Download thread exception caught in main thread: " << e.what() << std::endl;
                    download_success = false;
                    break;
                }
            }

            // // 测试取消文件下载接口
            // if (progress > 15) {
            //     std::cout << "cancel download" << std::endl;
            //     gcode_ptr->download_cancel_= true;
            //     break;
            // }

            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        download_thread.join();

        // 确保文件刷入磁盘
        Utils::Execute("sync", recv);
        // 下载结束, 进行文件校验和上报校验结果
        md5_status = FileMd5Check(dreame_sub_ptr, req_json, download_success, fileMd5, filePath);
    }
}

void PrintTaskHandler::UploadGcodeDownloadProgress(DreameSub* dreame_sub_ptr, Json::Value req_json) {
    std::shared_ptr<Gcode> gcode_ptr = dreame_sub_ptr->gcode_handler_;
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response.removeMember("code");
    response.removeMember("message");
    response["id"] = req_json["id"].asString();
    response["method"] = "thing.property.print.up";
    response["data"]["print_step"]["task_info_id"] = print_task_info->printId;
    response["data"]["print_step"]["print_step_info"] = 13;
    response["data"]["print_step"]["step_data"]["progress"] = gcode_ptr->GetDownloadProgress();
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

bool PrintTaskHandler::FileMd5Check(DreameSub* dreame_sub_ptr, Json::Value req_json, bool download_status, const std::string& filemd5, const std::string& local_path) {
    bool md5_check = false;
    std::string recv;
    bool is_3mf_file = false;
    if (print_task_info->filename.substr(print_task_info->filename.size() - 4) == ".3mf") {
        is_3mf_file = true;
    }
    if (download_status) {
        // 下载成功，校验MD5
        std::string calculated_md5 = Utils::calculateMD5(local_path);
        if (calculated_md5 == filemd5) {
            md5_check = true;
            LOG_INFO("MD5 verification successful");
            std::cout << "MD5 verification successful" << std::endl;
            if (is_3mf_file) {
                std::string cmd = "7z x " + local_path + " -o" + globalConfigObject["gcode_file_dir"].asString() + "/." + print_task_info->filename + " > /dev/null 2>&1";
                // 处理文件名包含空格的情况
                if (print_task_info->filename.substr(0, print_task_info->filename.size() - 4).find(" ") != std::string::npos) {
                    cmd = "7z x '" + local_path + "' -o" + globalConfigObject["gcode_file_dir"].asString() + "/'." + print_task_info->filename + "' > /dev/null 2>&1";
                }
                std::cout << cmd << std::endl;
                Utils::Execute(cmd, recv);

                // 对解压路径内的Metadata目录下的所有文件名加前缀
                std::string metadata_dir = globalConfigObject["gcode_file_dir"].asString() + "/." + print_task_info->filename + "/Metadata/";

                if (Utils::DirectoryExists(metadata_dir)) {
                    std::vector<std::string> files;
                    Utils::getFiles(metadata_dir, files);
                    std::string filename = print_task_info->filename.substr(0, print_task_info->filename.size() - 4); // 去掉.3mf

                    for (const auto& file_path : files) {
                        size_t last_slash = file_path.find_last_of('/');
                        std::string dir = file_path.substr(0, last_slash);
                        std::string old_name = file_path.substr(last_slash + 1);
                        // 如果文件名没有前缀，则加上前缀
                        if (old_name.find(filename + "_") != 0) {
                            std::string new_name = filename + "_" + old_name;
                            std::string new_path = dir + "/" + new_name;
                            if (rename(file_path.c_str(), new_path.c_str()) != 0) {
                                std::cout << "rename fail: " << new_path << std::endl;
                            }
                            std::cout << "rename: " << new_path << std::endl;
                        }
                    }
                }
            }
        }
        else {
            // 下载成功但是文件校验失败, 删除文件
            LOG_ERROR("MD5 verification failed. Calculated: {}, Expected: {}", calculated_md5, filemd5);
            std::cout << "MD5 verification failed. Calculated: " << calculated_md5 << ", Expected: " << filemd5 << std::endl;
            if (Utils::FileExists(local_path) && !is_3mf_file) {
                Utils::Execute("rm " + local_path, recv);
                LOG_INFO("File {} deleted due to MD5 mismatch", local_path);
                std::cout << "File " << local_path << " deleted due to MD5 mismatch" << std::endl;
            }
            else if (Utils::FileExists(local_path) && is_3mf_file) {
                // 3mf文件存在, 则先清除文件
                std::string filePathDir = globalConfigObject["gcode_file_dir"].asString() + "/." + print_task_info->filename;
                std::string rm_file = "rm '" + local_path + "'";
                std::cout << rm_file << std::endl;
                Utils::Execute(rm_file, recv);
                if (Utils::DirectoryExists(filePathDir)) {
                    std::string rm_file_dir = "rm -r '" + filePathDir + "'";
                    std::cout << rm_file_dir << std::endl;
                    Utils::Execute(rm_file_dir, recv);
                }
            }
        }
    }
    else {
        // 下载失败, 删除文件
        if (Utils::FileExists(local_path)) {
            std::string rm_file = "rm '" + local_path + "'";
            std::cout << rm_file << std::endl;
            Utils::Execute(rm_file, recv);
            LOG_INFO("File {} deleted due to download failure", local_path);
            std::cout << "File " << local_path << " deleted due to download failure" << std::endl;
        }
        if (is_3mf_file) {
            std::string filePathDir = globalConfigObject["gcode_file_dir"].asString() + "/." + print_task_info->filename;
            if (Utils::DirectoryExists(filePathDir)) {
                std::string rm_file_dir = "rm -r '" + filePathDir + "'";
                std::cout << rm_file_dir << std::endl;
                Utils::Execute(rm_file_dir, recv);
            }
        }
    }
    if (md5_check) {
        return true;
    }
    else {
        return false;
    }
}

void PrintTaskHandler::PrintTaskPause(DreameSub* dreame_sub_ptr, Json::Value req_json) {
    Json::Value response;
    std::string response_string;
    std::string task_info_id = req_json["data"]["task_info_id"].asString();
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    std::string state = dreame_sub_ptr->klipper_info_->klipper_state;
    if (state != "printing") {
        const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::PrintCurStatusNotSupport);
        std::string msg = " print task pause failed, state: " + state;
        std::cout << msg << std::endl;
        LOG_ERROR(msg);
        response["code"] = error.code;
        response["message"] = error.message + msg;
    }
    // 上报暂停反馈
    if (print_task_info->printId != task_info_id) {
        const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::PrintIdNotMatch);
        response["code"] = error.code;
        response["message"] = error.message;
    }
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
    const auto& success = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::Success);
    if (response["code"] != success.code) {
        return;
    }
    dreame_sub_ptr->AddTaskID(req_json["id"].asString(), req_json["method"].asString(), TASK_SHORT_TIMEOUT);
    // 将服务端下发的信息透传到klipper
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), dreame_sub_ptr->KlipperDataJsonParse(req_json));
}

void PrintTaskHandler::PrintTaskResume(DreameSub* dreame_sub_ptr, Json::Value req_json) {
    Json::Value response;
    std::string response_string;
    std::string task_info_id = req_json["data"]["task_info_id"].asString();
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    std::string state = dreame_sub_ptr->klipper_info_->klipper_state;
    if (state != "paused") {
        const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::PrintCurStatusNotSupport);
        std::string msg = " print task resume failed, state: " + state;
        std::cout << msg << std::endl;
        LOG_ERROR(msg);
        response["code"] = error.code;
        response["message"] = error.message + msg;
    }
    // 上报恢复反馈
    if (print_task_info->printId != task_info_id) {
        const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::PrintIdNotMatch);
        response["code"] = error.code;
        response["message"] = error.message;
    }
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
    const auto& success = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::Success);
    if (response["code"] != success.code) {
        return;
    }
    dreame_sub_ptr->AddTaskID(req_json["id"].asString(), req_json["method"].asString(), TASK_SHORT_TIMEOUT);
    // 将服务端下发的信息透传到klipper
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), dreame_sub_ptr->KlipperDataJsonParse(req_json));
}

void PrintTaskHandler::PrintTaskStop(DreameSub* dreame_sub_ptr, Json::Value req_json) {
    std::string task_info_id = req_json["data"]["task_info_id"].asString();
    // 上报取消反馈
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    if (print_task_info->printId != task_info_id) {
        const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::PrintIdNotMatch);
        response["code"] = error.code;
        response["msg"] = error.message;
    }
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
    const auto& success = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::Success);
    if (response["code"] != success.code) {
        return;
    }
    dreame_sub_ptr->AddTaskID(req_json["id"].asString(), req_json["method"].asString(), TASK_SHORT_TIMEOUT);
    // 将服务端下发的信息透传到klipper
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), dreame_sub_ptr->KlipperDataJsonParse(req_json));
}

Json::Value PrintTaskHandler::GetDownloadUrl(DreameSub* dreame_sub_ptr, Json::Value req_json) {
    std::string file_key = req_json["data"]["print_task_info"]["file_key"].asString();
    std::string url = globalConfigObject["server_url"].asString() + "/api/3dprint/file/v1/file/downloadWithoutToken";

    Json::Value requestBody;
    requestBody["fileName"] = req_json["data"]["print_task_info"]["file_name"].asString();
    requestBody["objectName"] = file_key;
    requestBody["sourceType"] = 1;

    std::string requestBodyStr;
    JsoncppParseRead::ParseJsonToString(requestBodyStr, requestBody);

    // 使用curl发送POST请求
    std::string command = "curl -X POST -H \"Content-Type: application/json\" -d '" + requestBodyStr + "' " + url;
    std::cout << "GetDownloadUrl command: " << command << std::endl;
    LOG_INFO("GetDownloadUrl command: {}", command);
    std::string responseStr;
    try {
        Utils::Execute(command, responseStr);
    }
    catch (const std::exception& e) {
        std::string error_msg = "Error executing command: " + std::string(e.what());
        std::cerr << error_msg << std::endl;
        LOG_ERROR(error_msg);
        return Json::Value(Json::objectValue);
    }
    std::cout << "GetDownloadUrl response: " << responseStr << std::endl;
    LOG_INFO("GetDownloadUrl response: {}", responseStr);

    Json::Value responseJson;
    if (!JsoncppParseRead::ReadStringToJson(responseStr, responseJson)) {
        std::string error_msg = "Failed to parse JSON response: " + responseStr;
        std::cerr << error_msg << std::endl;
        LOG_ERROR(error_msg);
        return Json::Value(Json::objectValue);
    }

    // 提取并返回设备唯一ID
    if (responseJson.isMember("data") && responseJson["data"].isMember("downloadUrl")) {
        return responseJson;
    }
    else {
        std::string error_msg = "downloadUrl not found in response: " + responseStr;
        std::cerr << error_msg << std::endl;
        LOG_ERROR(error_msg);
        return Json::Value(Json::objectValue);
    }

}

void PrintTaskHandler::HandlePrintQueue(DreameSub* dreame_sub_ptr, Json::Value req_json) {
    Json::Value print_task = dreame_sub_ptr->GetFirstPrintTask();
    // 队列数据减少一个后 上报新的列表数据, 然后再开始打印
    std::this_thread::sleep_for(std::chrono::seconds(2));
    GetPrintTaskQueueList(dreame_sub_ptr, req_json);

    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    if (print_task.empty()) {
        const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::PrintQueueEmpty);
        response["code"] = error.code;
        response["message"] = error.message;
        std::string response_string;
        JsoncppParseRead::ParseJsonToString(response_string, response);
        (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
        return;
    }
    if (req_json["data"]["task_info_id"].asString() == print_task["data"]["print_task_info"]["task_info_id"].asString()) {
        print_task["print_from_queue"] = true;
        PrintTaskStart(dreame_sub_ptr, print_task);
    }
    else {
        const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::PrintTaskIdNotMatch);
        response["code"] = error.code;
        response["message"] = error.message;
        std::string response_string;
        JsoncppParseRead::ParseJsonToString(response_string, response);
        (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
        return;
    }
}

void PrintTaskHandler::GetPrintTaskQueueList(DreameSub* dreame_sub_ptr, Json::Value req_json) {
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = "thing.property.print.up";
    response["data"]["print_queue_list"] = Json::Value(Json::arrayValue);
    if (!dreame_sub_ptr->print_task_queue_list.empty()) {
        for (const auto& task : dreame_sub_ptr->print_task_queue_list) {
            response["data"]["print_queue_list"].append(task["data"]["print_task_info"]);
        }
    }
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

void PrintTaskHandler::GetPrintTaskInfoDetail(DreameSub* dreame_sub_ptr, Json::Value req_json) {
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    std::string task_info_id = req_json["data"]["task_info_id"].asString();
    response["data"]["start_print_config"] = Json::Value(Json::objectValue);
    for (const auto& task : dreame_sub_ptr->print_task_queue_list) {
        if (task["data"]["print_task_info"]["task_info_id"].asString() == task_info_id) {
            response["data"]["start_print_config"] = task["data"];
            break;
        }
    }
    if (response["data"]["start_print_config"].empty()) {
        const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::PrintTaskIdNotMatch);
        response["code"] = error.code;
        response["message"] = error.message;
    }
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

void PrintTaskHandler::DeletePrintTaskQueueTargetId(DreameSub* dreame_sub_ptr, Json::Value req_json) {
    bool result;
    result = dreame_sub_ptr->DeletePrintTask(req_json);
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    if (!result) {
        const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::PrintTaskIdNotMatch);
        response["code"] = error.code;
        response["message"] = error.message;
    }
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);

    // 删除之后 上报新的列表数据
    std::this_thread::sleep_for(std::chrono::seconds(2));
    GetPrintTaskQueueList(dreame_sub_ptr, req_json);

}

void PrintTaskHandler::PrintTaskQueueAdjust(DreameSub* dreame_sub_ptr, Json::Value req_json){
    bool result;
    result = dreame_sub_ptr->AdjustPrintTaskQueue(req_json);
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    if (!result) {
        const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::PrintTaskIdNotMatch);
        response["code"] = error.code;
        response["message"] = error.message;
    }
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);

    // 调整完后上报一次数据
    std::this_thread::sleep_for(std::chrono::seconds(1));
    GetPrintTaskQueueList(dreame_sub_ptr, req_json);
}

