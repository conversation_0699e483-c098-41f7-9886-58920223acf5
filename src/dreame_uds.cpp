/**
 * @file dreame_uds.cpp
 * <AUTHOR> (g<PERSON><PERSON><PERSON>@dreame.tech)
 * @brief 
 * @version 0.1
 * @date 2025-03-10
 * @copyright Copyright (c) {2025} 追觅科技有限公司版权所有
 */
#include <iostream>
#include <memory>
#include "dreame_uds.h"
#include "jsonparse.h"
#include "logger.h"
#include "utils.h" // Ensure Utils is included

DreameUds::DreameUds() : uds_server_queue(nullptr), uds_server_running(false) {}

CircularQueue<Result>* DreameUds::getQueue() {
    return uds_server_queue;
}

int DreameUds::getClientSockFd() {
    return dreame_uds_socket_->cur_client_sockfd;
}

void DreameUds::RecvUdsClientBuffer(const int fd, const uint8_t *buffer, const uint32_t length)
{
    std::unique_lock<std::mutex> lck(g_mtx);
    std::string recv_str;
    recv_str.assign((char *)(buffer), length - 1);
    Result result;
    result.fd = fd;
    result.recv_str = recv_str;
    uds_server_queue->push(result);
}

void DreameUds::SendData(const int fd, std::string data)
{
    if (fd == -1) {
        return;
    }
    data += "\x03";
    std::string output_msg = "Send Data To MainProc fd:" + std::to_string(fd) + " data:" + data;
    // 不打印频繁请求klipper的信息
    if (data.find("get.klipper.info") == std::string::npos) {
        LOG_INFO(output_msg);
        std::cout << output_msg << std::endl;
    }
    dreame_uds_socket_->ServerSendMsg(fd, data.c_str(), data.size());
}

void DreameUds::Stop() {
    uds_server_running = false;
}

template <typename T, typename... Args>
std::unique_ptr<T> make_unique(Args&&... args) {
    return std::unique_ptr<T>(new T(std::forward<Args>(args)...));
}

bool DreameUds::Init(std::string uds_path, CircularQueue<Result> &queue)
{
    uds_server_queue = &queue;
    uds_server_running = true;
    uds_path = uds_path;
    // 判断uds_path的目录是否存在，不存在则创建
    std::string uds_dir = uds_path.substr(0, uds_path.rfind("/"));
    if (!Utils::DirectoryExists(uds_dir)) {
        std::string command = "mkdir -p " + uds_dir;
        std::string result;
        Utils::Execute(command, result);
    }
    dreame_uds_socket_ = std::make_shared<UnixDomainSocket>(uds_path);
    dreame_uds_socket_->CreateServer();
    // 设置uds回调
    dreame_uds_socket_->AddReadClientDataCallBack(std::bind(&DreameUds::RecvUdsClientBuffer, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
    return true;
}

DreameUds::~DreameUds() {
    std::cout << "Mqtt delete DreameUds..." << std::endl;
    Stop();
}