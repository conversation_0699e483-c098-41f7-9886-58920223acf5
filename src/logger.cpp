/**
 * @file logger.cpp
 * <AUTHOR> (g<PERSON><PERSON><PERSON>@dreame.tech)
 * @brief 
 * @version 0.1
 * @date 2025-03-07
 * @copyright Copyright (c) {2025} 追觅科技有限公司版权所有
 */
#include "logger.h"
#include "config.h"

std::shared_ptr<spdlog::logger> Logger::logger_ = nullptr;

void Logger::init() {
    std::string log_path = globalConfigObject["log"]["log_path"].asString();
    // 创建rotating file sink
    size_t max_size = globalConfigObject["log"]["max_size"].asInt() * 1024 * 1024; // 20M
    size_t max_files = globalConfigObject["log"]["max_files"].asInt();  // 3个
    auto sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
        log_path, max_size, max_files);

    // 创建logger实例
    logger_ = std::make_shared<spdlog::logger>("mqttproc", sink);
    
    // 设置日志格式
    logger_->set_pattern("%Y-%m-%d %H:%M:%S.%f [%n:%#] [%!:%s] [%l] %v");
    
    // 设置日志级别
    // 自动刷新日志
    if (globalConfigObject["log"]["level"].asString() == "trace") {
        logger_->set_level(spdlog::level::trace);
        logger_->flush_on(spdlog::level::trace);
    } else if (globalConfigObject["log"]["level"].asString() == "debug") {
        logger_->set_level(spdlog::level::debug);
        logger_->flush_on(spdlog::level::debug);
    } else if (globalConfigObject["log"]["level"].asString() == "info") {
        logger_->set_level(spdlog::level::info);
        logger_->flush_on(spdlog::level::info);
    } else if (globalConfigObject["log"]["level"].asString() == "warn") {
        logger_->set_level(spdlog::level::warn);
        logger_->flush_on(spdlog::level::warn);
    } else if (globalConfigObject["log"]["level"].asString() == "err") {
        logger_->set_level(spdlog::level::err);
        logger_->flush_on(spdlog::level::err);
    } else if (globalConfigObject["log"]["level"].asString() == "critical") {
        logger_->set_level(spdlog::level::critical);
        logger_->flush_on(spdlog::level::critical);
    }
    
    // 注册为全局logger
    spdlog::register_logger(logger_);
}

std::shared_ptr<spdlog::logger> Logger::get() {
    return logger_;
}