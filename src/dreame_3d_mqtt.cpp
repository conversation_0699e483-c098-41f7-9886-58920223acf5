#include <iostream>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unordered_map>
#include "dreame_3d_mqtt.h"
#include "utils.h"
#include "logger.h"

#define DEFAULT_STACK_SIZE -1

// --- 遗嘱消息 (LWT) 配置 ---
std::string LWT_TOPIC;    // 遗嘱消息发布的主题
std::string LWT_PAYLOAD;        // 遗嘱消息内容
const int LWT_QOS = 1;                           // 遗嘱消息的服务质量等级
const bool LWT_RETAIN = false;                   // 遗嘱消息是否需要保留

/**
 * @brief 全局注册话题->回调表
 */
static std::unordered_map<std::string, std::function<void(mqtt::const_message_ptr &)>> g_topic_function_map_;

Dreame3dMqtt::Dreame3dMqtt(std::string addr, int port)
    : ip_addr_(addr), port_(port), report_state(false)
{
    // g_topic_function_map_.clear();
}

Dreame3dMqtt::~Dreame3dMqtt()
{
    for (auto kv : g_topic_function_map_) {
        if (!mqtt_client_) {
            break;
        }
        mqtt::token_ptr unsubtok = mqtt_client_->unsubscribe(kv.first.c_str());
        std::string msg = "Unsubscription from topic " + kv.first;
        if (!unsubtok->wait_for(std::chrono::seconds(5))) {
            msg += " timed out.";
            LOG_ERROR(msg);
        } else {
            msg += " successfully.";
            LOG_INFO(msg);
        }
    }

    if (mqtt_client_) {
        mqtt::token_ptr disconntok = mqtt_client_->disconnect();
        if (!disconntok->wait_for(std::chrono::seconds(5))) {
            LOG_ERROR("~Dreame3dMqtt Disconnection timed out.");
        } else {
            LOG_INFO("~Dreame3dMqtt Disconnected successfully.");
        }    
    }
}

void Dreame3dMqtt::messageArrived(mqtt::const_message_ptr &msg)
{

    // std::string topic;
    // topic = msg->get_topic();
    // if (g_topic_function_map_.count(topic)) {
    //     // std::cout << "Handler topic: " << topic << std::endl;
    //     g_topic_function_map_[topic](msg);
    // } else {
    //     std::cerr << "No handler for topic: " << topic << std::endl;
    // }

    std::string topic = msg->get_topic();
    bool found = false;
    if (topic.rfind("/3dprinter/dev", 0) == 0) {
        // 此处为服务端要求 不响应/3dprinter/dev开头的消息
        std::string msg = "cur topic is not support " + topic;
        LOG_INFO(msg);
        return;
    }
    for (const auto& entry : g_topic_function_map_) {
        const std::string& pattern = entry.first;
        if (Utils::match_topic(pattern, topic)) {
            entry.second(msg);  // Call the handler function
            found = true;
            break;
        }
    }
    if (!found) {
        std::string msg = "No handler for topic: " + topic;
        LOG_INFO(msg);
    }
}

bool Dreame3dMqtt::Init()
{
    topic_               = "/3dprinter/client/dev/res/P300/G2522"; // 立即响应的指令发到这个topic
    std::string clientId = GetClientId();
// #if defined(__x86_64__)
//     clientId = "x86" + clientId;
// #endif
    // 如果服务端前置开启了SSL/TLS双向认证的配置, 此接口也无法脸上服务端
    // 此接口适用于服务端未启用SSL/TLS双向认证的配置
    SetTopicValue();
    std::string server_uri = "tcp://" + ip_addr_ + ":" + std::to_string(port_);
    std::cerr << "uri: " << server_uri << " client_id:" << clientId << std::endl;
    auto client = std::make_shared<mqtt::async_client>(server_uri, clientId);
    mqtt_client_ = client;

    // 设置回调
    callback_ = std::make_unique<Callback>(this);
    client->set_callback(*callback_);

    // 设置连接成功回调, 上报一条在线信息
    client->set_connected_handler([this](const std::string& cause) {
        report_state = true;
        // Connected to MQTT broker. Cause: automatic reconnect
        // Connected to MQTT broker. Cause: connect onSuccess called
        std::cout << "Connected to MQTT broker. Cause: " << cause << std::endl;
        LOG_INFO("Connected to MQTT broker. Cause: " + cause);
        Json::Value response;
        response = Utils::GetMqttResponseJson();
        response["method"] = "thing.property.machine.up";
        response["data"]["machine_online_state"] = 1;  // 1在线 2离线
        std::string response_string;
        JsoncppParseRead::ParseJsonToString(response_string, response);
        char buf[1024];
        int len = snprintf(buf, sizeof(buf),  "%s", response_string.c_str());
        std::string msg = "Upload " + std::to_string(len) + " bytes to (" + thing_topic_up + ")" + response_string;
        std::cout << msg << std::endl;
        LOG_INFO(msg);
        mqtt::message_ptr pubmsg = mqtt::make_message(thing_topic_up.c_str(), (uint8_t *)buf, len);
        pubmsg->set_qos(QOS0);
        mqtt_client_->publish(pubmsg)->wait_for(2);
        CheckOtaUpgradeResult();
    });

    // 构造遗嘱消息数据
    LWT_TOPIC = thing_topic_up;
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["method"] = "thing.property.machine.up";
    response["data"]["machine_online_state"] = 2;  // 1在线 2离线
    JsoncppParseRead::ParseJsonToString(LWT_PAYLOAD, response);
    mqtt::message_ptr willmsg = mqtt::make_message(
        LWT_TOPIC,         // 遗嘱主题
        LWT_PAYLOAD,       // 遗嘱内容
        LWT_QOS,           // 遗嘱 QoS
        LWT_RETAIN         // 遗嘱保留标志
    );
    auto connopts = mqtt::connect_options_builder()
        .automatic_reconnect(true)
        .keep_alive_interval(std::chrono::seconds(globalConfigObject["machine_keep_alive_period"].asInt()))
        .clean_session(true)  // 服务端要求
        .will(*willmsg)       // 设置遗嘱消息
        .user_name("admin")
        .password("123456")
        .finalize();

    std::cout << "MQTT Connecting..." << std::endl;
    mqtt::token_ptr conntok = client->connect(connopts);
    std::cout << "Waiting for the connection..." << std::endl;
    try {
        conntok->wait();
    } catch (const mqtt::exception &e) {
        std::cerr << "MQTT connect failed: " << e.what() << std::endl;
        return false;
    }
    std::cout << "MQTT connect success" << std::endl;

    return true;
}


bool Dreame3dMqtt::SSLInit()
{
    topic_               = "/3dprinter/client/dev/res/P300/G2522"; // 立即响应的指令发到这个topic
    std::string clientId = GetClientId();
// #if defined(__x86_64__)
//     clientId = "x86" + clientId;
// #endif
    SetTopicValue();
    ip_addr_ = globalConfigObject["mqtt_addr"].asString();
    port_ = globalConfigObject["mqtt_port"].asInt();
    std::string server_uri = "ssl://" + ip_addr_ + ":" + std::to_string(port_);
    std::cerr << "uri: " << server_uri << " client_id:" << clientId << std::endl;
    auto client = std::make_shared<mqtt::async_client>(server_uri, clientId);
    mqtt_client_ = client;

    // 设置回调
    callback_ = std::make_unique<Callback>(this);
    client->set_callback(*callback_);

    // 设置连接成功回调, 上报一条在线信息
    client->set_connected_handler([this](const std::string& cause) {
        report_state = true;
        // Connected to MQTT broker. Cause: automatic reconnect
        // Connected to MQTT broker. Cause: connect onSuccess called
        std::cout << "Connected to MQTT broker. Cause: " << cause << std::endl;
        LOG_INFO("Connected to MQTT broker. Cause: " + cause);
        Json::Value response;
        response = Utils::GetMqttResponseJson();
        response["method"] = "thing.property.machine.up";
        response["data"]["machine_online_state"] = 1;  // 1在线 2离线
        std::string response_string;
        JsoncppParseRead::ParseJsonToString(response_string, response);
        char buf[1024];
        int len = snprintf(buf, sizeof(buf),  "%s", response_string.c_str());
        std::string msg = "Upload " + std::to_string(len) + " bytes to (" + thing_topic_up + ")" + response_string;
        std::cout << msg << std::endl;
        LOG_INFO(msg);
        try{
            mqtt::message_ptr pubmsg = mqtt::make_message(thing_topic_up.c_str(), (uint8_t *)buf, len);
            pubmsg->set_qos(QOS0);
            mqtt_client_->publish(pubmsg)->wait_for(2);
        } catch (const std::exception &e) {
            std::cerr << "MQTT publish failed: " << e.what() << std::endl;
        } catch (...) {
            std::cerr << "MQTT publish failed: unknown error" << std::endl;
        }
        CheckOtaUpgradeResult();
    });
    auto sslopts = mqtt::ssl_options_builder()
    .trust_store(globalConfigObject["server_ca_cert_file"].asString())
    .key_store(globalConfigObject["client_cert_file"].asString())
    .private_key(globalConfigObject["client_key_file"].asString())
    .error_handler([](const std::string& msg) {
        std::cerr << "SSL Error: " << msg << std::endl;
    })
    .finalize();

    // 构造遗嘱消息数据
    LWT_TOPIC = thing_topic_up;
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["method"] = "thing.property.machine.up";
    response["data"]["machine_online_state"] = 2;  // 1在线 2离线
    JsoncppParseRead::ParseJsonToString(LWT_PAYLOAD, response);
    mqtt::message_ptr willmsg = mqtt::make_message(
        LWT_TOPIC,         // 遗嘱主题
        LWT_PAYLOAD,       // 遗嘱内容
        LWT_QOS,           // 遗嘱 QoS
        LWT_RETAIN         // 遗嘱保留标志
    );
    auto connopts = mqtt::connect_options_builder()
        .ssl(std::move(sslopts))
        .automatic_reconnect(true)
        .keep_alive_interval(std::chrono::seconds(globalConfigObject["machine_keep_alive_period"].asInt()))
        .clean_session(true)  // 服务端要求
        .will(*willmsg)       // 设置遗嘱消息
        .finalize();

    std::cout << "MQTT Connecting..." << std::endl;
    mqtt::token_ptr conntok = client->connect(connopts);
    try {
        conntok->wait();
    } catch (const mqtt::exception &e) {
        std::string err_msg = "MQTT connect failed: " + std::string(e.what());
        std::cerr << err_msg << std::endl;
        LOG_ERROR(err_msg);
        return false;
    }
    std::cout << "MQTT SSLInit connect success" << std::endl;
    LOG_INFO("MQTT SSLInit connect success");
    return true;
}

void Dreame3dMqtt::AddCallback(std::string &topic, const int qos, std::function<void(mqtt::const_message_ptr &)> handler)
{
    std::unique_lock<std::mutex> lck(g_mtx);
    g_topic_function_map_[topic] = handler;
    std::string msg = "Mqtt subscribe topic " + topic;
    std::cout << msg << std::endl;
    LOG_INFO(msg);
    if (mqtt_client_) {
        if (mqtt_client_->is_connected()){
            try{
                mqtt::token_ptr subtok = mqtt_client_->subscribe(topic.c_str(), qos);
                if (!subtok->wait_for(std::chrono::seconds(5))) {
                    std::string err_msg = "Subscription to topic " + topic + " timed out.";
                    std::cerr << err_msg << std::endl;
                    LOG_ERROR(err_msg);
                }
            } catch (const std::exception& e) {
                std::cerr << "AddCallback exception: " << e.what() << std::endl;
                LOG_ERROR("AddCallback exception: " + std::string(e.what()));
            } catch (...) {
                std::cerr << "Unknown AddCallback exception" << std::endl;
                LOG_ERROR("Unknown AddCallback exception");
            }
        } else {
            std::string err_msg = "Subscription to topic " + topic + " fail" + " MQTT is disconnected";
            std::cerr << err_msg << std::endl;
            LOG_ERROR(err_msg);
        }
    }

}


void Dreame3dMqtt::DelTopic(std::string topic)
{
    std::unique_lock<std::mutex> lck(g_mtx);
    g_topic_function_map_.erase(topic);
    mqtt::token_ptr unsubtok = mqtt_client_->unsubscribe(topic.c_str());
    std::string msg = "Unsubscription from topic " + topic;
    if (!unsubtok->wait_for(std::chrono::seconds(5))) {
        msg += " timed out.";
        std::cerr << msg << std::endl;
        LOG_ERROR(msg);
    } else {
        msg += " successfully.";
        std::cout << msg << std::endl;
        LOG_INFO(msg);
    }
}

std::string Dreame3dMqtt::GetClientId()
{
    std::string client_id = globalConfigObject["device_id"].asString();
    // std::string client_id = globalConfigObject["device_id"].asString() + "&300|secureMode=2,signMethod=hmacsha256,timestamp=1745982948000|";
    return client_id;
    std::string devid            = Utils::ReadFileIntoString("/proc/cpuinfo");
    std::string cpuinfo          = "Serial";
    std::string::size_type index = devid.find(cpuinfo);
    if (index != std::string::npos) {
        uint64_t size                   = cpuinfo.size() + 4;
        std::string::size_type position = devid.find_first_of("\n", index + size);
        std::string str                 = devid.substr(index + size, position - index - size);
        std::string msg = "Device ID is " + str;
        std::cout << msg << std::endl;
        LOG_INFO(msg);
        return str;
    }
    return CLIENTID;
}


int Dreame3dMqtt::PublishMsg(const char *topicName, const uint8_t *payload, const int len, const int qos)
{
    if (mqtt_client_->is_connected()){
        try {
            mqtt::message_ptr pubmsg = mqtt::make_message(topicName, payload, len);
            pubmsg->set_qos(qos);
            mqtt_client_->publish(pubmsg)->wait_for(PUBLISH_TIMEOUT);
        } catch (const std::exception& e) {
            std::cerr << "PublishMsg exception: " << e.what() << std::endl;
            LOG_ERROR("PublishMsg exception: " + std::string(e.what()));
        } catch (...) {
            std::cerr << "Unknown PublishMsg exception" << std::endl;
            LOG_ERROR("Unknown PublishMsg exception");
        }
    } else{
        std::string payload_str(reinterpret_cast<const char*>(payload), len);
        std::string msg = "mqtt is not connected, the data has been discarded: " + payload_str;
        std::cout << msg << std::endl;
        LOG_INFO(msg);
    }
    return 0;
}

void Dreame3dMqtt::Loop()
{
    char buf[102400];
    float version = 0.3;
    // std::string msg = "Mqtt " + GetClientId() + " upload topic is " + topic_;
    // std::cout << msg << std::endl;
    // LOG_INFO(msg);
    uint32_t count     = 0;
    int64_t state_last_timer = Utils::GetCurrentMsTime();
    int64_t heartbeat_last_timer = Utils::GetCurrentMsTime();
    bool re_subscribe = false;
    int qos = QOS0;
    while (true) {
        int64_t state_cur_timer = Utils::GetCurrentMsTime();
        if (state_cur_timer - state_last_timer >= 1000) {
            state_last_timer = state_cur_timer;
            std::unique_lock<std::mutex> lck(msg_g_mtx);
            for (uint32_t i = 0; i < mainproc_msg_list_.size(); ++i) {
                Json::Value mainproc_msg_json;
                JsoncppParseRead::ReadStringToJson(mainproc_msg_list_[i], mainproc_msg_json);
                try {
                    std::string cur_topic = GetCurUploadTopic(mainproc_msg_json["method"].asString());
                    qos = QOS0;
                    // 要上报的数据中有qos字段时,以其为主,否则默认按qos0进行上报
                    if (mainproc_msg_json.isMember("qos")) {
                        qos = mainproc_msg_json["qos"].asInt();
                        mainproc_msg_json.removeMember("qos");
                    }
                    if (cur_topic == ""){
                        // 处理不认识的method的异常情况
                        std::string msg = "cur_topic is empty, unknow method: " + mainproc_msg_json["method"].asString();
                        std::cout << msg << std::endl;
                        LOG_INFO(msg);
                        continue;
                    }
                    std::string result = "";
                    JsoncppParseRead::ParseJsonToString(result, mainproc_msg_json);
                    int len = snprintf(buf, sizeof(buf),  "%s", result.c_str());
                    std::string msg = "qos:" + std::to_string(qos) + " " + "Upload " + std::to_string(len) + " bytes to (" + cur_topic + ")" + result;
                    std::cout << msg << std::endl;
                    LOG_INFO(msg);
                    // PublishMsg(cur_topic.c_str(), (uint8_t *)buf, len, QOS0);
                    PublishMsg(cur_topic.c_str(), (uint8_t *)buf, len, qos);
                } catch (const std::exception& e) {
                    std::cerr << "Exception: " << e.what() << std::endl;
                    LOG_INFO("Exception: " + std::string(e.what()));
                }
            }
            mainproc_msg_list_.clear();
        }
        if (!mqtt_client_->is_connected() && !re_subscribe){
            re_subscribe = true;
        } else if (mqtt_client_->is_connected() && re_subscribe) {
            try{
                for (auto kv : g_topic_function_map_) {
                    mqtt::token_ptr subtok = mqtt_client_->subscribe(kv.first.c_str(), QOS0);
                    if (!subtok->wait_for(std::chrono::seconds(5))) {
                        std::string msg = "ReSubscribed to topic " + kv.first + " timed out.";
                        std::cerr << msg << std::endl;
                        LOG_INFO(msg);
                    } else {
                        std::string msg = "ReSubscribed to topic " + kv.first + " success";
                        std::cout << msg << std::endl;
                        LOG_INFO(msg);
                    }
                }
                re_subscribe = false;
            } catch (const std::exception& e) {
                std::cerr << "ReSubscribe exception: " << e.what() << std::endl;
                LOG_ERROR("ReSubscribe exception: " + std::string(e.what()));
                std::this_thread::sleep_for(std::chrono::seconds(10));
            } catch (...) {
                std::cerr << "Unknown ReSubscribe exception" << std::endl;
                LOG_ERROR("Unknown ReSubscribe exception");
                std::this_thread::sleep_for(std::chrono::seconds(10));
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

int Dreame3dMqtt::UploadMsg(std::string msg)
{
    std::unique_lock<std::mutex> lck(g_mtx);
    msg_list_.push_back(msg);
    return msg_list_.size();
}

int Dreame3dMqtt::UploadStateMsg(std::string msg)
{
    std::unique_lock<std::mutex> lck(state_g_mtx);
    state_msg_list_.push_back(msg);
    return state_msg_list_.size();
}

int Dreame3dMqtt::UploadHeartbeatMsg(std::string msg)
{
    std::unique_lock<std::mutex> lck(heartbeat_msg_g_mtx);
    mainproc_heartbeat_msg_list.push_back(msg);
    return mainproc_heartbeat_msg_list.size();
}

int Dreame3dMqtt::UploadTopicMsg(std::string msg)
{
    std::unique_lock<std::mutex> lck(msg_g_mtx);
    mainproc_msg_list_.push_back(msg);
    return mainproc_msg_list_.size();
}

std::string Dreame3dMqtt::GetProjectName(){
    return project_name_;
}

std::string Dreame3dMqtt::GetModelId(){
    return model_id_;
}

std::string Dreame3dMqtt::GetDeviceId(){
    return device_id_;
}

void Dreame3dMqtt::SetTopicValue(){
    project_name_ = "3dprinter";
    model_id_ = "/P300/";
    device_id_ = "";
    // 判断是否包含&符号
    size_t pos = globalConfigObject["device_id"].asString().find('&');
    if (pos != std::string::npos) {
        std::string firstPart = globalConfigObject["device_id"].asString().substr(0, pos);
        device_id_ = firstPart;
    }
    std::string topic_start = project_name_+model_id_+device_id_;
    thing_topic_up = topic_start + "/thing/up";
    property_up_topic = topic_start + "/thing/property/up";           // 属性上报topic
    property_set_response_topic = topic_start + "/thing/property/set/response"; // 设置属性回复ack topic
    event_up_topic = topic_start + "/thing/event/up";                 // 事件上报topic
    service_response_topic = topic_start + "/thing/service/response"; // 设备回复服务端ack(可携带响应内容)topic
    progress_up_topic = topic_start + "/thing/progress/up";           // 打印进度上报topic
}

std::string Dreame3dMqtt:: GetCurUploadTopic(std::string method){
    return thing_topic_up;
}

void Dreame3dMqtt::CheckOtaUpgradeResult(){
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["method"] = "thing.property.ota.up";
    response["data"]["progres_ota"]["status"] = 5;
    if (Utils::FileExists(globalConfigObject["upgradeflag_path"].asString())) {
        std::string recv;
        std::string cat_cmd = "cat " + globalConfigObject["upgradeflag_path"].asString() + " | tr -d '\n'";
        Utils::Execute(cat_cmd, recv);
        LOG_INFO("ota upgrade result: " + recv);
        if (recv == "UPGRADE_SUCCESS") {
            response["data"]["progres_ota"]["status"] = 4;
        } else {
            response["data"]["progres_ota"]["status"] = 8;
        }
        response["data"]["progres_ota"]["progress"] = 100;
        std::string response_string;
        JsoncppParseRead::ParseJsonToString(response_string, response);
        // 上报到mqtt服务端
        char buf[1024];
        int len = snprintf(buf, sizeof(buf),  "%s", response_string.c_str());
        std::string msg = "Upload " + std::to_string(len) + " bytes to (" + thing_topic_up + ")" + response_string;
        std::cout << msg << std::endl;
        LOG_INFO(msg);
        try{
            mqtt::message_ptr pubmsg = mqtt::make_message(thing_topic_up.c_str(), (uint8_t *)buf, len);
            pubmsg->set_qos(QOS0);
            mqtt_client_->publish(pubmsg)->wait_for(2);
        } catch (const std::exception &e) {
            std::cerr << "MQTT publish failed: " << e.what() << std::endl;
        } catch (...) {
            std::cerr << "MQTT publish failed: unknown error" << std::endl;
        }
        std::string rm_cmd = "rm " + globalConfigObject["upgradeflag_path"].asString();
        Utils::Execute(rm_cmd, recv);
    }
}
