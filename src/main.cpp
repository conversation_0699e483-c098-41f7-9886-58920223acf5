#include "utils.h"
#include "dreame_sub.h"
#include "xepoll.h"
#include <csignal>
#include <cstdlib>
#include <cstdio>
#include <fstream>
#include <cstring>
#include <iostream>
#include <memory>
#include "logger.h"
#include "dreame_uds.h"
#include "config.h"
#include "circular_queue.h"

#if defined(__unix__) || defined(__APPLE__)
#ifdef __GLIBC__
#include <execinfo.h>
#endif

#define PRINT_SIZE_ 100
const char *g_exe_name;

static void _signal_handler(int signum)
{
#ifdef __GLIBC__
    void *array[PRINT_SIZE_];
    char **strings;

    size_t size = backtrace(array, PRINT_SIZE_);
    strings     = backtrace_symbols(array, size);

    if (strings == nullptr) {
        fprintf(stderr, "backtrace_symbols");
        exit(EXIT_FAILURE);
    }
#endif
    switch (signum) {
    case SIGSEGV:
        fprintf(stderr, "widebright received SIGSEGV! Stack trace:\n");
        break;

    case SIGPIPE:
        fprintf(stderr, "widebright received SIGPIPE! Stack trace:\n");
        break;

    case SIGFPE:
        fprintf(stderr, "widebright received SIGFPE! Stack trace:\n");
        break;

    case SIGABRT:
        fprintf(stderr, "widebright received SIGABRT! Stack trace:\n");
        break;

    default:
        break;
    }
#ifdef __GLIBC__
#ifdef BACKTRACE_DEBUG
    std::vector<std::string> strs;
    for (uint32_t i = 0; i < size; i++) {
        fprintf(stderr, "%d %s \n", i, strings[i]);
        strs.push_back(strings[i]);
    }
    Utils::Addr2Line(g_exe_name, strs);
#else
    std::string path = std::string(g_exe_name) + ".log";
    std::ofstream outfile(path, std::ios::out | std::ios::app);
    if (outfile.is_open()) {
        outfile << "Commit ID: " << GIT_VERSION << std::endl;
        outfile << "Git path: " << GIT_PATH << std::endl;
        outfile << "Compile time: " << __TIME__ << " " << __DATE__ << std::endl;
    }
    for (uint32_t i = 0; i < size; i++) {
        fprintf(stderr, "%d %s \n", i, strings[i]);
        if (outfile.is_open()) {
            outfile << strings[i] << std::endl;
        }
    }
    if (outfile.is_open()) {
        outfile.close();
    }
#endif
    free(strings);
#endif
    signal(signum, SIG_DFL); /* 还原默认的信号处理handler */
    fprintf(stderr, "%s quit execute now\n", g_exe_name);
    fflush(stderr);
    MY_EPOLL.EpoolQuit();
    exit(-1);
}
#endif

static void print_version(void)
{
    std::cout << "==============================================================" << std::endl;
    std::cout << "This is dreame 3d printer MQTT Process!!" << std::endl;
    std::cout << "commit ID: " << GIT_VERSION << std::endl;
    std::cout << "git path: " << GIT_PATH << std::endl;
    std::cout << "Compile time: " << __TIME__ << " : " << __DATE__ << std::endl;
    std::cout << "Author:          Leo Huang" << std::endl;
    std::cout << "==============================================================" << std::endl;
}

void Clean3mfDirResidue()
{   
    // 清理3mf目录中残留的无效目录
    std::string three_mf_dir = globalConfigObject["gcode_file_dir"].asString();
    std::string gcodes_dir = globalConfigObject["gcode_file_dir"].asString();

    std::vector<std::string> sub_dirs;
    Utils::getFiles(three_mf_dir, sub_dirs);

    for (const auto& sub_path : sub_dirs) {
        // 获取文件名
        std::string dir_name = sub_path.substr(three_mf_dir.length());
        // 只处理目录
        if (Utils::DirectoryExists(three_mf_dir + "/" + dir_name)) {
            // 只处理以.3mf结尾的目录
            if (dir_name.size() >= 4 && dir_name.substr(dir_name.size() - 4) == ".3mf") {
                // 检查gcodes目录下是否有同名3mf文件
                std::string three_mf_file = gcodes_dir + "/" + dir_name.substr(1);
                if (!Utils::FileExists(three_mf_file)) {
                    // 没有同名3mf文件，删除该目录
                    std::string cmd = "rm -rf '" + three_mf_dir + "/" + dir_name + "'";
                    std::cout << "cmd:" << cmd << std::endl;
                    std::string recv;
                    Utils::Execute(cmd, recv);
                }
            }
        }
    }
}

int main(int argc, char* argv[])
{
#if defined(__unix__) || defined(__APPLE__)
    signal(SIGPIPE, _signal_handler); // SIGPIPE，管道破裂。
    signal(SIGSEGV, _signal_handler); // SIGSEGV，非法内存访问
    signal(SIGFPE, _signal_handler);  // SIGFPE，数学相关的异常，如被0除，浮点溢出，等等
    signal(SIGABRT, _signal_handler); // SIGABRT，由调用abort函数产生，进程非正常退出
    g_exe_name = argv[0];
#endif

    // config init
    std::string file_path = std::string(PROJECT_PATH) + "/mqttproc/etc/config/mqtt_proc.json";
    if (argc > 1) {
        file_path = argv[1];
    }
    if (!LoadConfig(file_path)) {
        std::cout << "load config error" << std::endl;
        return -1;
    }

    Logger::init();
    LOG_INFO("mqttproc start...");

    print_version();

    Clean3mfDirResidue();

    // 检查本地SSL/TLS证书是否存在,如果不存在则从内网服务器获取, 3次重试, 获取不到则退出
    // TODO 等自启动服务配置号等待网络服务启动后再启动本服务, 下面的等待网络可用的逻辑可以删除
    std::string msg = "network is not available, wait for 1 second";
    while (true)
    {
        if (Utils::isNetworkAvailable()){
            LOG_INFO("network is available...");
            break;
        } else {
            std::this_thread::sleep_for(std::chrono::seconds(2));
            std::cout << msg << std::endl;
            LOG_INFO(msg);
        }
    }
    // TODO 证书信息持久化后 此处逻辑需要删除 检查本地SSL/TLS证书是否存在,如果不存在则从内网服务器获取, 3次重试, 获取不到则退出
    if (!Utils::FileExists(globalConfigObject["client_cert_file"].asString()) || globalConfigObject["device_id"].asString().empty()) {
        int count = 3;
        while (count > 0) {
            if (Utils::isNetworkAvailable()) {
                std::string server_url = globalConfigObject["server_url"].asString();
                Utils::GetDeviceCert(server_url);
                break;
            }
            count--;
        }
        if (count == 0) {
            std::string msg = "get cert fail, please check the network, program exit";
            std::cout << msg << std::endl;
            LOG_ERROR(msg);
            return -1;
        }
    }
    //同步网络时间
    Utils::syncNetworkTime();

    CircularQueue<Result> uds_server_queue;
    // 队列存储mqtt订阅topic所受到的订阅消息
    CircularQueue<std::string> mqtt_recv_queue;

    // std::unique_ptr<DreameUds> dreame_uds(new DreameUds());
    std::shared_ptr<DreameUds> dreame_uds(new DreameUds());
    // uds_server_queue在这里传入, 用于接收mainproc传过来的数据
    dreame_uds->Init(globalConfigObject["mqttproc_uds"].asString(), uds_server_queue);

    std::shared_ptr<Dreame3dMqtt> mqtt = std::make_shared<Dreame3dMqtt>();
    if (globalConfigObject["mqtt_ssl"].asBool()){
        mqtt->SSLInit();  // 开启SSL/TLS认证
    } else {
        mqtt->Init();
    }
    std::unique_ptr<DreameSub> iot_mqtt_sub(new DreameSub(mqtt, dreame_uds));
    iot_mqtt_sub->Init(mqtt_recv_queue);
    // 支持通配符匹配
    std::string topic_start = mqtt->GetProjectName()+mqtt->GetModelId()+mqtt->GetDeviceId();
    iot_mqtt_sub->SubTopic(topic_start+"/thing/down");
    mqtt->Loop();

    dreame_uds->Stop();
    return 0;
}
