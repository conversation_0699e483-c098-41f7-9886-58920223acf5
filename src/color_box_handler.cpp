/**
 * @file color_box_handler.cpp
 * <AUTHOR> (g<PERSON><PERSON><PERSON>@dreame.tech)
 * @brief 
 * @version 0.1
 * @date 2025-04-10
 * @copyright Copyright (c) {2025} 追觅科技有限公司版权所有
 */

#include "color_box_handler.h"
#include "logger.h"
#include "utils.h"

ColorBoxHandler::ColorBoxHandler() {}

ColorBoxHandler::~ColorBoxHandler() {}

void ColorBoxHandler::ColorBoxLoadedSlot(DreameSub* dreame_sub_ptr, Json::Value req_json){
    Json::Value response;
    response = Utils::GetResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    response["type"] = "colorbox.loadedslot";
    response["action"] = "get";
    response["data"]["data"]["id"] = 0;  // 0主料盒ID 1从料盒ID
    response["data"]["data"]["loadedSlot"] = 0;   // 当前料盒已加载的料槽index, 未加载则为-1
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadMsg(response_string);
}

void ColorBoxHandler::ColorBoxInfoGet(DreameSub* dreame_sub_ptr, Json::Value req_json){
    Json::Value response;
    response = Utils::GetResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    // TODO: 返回数据待和多色盒子联调确认
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadMsg(response_string);
}

void ColorBoxHandler::ColorBoxDryerSet(DreameSub* dreame_sub_ptr, Json::Value req_json){
    Json::Value send_data;
    Json::Value response;
    Json::Value drying_status_response;
    int status = req_json["data"]["records"][0]["status"].asInt(); // 0关闭 1开启
    send_data["id"] = req_json["id"].asString();
    send_data["src"] = "mqtt_proc";
    send_data["des"] = "acf_box";
    send_data["method"] = "set_heater";
    send_data["params"]["status"] = status;
    std::string send_data_string;
    JsoncppParseRead::ParseJsonToString(send_data_string, send_data);
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), send_data_string);

    response = Utils::GetResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    drying_status_response["status"] = 1;
    drying_status_response["targetTemp"] = 60;
    drying_status_response["duration"] = 120;
    drying_status_response["remainTime"] = 24;
    response["data"]["data"]["records"].append(drying_status_response);
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadMsg(response_string);
}

void ColorBoxHandler::ColorBoxFilFeedSet(DreameSub* dreame_sub_ptr, Json::Value req_json){
    Json::Value send_data;
    Json::Value response;
    int id = req_json["data"]["records"][0]["id"].asInt(); 
    int slotIndex = req_json["data"]["records"][0]["feedStatus"]["slotIndex"].asInt();
    int type = req_json["data"]["records"][0]["feedStatus"]["type"].asInt();
    send_data["id"] = req_json["id"].asString();
    send_data["src"] = "mqtt_proc";
    send_data["des"] = "acf_box";
    send_data["method"] = "set_feed";
    send_data["params"]["id"] = id;  // 盒子id
    send_data["params"]["slotIndex"] = slotIndex; // 料槽index
    send_data["params"]["type"] = type;  // 1进料 2退料 3结束进退料 
    std::string send_data_string;
    JsoncppParseRead::ParseJsonToString(send_data_string, send_data);
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), send_data_string);

    response = Utils::GetResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadMsg(response_string);
}

void ColorBoxHandler::ColorBoxFilAutoFeedSet(DreameSub* dreame_sub_ptr, Json::Value req_json){
    Json::Value send_data;
    Json::Value response;
    int id = req_json["data"]["records"][0]["id"].asInt(); 
    int autoFeed = req_json["data"]["records"][0]["autoFeed"].asInt();
    send_data["id"] = req_json["id"].asString();
    send_data["src"] = "mqtt_proc";
    send_data["des"] = "acf_box";
    send_data["method"] = "set_auto_feed";
    send_data["params"]["id"] = id;  // 盒子id
    send_data["params"]["autoFeed"] = autoFeed;  // 1开启 2关闭
    std::string send_data_string;
    JsoncppParseRead::ParseJsonToString(send_data_string, send_data);
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), send_data_string);

    response = Utils::GetResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadMsg(response_string);
}

void ColorBoxHandler::ColorBoxSlotRefreshSet(DreameSub* dreame_sub_ptr, Json::Value req_json){
    Json::Value send_data;
    Json::Value response;
    Json::Value status_response;
    Json::Value slot_response;
    int id = req_json["data"]["records"][0]["id"].asInt(); 
    int index = req_json["data"]["records"][0]["slots"]["index"].asInt();
    send_data["id"] = req_json["id"].asString();
    send_data["src"] = "mqtt_proc";
    send_data["des"] = "acf_box";
    send_data["method"] = "set_slot_refresh";
    send_data["params"]["id"] = id;  // 盒子id
    send_data["params"]["index"] = index;  // 料槽index
    std::string send_data_string;
    JsoncppParseRead::ParseJsonToString(send_data_string, send_data);
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), send_data_string);

    response = Utils::GetResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();

    status_response["index"] = 1;
    slot_response["sku"] = "HPLGR-104";
    slot_response["type"] = "PLA";
    // 修复部分：正确构造 color 数组
    Json::Value color_array;
    color_array.append(255); 
    color_array.append(255);
    color_array.append(255); 
    slot_response["color"] = color_array;
    slot_response["edit_status"] = 0;
    slot_response["status"] = 1;
    status_response["slots"].append(slot_response);
    response["data"]["data"]["records"].append(status_response);
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadMsg(response_string);
}

void ColorBoxHandler::ColorBoxSlotInfoSet(DreameSub* dreame_sub_ptr, Json::Value req_json){
    Json::Value send_data;
    Json::Value response;
    Json::Value status_response;
    Json::Value slot_response;
    int id = req_json["data"]["records"][0]["id"].asInt(); 
    int index = req_json["data"]["records"][0]["slots"]["index"].asInt();
    std::string type = req_json["data"]["records"][0]["slots"]["type"].asString();
    Json::Value color_array = req_json["data"]["records"][0]["slots"]["color"];
    send_data["id"] = req_json["id"].asString();
    send_data["src"] = "mqtt_proc";
    send_data["des"] = "acf_box";
    send_data["method"] = "set_slot_refresh";
    send_data["params"]["id"] = id;  // 盒子id
    send_data["params"]["index"] = index;  // 料槽index
    send_data["params"]["type"] = type;  // 耗材类型
    send_data["params"]["color"] = color_array;  // 耗材颜色
    std::string send_data_string;
    JsoncppParseRead::ParseJsonToString(send_data_string, send_data);
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), send_data_string);

    response = Utils::GetResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();

    status_response["id"] = 1;
    slot_response["index"] = 0;
    slot_response["sku"] = "HPLGR-104";
    slot_response["type"] = "PLA";
    slot_response["color"] = color_array;
    slot_response["edit_status"] = 0;
    slot_response["status"] = 1;
    status_response["slots"].append(slot_response);
    response["data"]["data"]["records"].append(status_response);
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadMsg(response_string);
}
