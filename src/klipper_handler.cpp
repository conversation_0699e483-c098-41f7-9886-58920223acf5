/**
 * @file klipper_handler.cpp
 * <AUTHOR> (g<PERSON><PERSON><PERSON>@dreame.tech)
 * @brief 
 * @version 0.1
 * @date 2025-03-26
 * @copyright Copyright (c) {2025} 追觅科技有限公司版权所有
 */

 #include "klipper_handler.h"
 #include "logger.h"
 #include "utils.h"

 KlipperHandler::Klip<PERSON><PERSON><PERSON><PERSON>() {}

 KlipperHandler::~<PERSON><PERSON><PERSON><PERSON>andler() {}


void KlipperHandler::UploadStatusNow(DreameSub* dreame_sub_ptr){
    Json::Value send_data;
    send_data["src"] = "mqtt_proc";
    send_data["des"] = "klippy_proc";
    send_data["method"] = "upload.status.now";
    std::string send_data_string;
    JsoncppParseRead::ParseJsonToString(send_data_string, send_data);
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), send_data_string);
}

void KlipperHandler::GetKlipperInfo(DreameSub* dreame_sub_ptr){
    Json::Value send_data;
    send_data["src"] = "mqtt_proc";
    send_data["des"] = "klippy_proc";
    send_data["id"] = Utils::CreateUUID();
    send_data["method"] = "get.klipper.info";
    std::string send_data_string;
    JsoncppParseRead::ParseJsonToString(send_data_string, send_data);
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), send_data_string);
}

void KlipperHandler::UploadMachineState(DreameSub* dreame_sub_ptr){
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["method"] = "thing.property.machine.up";
    response["data"]["machine_state"] = dreame_sub_ptr->klipper_info_->machine_state;
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
    return;
}

void KlipperHandler::ScriptSet(DreameSub* dreame_sub_ptr, Json::Value req_json){
    dreame_sub_ptr->AddTaskID(req_json["id"].asString(), req_json["method"].asString(), TASK_LONG_TIMEOUT);
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), dreame_sub_ptr->KlipperDataJsonParse(req_json));
    return;
}

void KlipperHandler::CommandDispatch(DreameSub* dreame_sub_ptr, Json::Value req_json){
    // 特殊处理下获取打印队列的请求, 其它请求透传到klipper
    if (req_json["method"].asString()=="thing.property.print.get" && req_json["data"].isArray()){
        for (int i = 0; i < req_json["data"].size(); i++) {
            if (req_json["data"][i].asString() == "print_queue_list") {
                // 从打印任务队列中获取数据
                dreame_sub_ptr->print_task_handler_->GetPrintTaskQueueList(dreame_sub_ptr, req_json);
                break;
            }
        }
    }
    dreame_sub_ptr->AddTaskID(req_json["id"].asString(), req_json["method"].asString(), TASK_SHORT_TIMEOUT);
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), dreame_sub_ptr->KlipperDataJsonParse(req_json));
    // 如果是thing.service.print_clear.set, APP端要求上报一次机器状态
    if (req_json["method"].asString() == "thing.service.print_clear.set") {
        UploadMachineState(dreame_sub_ptr);
    }
    return;
}


void KlipperHandler::HandleClientCode(DreameSub* dreame_sub_ptr, Json::Value req_json){
    // 推送到UI端
    Json::Value send_data;
    send_data["src"] = "mqtt_proc";
    send_data["des"] = "gui_proc";
    send_data["id"] = Utils::CreateUUID();
    send_data["method"] = req_json["method"].asString();
    send_data["result"]["status"]["client_error_code"] = req_json["data"]["error_info"];
    std::string send_data_string;
    JsoncppParseRead::ParseJsonToString(send_data_string, send_data);
    LOG_INFO(send_data_string);
    LOG_INFO("FD: " + std::to_string((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd()));
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), send_data_string);

    // 推送到MQTT服务器
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["method"] = "thing.property.error_code.up";
    response["data"]["error_code"] = req_json["data"]["error_info"];
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

std::string KlipperHandler::HandleErrorCodeCategory(Json::Value req_json) {
    // req_json["data"]["error_info"] = {"code":"007","message":"Must home axis first", "values": [10.0, 10.0, 0.0, [0.0]]}

}