/**
 * @file config.cpp
 * <AUTHOR> (guo<PERSON><PERSON>@dreame.tech)
 * @brief 
 * @version 0.1
 * @date 2025-03-10
 * @copyright Copyright (c) {2025} 追觅科技有限公司版权所有
 */
#include <iostream>
#include <fstream>
#include "config.h"
#include "jsonparse.h"
// #include "logger.h"
#ifdef __GLIBC__
#include <execinfo.h>
#endif

// 定义全局变量
Json::Value globalConfigObject;

void ConfigDefaultInit(){
    globalConfigObject["mqtt_ssl"] = true;
    globalConfigObject["mqtt_addr"] = "120.24.161.231";
    globalConfigObject["mqtt_port"] = 8883;
    globalConfigObject["machine_keep_alive_period"] = 20;
    globalConfigObject["mqttproc_config_file_path"] = std::string(PROJECT_PATH) + "/mqttproc/etc/config/mqtt_proc.json";
    globalConfigObject["server_ca_cert_file"] = std::string(PROJECT_PATH) + "/printer_data/certs/ca.pem";
    globalConfigObject["client_cert_file"] = std::string(PROJECT_PATH) + "/printer_data/certs/client.pem";
    globalConfigObject["client_key_file"] = std::string(PROJECT_PATH) + "/printer_data/certs/client.key";
    globalConfigObject["mqttproc_uds"] = std::string(PROJECT_PATH) + "/printer_data/comms/mqtt_proc.sock";
    globalConfigObject["gcode_file_dir"] = std::string(PROJECT_PATH) + "/printer_data/gcodes";
    globalConfigObject["3mf_dir"] = std::string(PROJECT_PATH) + "/printer_data/3mf_dir";
    globalConfigObject["upgradeflag_path"] = "/userdata/upgradeflag";
    globalConfigObject["brightness_path"] = "/sys/class/leds/line/brightness";
    globalConfigObject["server_url"] = "http://47.107.130.160:85";
    globalConfigObject["divice_id"] = "";
    globalConfigObject["divice_cn"] = "";
    Json::Value log;
    log["log_path"] = std::string(PROJECT_PATH) + "/printer_data/logs/mqtt_proc.log";
    log["max_size"] = 20;
    log["max_files"] = 3;
    log["level"] = "debug";
    globalConfigObject["log"] = log;
}

bool LoadConfig(const std::string &file_path) {
    std::ifstream file(file_path);
    if (!file.is_open()) {
        std::cout << "file_path not exist: " << file_path << std::endl;
        // 赋值默认值, 避免启动失败
        ConfigDefaultInit();
        return true;
    }
    if (JsoncppParseRead::ReadFileToJson(file_path, globalConfigObject)) {
        std::cout << "Parsed JSON from file:" << globalConfigObject << std::endl;
        std::string config_string;
        JsoncppParseRead::ParseJsonToString(config_string, globalConfigObject);
    } else {
        std::cout << "Failed to read JSON from file!" << std::endl;
        // 使用默认值初始化配置项
        ConfigDefaultInit();
        return true;
    }
    return true;
}

bool SetMachineKeepAlivePeriod(int period) {
    globalConfigObject["machine_keep_alive_period"] = period;
    std::string mqttproc_config_file_path = globalConfigObject["mqttproc_config_file_path"].asString();
    if (Utils::FileExists(mqttproc_config_file_path)) {
        std::ofstream file(mqttproc_config_file_path);
        file << globalConfigObject;
        file.close();
    }
    std::string recv;
    Utils::Execute("sync", recv);
    return true;
}

