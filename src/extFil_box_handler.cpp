/**
 * @file extFil_box_handler.cpp
 * <AUTHOR> (guo<PERSON><PERSON>@dreame.tech)
 * @brief 
 * @version 0.1
 * @date 2025-04-10
 * @copyright Copyright (c) {2025} 追觅科技有限公司版权所有
 */

#include "extFil_box_handler.h"
#include "logger.h"
#include <fstream>
#include "utils.h"
#include "ErrorCodeMap.h"
 
ExtFilBoxHandler::ExtFilBoxHandler() {}

ExtFilBoxHandler::~ExtFilBoxHandler() {}

void ExtFilBoxHandler::ExtFilBoxFilSet(DreameSub* dreame_sub_ptr, Json::Value req_json){
    Json::Value response;
    Json::Value file_json;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    // TODO 实际文件位置待定
    std::string file_path = "/home/<USER>/printer_data/ext_fil_box.json";
    if (!Utils::FileExists(file_path)) {
        file_json["ext_fil_box_type"]["type"] = 1;
        file_json["ext_fil_box_type"]["color"] = "ffffff";
        std::ofstream file(file_path);
        file << file_json;
        file.close();
    }
    int ext_fil_box_type = req_json["data"]["ext_fil_box_type"]["type"].asInt();
    std::string ext_fil_box_color = req_json["data"]["ext_fil_box_type"]["color"].asString();
    JsoncppParseRead::ReadFileToJson(file_path, file_json);
    if (req_json["data"]["ext_fil_box_type"].isMember("type")){
        file_json["ext_fil_box_type"]["type"] = req_json["data"]["ext_fil_box_type"]["type"].asInt();
    }
    if (req_json["data"]["ext_fil_box_type"].isMember("color")){
        file_json["ext_fil_box_type"]["color"] = req_json["data"]["ext_fil_box_type"]["color"].asString();
    }
    std::ofstream out_file(file_path);
    out_file << file_json;
    out_file.close();

    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);

    // 修改后上报外置料架信息
    UploadBoxFilInfo(dreame_sub_ptr);
}

void ExtFilBoxHandler::ExtFilBoxFilGet(DreameSub* dreame_sub_ptr, Json::Value req_json){
    Json::Value file_json;
    Json::Value response;
    // TODO 实际文件位置待定
    std::string file_path = "/home/<USER>/printer_data/ext_fil_box.json";
    if (!Utils::FileExists(file_path)) {
        file_json["ext_fil_box_type"]["type"] = 1;
        file_json["ext_fil_box_type"]["color"] = "ffffff";
        std::ofstream file(file_path);
        file << file_json;
        file.close();
    }
    JsoncppParseRead::ReadFileToJson(file_path, file_json);
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = "thing.property.ext_fil_box.up";
    response["data"]["ext_fil_box_type"] = file_json["ext_fil_box_type"];
    response["data"]["ext_fil_box_fil_feed"]["loaded"] = 1;  // TODO 实际状态待获取

    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

void ExtFilBoxHandler::UploadBoxFilInfo(DreameSub* dreame_sub_ptr) {
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["method"] = "thing.property.ext_fil_box.up";
    // TODO 实际文件位置待定
    std::string file_path = "/home/<USER>/printer_data/ext_fil_box.json";
    Json::Value file_json;
    JsoncppParseRead::ReadFileToJson(file_path, file_json);
    response["data"]["ext_fil_box_type"] = file_json["ext_fil_box_type"];
    response["data"]["ext_fil_box_fil_feed"]["loaded"] = 1;  // TODO 实际状态待获取
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

