#include "video_stream.h"

VideoStream::VideoStream(){}

VideoStream::~VideoStream() {}


void VideoStream::CommandDispatch(DreameSub* dreame_sub_ptr, Json::Value req_json)
{
    dreame_sub_ptr->AddTaskID(req_json["id"].asString(), req_json["method"].asString(), TASK_SHORT_TIMEOUT);
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), dreame_sub_ptr->VideoDataJsonParse(req_json));
}

bool VideoStream::StartStream(DreameSub* dreame_sub_ptr, Json::Value req_json)
{
    Json::Value send_data;
    send_data["id"] = req_json["id"].asString();
    send_data["src"] = src;
    send_data["des"] = des;
    send_data["method"] = "set_video_push";
    send_data["params"] = req_json["data"];
    send_data["params"]["sta"] = "START";
    std::string send_data_string;
    JsoncppParseRead::ParseJsonToString(send_data_string, send_data);
    dreame_sub_ptr->AddTaskID(req_json["id"].asString(), req_json["method"].asString(), TASK_SHORT_TIMEOUT);
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), send_data_string);
    return true;
}

bool VideoStream::StopStream(DreameSub* dreame_sub_ptr, Json::Value req_json)
{
    Json::Value send_data;
    send_data["id"] = req_json["id"].asString();
    send_data["src"] = src;
    send_data["des"] = des;
    send_data["method"] = "set_video_push";
    send_data["params"]["sta"] = "STOP";
    std::string send_data_string;
    JsoncppParseRead::ParseJsonToString(send_data_string, send_data);
    dreame_sub_ptr->AddTaskID(req_json["id"].asString(), req_json["method"].asString(), TASK_SHORT_TIMEOUT);
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), send_data_string);
    return true;    
}