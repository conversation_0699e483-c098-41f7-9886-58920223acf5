/**
 * @file ota_handler.cpp
 * <AUTHOR> (guo<PERSON><PERSON>@dreame.tech)
 * @brief 
 * @version 0.1
 * @date 2025-03-27
 * @copyright Copyright (c) {2025} 追觅科技有限公司版权所有
 */

 #include "ota_handler.h"
 #include "logger.h"
 #include "utils.h"

OtaHandler::<PERSON><PERSON><PERSON><PERSON><PERSON>()
{}

OtaHandler::~<PERSON><PERSON><PERSON><PERSON><PERSON>() {}

void OtaHandler::HandleOtaVersionFromOtaProc(DreameSub* dreame_sub_ptr, Json::Value req_json) {
    dreame_sub_ptr->ota_info->current_ota_version = req_json["result"]["current_ota_version"];
}

void OtaHandler::GetClientCurrentOtaVersion(DreameSub* dreame_sub_ptr) {
    if (dreame_sub_ptr->ota_info->current_ota_version.empty()) {
        Json::Value send_data;
        send_data["method"] = "get_client_cur_ver";
        send_data["src"] = "mqtt_proc";
        send_data["des"] = "ota_proc";
        send_data["params"] = Json::Value(Json::objectValue);
        std::string send_data_string;
        JsoncppParseRead::ParseJsonToString(send_data_string, send_data);
        (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), send_data_string);
        std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    }
}

void OtaHandler::OtaVersionFetch(DreameSub* dreame_sub_ptr) {
    Json::Value response;
    std::string response_string;
    response = Utils::GetMqttResponseJson();
    response["method"] = "thing.property.ota.fetch";
    Json::Value data_array;
    data_array.append("latest_ota_version"); 
    response["data"] = data_array;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

void OtaHandler::OtaVersionUpload(DreameSub* dreame_sub_ptr){
    Json::Value response;
    std::string response_string;
    response = Utils::GetMqttResponseJson();
    response["method"] = "thing.property.ota.up";
    GetClientCurrentOtaVersion(dreame_sub_ptr);
    if (dreame_sub_ptr->ota_info->current_ota_version.empty()) {
        std::string msg = "OtaVersionUpload current_ota_version is empty";
        std::cout << msg << std::endl;
        LOG_ERROR(msg);
        return;
    }
    response["data"]["current_ota_version"] = dreame_sub_ptr->ota_info->current_ota_version;
    // response["data"]["current_ota_version"] = current_ota_version;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

void OtaHandler::OtaVersionSet(DreameSub* dreame_sub_ptr, Json::Value req_json){
    Json::Value response;
    std::string response_string;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    if (!req_json["data"]["latest_ota_version"].isNull()) {
        dreame_sub_ptr->ota_info->latest_ota_version = req_json["data"]["latest_ota_version"];
    }
    std::string latest_version_str;
    JsoncppParseRead::ParseJsonToString(latest_version_str, dreame_sub_ptr->ota_info->latest_ota_version);
    LOG_INFO(std::string("OtaVersionSet latest_ota_version: ") + latest_version_str);
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

void OtaHandler::OtaVersionGet(DreameSub* dreame_sub_ptr, Json::Value req_json){
    Json::Value response;
    std::string response_string;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = "thing.property.ota.up";
    for (int i = 0; i < req_json["data"].size(); i++) {
        if (req_json["data"][i].asString() == "current_ota_version") {
            if (dreame_sub_ptr->ota_info->current_ota_version.empty()) {
                GetClientCurrentOtaVersion(dreame_sub_ptr);
            }
            if (!dreame_sub_ptr->ota_info->current_ota_version.empty()) {
                response["data"]["current_ota_version"] = dreame_sub_ptr->ota_info->current_ota_version;
            } else{
                std::string msg = "current_ota_version is empty";
                LOG_ERROR(msg);
            }
        }
        if (req_json["data"][i].asString() == "progres_ota") {
            response["data"]["progres_ota"]["status"] = dreame_sub_ptr->ota_info->status;
            response["data"]["progres_ota"]["progress"] = dreame_sub_ptr->ota_info->progress;
        }
    }
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

void OtaHandler::OtaUpgradeStart(DreameSub* dreame_sub_ptr, Json::Value req_json){
    Json::Value send_data;
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();

    send_data["method"] = "set_ota_upgrade_start";
    send_data["src"] = "mqtt_proc";
    send_data["des"] = "ota_proc";
    send_data["params"] = Json::Value(Json::objectValue);
    std::string send_data_string;
    JsoncppParseRead::ParseJsonToString(send_data_string, send_data);
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), send_data_string);

    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

void OtaHandler::OtaUpgradeCancel(DreameSub* dreame_sub_ptr, Json::Value req_json){
    Json::Value send_data;
    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();

    if (dreame_sub_ptr->ota_info->status == 3) {
        response["data"]["code"] = 1;
        response["data"]["msg"] = "Upgrading and can't be canceled";
    } else {
        // 取消升级后状态置零
        dreame_sub_ptr->ota_info->status = 0;
        send_data["method"] = "set_ota_upgrade_cancel";
        send_data["src"] = "mqtt_proc";
        send_data["des"] = "ota_proc";
        send_data["params"] = Json::Value(Json::objectValue);
        std::string send_data_string;
        JsoncppParseRead::ParseJsonToString(send_data_string, send_data);
        (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), send_data_string);
    }
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

void OtaHandler::GetNewVer(DreameSub* dreame_sub_ptr, Json::Value req_json) {
    Json::Value response_json;
    response_json["result"]["sta"] = "OK";
    GetClientCurrentOtaVersion(dreame_sub_ptr);
    OtaVersionUpload(dreame_sub_ptr);
    OtaVersionFetch(dreame_sub_ptr);
    std::string latest_version_str;
    JsoncppParseRead::ParseJsonToString(latest_version_str, dreame_sub_ptr->ota_info->latest_ota_version);
    LOG_INFO(std::string("GetNewVer latest_ota_version: ") + latest_version_str);
    response_json["result"]["latest_ota_version"] = dreame_sub_ptr->ota_info->latest_ota_version;
    response_json["method"] = req_json["method"].asString();
    response_json["src"] = "mqtt_proc";
    response_json["des"] = "ota_proc";

    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response_json);
    (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), response_string);
}

void OtaHandler::SetOtaUpgradeProgress(DreameSub* dreame_sub_ptr, Json::Value req_json){
    dreame_sub_ptr->ota_info->status = req_json["params"]["status"].asInt();
    dreame_sub_ptr->ota_info->progress = req_json["params"]["progress"].asInt();

    Json::Value response;
    response = Utils::GetMqttResponseJson();
    response["method"] = "thing.property.ota.up";
    response["data"]["progres_ota"]["id"] = req_json["id"];
    response["data"]["progres_ota"]["status"] = dreame_sub_ptr->ota_info->status;
    response["data"]["progres_ota"]["progress"] = dreame_sub_ptr->ota_info->progress;
    std::string response_mqtt_string;
    JsoncppParseRead::ParseJsonToString(response_mqtt_string, response);
    // 上报到mqtt服务端
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_mqtt_string);
}




