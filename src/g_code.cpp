#include "g_code.h"
#include "logger.h"
#include <cstdio>
#include <signal.h>
#include <thread>
#include <chrono>
#include <iostream>
#include <sstream>
#include <unistd.h>
#include <fstream>
#include <sys/wait.h>
#include <dirent.h>
#include <limits.h>
#include <stdlib.h>

Gcode::Gcode() {}

Gcode::~Gcode() {}

bool Gcode::GcodeDownload(const std::string& url, const std::string& local_path) {
    if (url.empty() || local_path.empty()) {
        LOG_ERROR("Invalid url or local path");
        return false;
    }

    // 重置状态
    download_cancel_ = false;
    download_progress_ = 0.0f;

    // 构建wget命令
    std::string command = "wget -c -q --show-progress \"" + url + "\" -O \"" + local_path + "\" 2>&1";
    LOG_INFO("Download command: {}", command);
    std::cout << "Download command: " << command << std::endl;

    // 打开管道
    wget_pipe_ = popen(command.c_str(), "r");
    if (!wget_pipe_) {
        LOG_ERROR("Failed to open pipe for wget");
        std::cout << "Failed to open pipe for wget" << std::endl;
        return false;
    }

    char buffer[128];
    std::string line;
    std::string last_line;
    while (fgets(buffer, sizeof(buffer), wget_pipe_) != nullptr) {
        if (download_cancel_) {
            CancelDownload();
            return false;
        }
        line = buffer;
        last_line = line;
        // 解析进度
        size_t pos = line.find("%");
        if (pos != std::string::npos) {
            std::string progress_str = line.substr(pos - 3, 3);
            try {
                float progress = std::stof(progress_str);
                download_progress_ = progress;
                // LOG_INFO("Download gcode file progress: {}%", progress);
            } catch (const std::exception& e) {
                LOG_ERROR("Failed to parse progress: {}", e.what());
                std::cout << "Failed to parse progress: " << e.what() << std::endl;
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    int status = pclose(wget_pipe_);
    wget_pipe_ = nullptr;
    if (WIFEXITED(status) && WEXITSTATUS(status) == 0) {
        LOG_INFO("Download gcode file completed successfully");
        std::cout << "Download gcode file completed successfully" << std::endl;
        download_progress_ = 100.0f;
        return true;
    } else {
        LOG_ERROR("Download gcode file failed, last line: {}", last_line);
        std::cout << "Download gcode file failed, last line: " << last_line << std::endl;
        return false;
    }
}

void Gcode::CancelDownload() {
    std::string cmd = "ps -eo pid,cmd | grep 'wget -c -q --show-progress' | grep -v grep | awk '{print $1}' | xargs -r kill -9";
    std::cout << "kill wget process cmd: " << cmd << std::endl;
    LOG_INFO(cmd);
    std::string recv;
    Utils::Execute(cmd, recv);
    wget_pipe_ = nullptr;
    return;
    download_cancel_ = true;
    if (wget_pipe_) {
        // 获取wget进程ID并终止它
        int pid = -1;
        char cmd[128];
        sprintf(cmd, "pgrep -P %d", getpid());
        FILE* fp = popen(cmd, "r");
        if (fp) {
            if (fscanf(fp, "%d", &pid) == 1) {
                std::cout << "kill wget pid: : " << pid << std::endl;
                LOG_INFO("kill wget pid: {}", pid);
                kill(pid, SIGTERM);
            }
            pclose(fp);
        }
        
        //等待子进程退出
        int status;
        waitpid(pid, &status, 0);
        
        pclose(wget_pipe_);
        wget_pipe_ = nullptr;
    }
}


int Gcode::GetDownloadProgress() const {
    return download_progress_;
}

void Gcode::FileSdList(DreameSub* dreame_sub_ptr, Json::Value req_json){
    Json::Value response;
    Json::Value status_response;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    std::string sub_dir = req_json["data"]["file_path"].asString();
    std::string targetPath;
    status_response["file_list"] = Json::arrayValue;
    if (sub_dir == ""){
        Utils::scanDiskDirectory(status_response["file_list"]);
    } else {
        if (sub_dir.length() == 5) {
            if (sub_dir.substr(0, 5) == "local") {
                targetPath = globalConfigObject["gcode_file_dir"].asString();
                sub_dir = sub_dir.substr(5);
            }
        } else if (sub_dir.length() >= 6 && sub_dir.substr(0, 6) == "local/") {
            targetPath = globalConfigObject["gcode_file_dir"].asString();
            sub_dir = sub_dir.substr(6);
        } else if (sub_dir.length() >= 6 && sub_dir.substr(0, 5) == "udisk") {
            targetPath = "/mnt";
        }
        Utils::scanDirectory(targetPath, sub_dir, status_response["file_list"]);
    }
    response["data"] = status_response;
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

void Gcode::FileInfo(DreameSub* dreame_sub_ptr, Json::Value req_json){
    std::string file_path = req_json["data"]["file_path"].asString();
    Json::Value response;
    std::string udisk_file_3mf_dir = "";
    std::string udisk_file_name = "";
    std::string recv;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    response["data"]["file_path"] = file_path;
    response["data"]["plate_num"] = 0;
    if (file_path.substr(0, 6) == "local/"){
        file_path = file_path.substr(6);
    } else if (file_path.substr(0, 5) == "udisk") {
        file_path = file_path.substr(7);
        std::string file_path_tmp = "/home/<USER>/printer_data/tmp";
        if (!Utils::DirectoryExists(file_path_tmp)) {
            Utils::Execute("mkdir -p " + file_path_tmp, recv);
        }
        udisk_file_name = file_path.substr(file_path.rfind("/") + 1);
        udisk_file_3mf_dir = file_path_tmp + "/" + udisk_file_name.substr(0, udisk_file_name.size() - 4);
        std::string cmd = "7z x /mnt/" + req_json["data"]["file_path"].asString() + " -o" + udisk_file_3mf_dir;
        std::cout << "cmd: " << cmd << std::endl;
        if (!Utils::DirectoryExists(udisk_file_3mf_dir)) {
            Utils::Execute(cmd, recv);
        }
    }
    std::string file_3mf_dir = globalConfigObject["gcode_file_dir"].asString() + "/." + file_path;
    if (req_json["data"]["file_path"].asString().substr(0, 5) == "udisk") {
        file_3mf_dir = udisk_file_3mf_dir;
        file_path = udisk_file_name;
    }
    int plate_num = 0;
    if (file_path.substr(file_path.size()-4, file_path.size()) != ".3mf") {
        const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::FileTypeNotSupport);
        response["code"] = error.code;
        response["message"] = error.message;
    } else if (!Utils::DirectoryExists(file_3mf_dir)) { 
        const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::FileDirNotExist);
        response["code"] = error.code;
        response["message"] = error.message;
    } else {
        std::string gcode_file_dir = globalConfigObject["gcode_file_dir"].asString() + "/." + file_path + "/Metadata";
        if (req_json["data"]["file_path"].asString().substr(0, 5) == "udisk") {
            gcode_file_dir = udisk_file_3mf_dir + "/Metadata";
        }
        // 获取3mf_dir目录下以.gcode结尾的文件的数量
        DIR* dir = opendir(gcode_file_dir.c_str());
        struct dirent* entry;
        while ((entry = readdir(dir)) != nullptr) {
            std::string filename = entry->d_name;
            // 判断是否以 .gcode 结尾
            if (filename.length() >= 6 && filename.compare(filename.length() - 6, 6, ".gcode") == 0) {
                plate_num++;
            }
        }
        closedir(dir);
    }
    if (Utils::DirectoryExists(udisk_file_3mf_dir)){
        std::string rm_cmd = "rm -r " + udisk_file_3mf_dir;
        Utils::Execute(rm_cmd, recv);
    }
    response["data"]["plate_num"] = plate_num;
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

void Gcode::FilePlateInfo(DreameSub* dreame_sub_ptr, Json::Value req_json){
    std::string file_path = req_json["data"]["file_path"].asString();
    std::string plate_index = req_json["data"]["plate_index"].asString();
    Json::Value response;
    std::string udisk_file_3mf_dir = "";
    std::string udisk_file_name = "";
    std::string recv;
    response = Utils::GetMqttResponseJson();
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();
    response["data"]["file_path"] = file_path;
    response["data"]["img64"] = "";
    response["data"]["plate_index"] = plate_index;
    if (file_path.substr(0, 6) == "local/"){
        file_path = file_path.substr(6);
    } else if (file_path.substr(0, 5) == "udisk") {
        file_path = file_path.substr(7);
        std::string file_path_tmp = "/home/<USER>/printer_data/tmp";
        if (!Utils::DirectoryExists(file_path_tmp)) {
            Utils::Execute("mkdir -p " + file_path_tmp, recv);
        }
        udisk_file_name = file_path.substr(file_path.rfind("/") + 1);
        udisk_file_3mf_dir = file_path_tmp + "/" + udisk_file_name.substr(0, udisk_file_name.size() - 4);
        std::string cmd = "7z x /mnt/" + req_json["data"]["file_path"].asString() + " -o" + udisk_file_3mf_dir;
        std::cout << "cmd: " << cmd << std::endl;
        if (!Utils::DirectoryExists(udisk_file_3mf_dir)) {
            std::cout << "file_path_tmp " << udisk_file_3mf_dir << std::endl;
            Utils::Execute(cmd, recv);
        }
    }
    // 判断file_path是否以.3mf结尾
    if (file_path.substr(file_path.size()-4, file_path.size()) == ".3mf") { 
        std::string png_file_path = globalConfigObject["gcode_file_dir"].asString() + "/." + file_path + "/Metadata/" + file_path.substr(0, file_path.size() - 4) + "_plate_" + plate_index + ".png";
        if (req_json["data"]["file_path"].asString().substr(0, 5) == "udisk") {
            png_file_path = udisk_file_3mf_dir + "/Metadata/plate_"+plate_index+".png";
        }
        std::ifstream file(png_file_path, std::ios::binary);
        if (file.is_open()) {
            std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
            response["data"]["img64"] = Utils::Base64Encode(content.c_str(), content.size());
        } else {
            const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::FileNotExist);
            response["code"] = error.code;
            response["message"] = error.message;
        }
    } else {
        const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::FileTypeNotSupport);
        response["code"] = error.code;
        response["message"] = error.message;
    }
    if (Utils::DirectoryExists(udisk_file_3mf_dir)){
        std::string rm_cmd = "rm -r " + udisk_file_3mf_dir;
        Utils::Execute(rm_cmd, recv);
    }
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
}

void Gcode::FileDelete(DreameSub* dreame_sub_ptr, Json::Value req_json) {
    bool is_uds = req_json.isMember("params");
    std::string file_path = is_uds ? req_json["params"]["file_path"].asString()
                                   : req_json["data"]["file_path"].asString();

    Json::Value response;
    if (is_uds) {
        response = Utils::GetResponseJson();
        response["src"] = "mqtt_proc";
        response["des"] = "gui_proc";
        response["result"]["file_path"] = file_path;
        response["result"]["sta"] = "OK";
    } else {
        response = Utils::GetMqttResponseJson();
        response["data"]["file_path"] = file_path;
    }
    response["id"] = req_json["id"].asString();
    response["method"] = req_json["method"].asString();

    // 拼接文件路径
    if (file_path.substr(0, 6) == "local/") {
        file_path = file_path.substr(6);
    } else if (file_path.substr(0, 5) == "udisk") {
        file_path = file_path.substr(7);
    }
    std::string full_file_path = globalConfigObject["gcode_file_dir"].asString() + "/" + file_path;

    // realpath归一化校验
    char resolved_file[PATH_MAX] = {0};
    char resolved_gcode[PATH_MAX] = {0};
    std::string gcode_dir = globalConfigObject["gcode_file_dir"].asString();

    if (realpath(full_file_path.c_str(), resolved_file) && realpath(gcode_dir.c_str(), resolved_gcode)) {
        std::string real_full_file_path(resolved_file);
        std::string real_gcode_dir(resolved_gcode);

        if (real_full_file_path == real_gcode_dir || real_full_file_path == real_gcode_dir + "/" || real_full_file_path.find(real_gcode_dir) != 0) {
            const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::FileTypeNotSupport);
            response["code"] = error.code;
            response["message"] = error.message + real_full_file_path + " path not allowed (realpath)";
            std::string response_string;
            JsoncppParseRead::ParseJsonToString(response_string, response);
            // 发送反馈
            if (is_uds) {
                (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), response_string);
            } else {
                (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
            }
            return;
        }
        full_file_path = real_full_file_path;
    }
    std::cout << "realpath: " << full_file_path << std::endl;

    // 检查文件路径是否存在
    if (!Utils::FileExists(full_file_path)) {
        const auto& error = ErrorCodeMap::GetErrorInfo(ErrorCodeMap::ErrorCode::FileNotExist);
        response["code"] = error.code;
        response["message"] = error.message + full_file_path;
    } else {
        // 判断被删除文件是否为3mf文件
        if (full_file_path.size() >= 4 && full_file_path.substr(full_file_path.size() - 4) == ".3mf") {
            std::string filename = full_file_path.substr(full_file_path.find_last_of('/') + 1);
            filename = filename.substr(0, filename.size() - 4);
            std::string delete_dir = globalConfigObject["gcode_file_dir"].asString() + "/." + filename + ".3mf";
            if (Utils::DirectoryExists(delete_dir)) {
                std::string delete_cmd = "rm -rf '" + delete_dir + "'";
                std::string recv;
                Utils::Execute(delete_cmd, recv);
                std::cout << "3mf_Dir " << delete_dir << " deleted successfully" << std::endl;
            }
        }
        std::string cmd = "rm -rf '" + full_file_path + "'";
        std::string recv;
        Utils::Execute(cmd, recv);
        std::cout << full_file_path << " deleted successfully" << std::endl;
    }

    // 发送反馈
    std::string response_string;
    JsoncppParseRead::ParseJsonToString(response_string, response);
    if (is_uds) {
        (*dreame_sub_ptr->getDreameUdsPtr())->SendData((*dreame_sub_ptr->getDreameUdsPtr())->getClientSockFd(), response_string);
    } else {
        (*dreame_sub_ptr->getMqttPtr())->UploadTopicMsg(response_string);
    }
}
