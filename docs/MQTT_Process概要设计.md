# MQTT Process概要设计

|   版本   |    作者 |   参与人员    |   日期    |   备注  |
|:-------:| :-------: | :---------: | :--------: | :-----: |
| v0.1 | leo |  | 2024/12/11 | 初稿 |

## 1. 概述


### 1.1 编写目的

本文档描述主要用于3d打印机与IOT服务连接功能，即Linux客户端的设计目标、系统架构、功能模块、通信流程和安全措施。为iot模块接入编码开发工作提供依据。

### 1.2 文档范围

本文档涵盖了MQTT客户端的概要设计，包括客户端与MQTT代理服务器（Broker）之间的连接、消息发布和订阅机制。

### 1.3 定义、缩略词和缩写
* MQTT：消息队列遥测传输（Message Queuing Telemetry Transport）
* Broker：MQTT代理服务器，负责消息的中转
* Client：MQTT客户端，即本文档所设计的软件
* QoS：服务质量（Quality of Service）

### 1.4 阅读对象

3d打印业务线开发人员，3d打印业务线测试人员，3d打印业务线项目经理，3d打印业务线产品经理

### 1.5 参考资料
[1]. [嵌入式软件框架](https://dreametech.feishu.cn/wiki/O6aUwmLjMi0ZavkA7rEctExrnDI)

[2]. [设备接入平台相关问题沟通](https://dreametech.feishu.cn/wiki/YajAwaKUviE1HOk5UPPc3brInLh)

[3]. [IOT平台设计](https://dreametech.feishu.cn/wiki/WOFrwwaScisEdHkmh5zcRNeenGd)

[4]. [EMQX文档](https://docs.emqx.com/zh/emqx/latest/connect-emqx/c.html)

## 2 项目概述

### 2.1 项目背景
为3d打印机提供云端接入功能，实现与云平台的连接、消息发布和订阅机制, G-code获取，视频流的推送等功能。

### 2.2 设计目标
* 实现MQTT协议的核心功能，包括连接、订阅、发布和消息处理。
* 实现服务器G-code文件下载功能。
* 实时视频流的推流和拉流功能。

## 3. 系统架构

### 3.1 总体架构
客户端采用模块化设计，主要包括以下几个模块：

* 消息处理模块：处理接收到的消息和待发送的消息。
* G-code模块：接收云端切片后的G-code和预览。
* 推流模块：视频流的推送控制。
* 安全模块：处理TLS/SSL加密和认证。

![图1: MQTT_Process流程](MQTT_Process流程.png)

### 3.2 技术选型
* 编程语言：C/C++，考虑到性能和资源消耗。
* 第三方库：使用现有的MQTT库，如paho.mqtt.embedded-c C/C++客户端库。
* 加密库：使用OpenSSL库实现TLS/SSL支持。

## 4. 功能模块

### 4.1 MQTT连接模块
提供单例类，实现MQTT协议的连接、断开，提供话题订阅，订阅话题移除和话题发布等接口。同时还要兼顾网络连接的掉线重连，网络质量评估。
```c++
class Dreame3dMqtt
{
private:
    const std::string CLIENTID = "FD2G007E";
    const std::string topic_   = "/3dprinter/sys/dev/101/herwr";
    const uint64_t timeout_    = 10000L;
    std::string ip_addr_;
    int port_;
    IPStack ipstack_;

    std::shared_ptr<MQTT::Client<IPStack, Countdown>> mqtt_client_;

    Dreame3dMqtt(std::string addr = "************", int port = 1883);

    /**
     * @brief 初始化接口
     */
    void Init();

    /**
     * @brief 连接MQTT服务器
     * @return true 连接成功
     * @return false 连接失败
     */
    bool MqttConnect();

    /**
     * @brief 获取client id
     * @return std::string
     */
    std::string GetClientId();

    /**
     * @brief 话题消息分发回调
     * @param md 
     */
    static void messageArrived(MQTT::MessageData &md);

public:
    static Dreame3dMqtt& GetInstance() {
        static Dreame3dMqtt instance;
        return instance;
    }
    ~Dreame3dMqtt();

    /**
     * @brief 消息发布接口
     * @param topicName 话题名
     * @param payload 内容
     * @param len 长度
     * @param qos QOS等级
     */
    void PublishMsg(const char* topicName, const uint8_t* payload, const int len, const MQTT::QoS qos = MQTT::QoS::QOS1);

    /**
     * @brief 回调注册
     * @param handler
     */
    void AddCallback(std::string topic, MQTT::QoS qos, std::function<void(MQTT::MessageData &)> handler);

    /**
     * @brief 移除订阅话题
     * @param topic 
     */
    void DelTopic(std::string topic);
};
```

### 4.2 MQTT消息处理模块
提供消息处理的抽象模板，实现MQTT订阅、发布等协议操作抽象，支持QoS 0、1和2的消息传输。当产生新的业务功能时，只需实现消息处理模块的接口，即可完成新功能的开发。

```c++
/**
 * 订阅接口抽象
 * */
class DremeSub {
private:
    const std::string topic_;
    /**
     * @brief 话题回调
     * @param md 
     */
    virtual void SubMsgArrived(MQTT::MessageData &md) = 0;

public:
    DremeSub(std::string topic);
    virtual ~DremeSub();

    virtual void Init() {
        Dreame3dMqtt::GetInstance().AddCallback(topic_.c_str(), MQTT::QOS2, std::bind(&DremeSub::SubMsgArrived, this, std::placeholders::_1));
    }
};
```

### 4.3 G-code模块
实现打印机的G-code文件下载功能，以及下载的G-code文件推送给打印进程。
```c++
class Gcode
{
private:
    std::string url_;
public:
    Gcode();
    ~Gcode();

    /**
     * @brief 下载云端G-code文件
     * @param url 远程地址
     * @return true 下载成功
     * @return false 下载失败
     */
    bool GcodeDownload(std::string& url);
};
```

### 4.4 推流模块
接收云端下发的推流指令，控制视频流的推送和停止。
```c++
class VideoStream
{
private:
    std::string url_;
public:
    VideoStream();
    ~VideoStream();

    /**
     * @brief 开启视频流
     * @param url rtmp地址
     * @return true 
     * @return false 
     */
    bool StartStream(std::string url);

    /**
     * @brief 停止推流
     * @return true 
     * @return false 
     */
    bool StopStream();
};
```

### 4.5 安全模块
实现TLS/SSL握手和数据加密。管理密钥和证书。

## 5. 订阅发布清单

注释：

PUB： 发布数据

SUB： 订阅数据

|   话题描述   |    话题名称 |   说明    |   数据类型    |   输入输出  |    频率(Hz)    |
|-------------| -------------- |-------------|-----------| ------------- | ------------- |
| 服务消息 | /3dprinter/${src}/dev/${modelid}/${deviceid}    | 接收服务器数据 | json-rpc | SUB | - |
| 打印机状态信息 | /3dprinter/dev/pub/${modelid}/${deviceid}/status    | 发布打印机状态 | json-rpc | PUB | 1 |
