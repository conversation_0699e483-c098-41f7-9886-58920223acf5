# 3d 打印iot接入程序
## 概述
3D打印机物联网接入程序是用于将3D打印服务与物联网应用程序集成在一起的解决方案。该解决方案允许开发人员使用API轻松构建物联网应用程序，而无需直接与3D打印平台交互。

概要设计文档 [MQTT Process](docs/MQTT_Process概要设计.md)

## 同步mqqt仓库
```bash
git submodule update --init --recursive
```

## 交叉编译
```bash
mkdir build
cd build
cmake -DCMAKE_TOOLCHAIN_FILE=cmake/build_for_rv1126.cmake ..
cmake -DCMAKE_TOOLCHAIN_FILE=cmake/build_for_rv1126.cmake -DCMAKE_BUILD_TYPE=Debug ..
```

## 运行
```bash
sudo service klipper restart/stop/start #控制服务的运行状态。
sudo service moonraker restart/stop/start
/usr/bin/python /opt/klipper/klippy/klippy.py /opt/klipper/printer.cfg -a /tmp/klippy_uds -l /tmp/klippy.log # 启动klipper服务
```

## 测试指令

客户端发送话题： /3dprinter/client/dev/P300/G2522
客户端订阅话题： /3dprinter/dev/sys/P300/G2522

### 客户端获取机器信息
发送示例
```json
{"id": 1, "method": "info", "params": {}}
```
返回示例
```json
{"id":1,"result":{"state":"ready","state_message":"Printer is ready","hostname":"nano","klipper_path":"/home/<USER>/klipper","python_path":"/usr/bin/python3","process_id":34912,"user_id":1000,"group_id":1000,"log_file":"/tmp/klippy.log","config_file":"/home/<USER>/klipper/config/printer-creality-cr6se-2021.cfg","software_version":"v0.12.0-358-gc88ee84be-dirty","cpu_info":"4 core ARMv8 Processor rev 1 (v8l)"}}
```

### 归位xyz
发送示例
```json
{"id": 2, "method": "gcode/script", "params": {"script": "G28 X Y Z"}}
```
返回示例
```json
{"id":2,"result":{}}
```
